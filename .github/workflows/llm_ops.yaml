name: LLMOps Service Python3 CI/CD

on:
  pull_request:
    types:
      - opened
      - synchronize
      - reopened
      - closed
    branches:
      - main
    paths:
      - src/nirvana/llmops/**

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

permissions:
  contents: read
  id-token: write

defaults:
  run:
    shell: bash

env:
  AWS_ACCOUNT_ID: ************
  TFTOOLS: terraform_7bca0ddf6c
  TFLOCKKEY: gha_llmops

jobs:
  check-trigger:
    runs-on: ubuntu-latest
    outputs:
      trigger: ${{ steps.set-trigger.outputs.trigger }}
    steps:
      - id: set-trigger
        run: |
          TRIGGER=false

          ACTION="${{ github.event.action }}"
          MERGED="${{ github.event.pull_request.merged || false }}"

          if [[ "$ACTION" != "closed" ]]; then
            TRIGGER=true
          elif [[ "$ACTION" == "closed" && "$MERGED" == "true" ]]; then
            TRIGGER=true
          fi

          echo "trigger=$TRIGGER" >> $GITHUB_OUTPUT
  build:
    needs: [check-trigger]
    if: |
      needs.check-trigger.outputs.trigger == 'true'
    uses: ./.github/workflows/llm_ops.build.yaml
    with:
      artifact-name: llmops
      aws-account-id: ************
      code-directory: src/nirvana/llmops

  lint:
    needs: [build]
    uses: ./.github/workflows/llm_ops.lint.yaml
    with:
      artifact-name: llmops

  push:
    if: github.event.pull_request.merged
    needs: [build]
    uses: ./.github/workflows/llm_ops.push.yaml
    with:
      artifact-name: llmops
      aws-account-id: ************

  deploy:
    needs: [push]
    uses: ./.github/workflows/llm_ops.deploy.yaml
    with:
      aws-account-id: ************
      url: ${{ needs.push.outputs.url }}

  tf-state:
    runs-on: ubuntu-latest
    needs: [deploy]
    steps:
      - name: Checkout code
        uses: actions/checkout@v5
        with:
          sparse-checkout: |
            .github
            src/deployment
            src/scripts

      - name: Configure Terraform environment
        uses: ./.github/actions/setup-tf-env
        with:
          account-id: ${{ env.AWS_ACCOUNT_ID }}
          tftools: ${{ env.TFTOOLS }}

      - name: Acquire remote lock
        id: lock
        run: $TFTOOLS lock ${TFLOCKKEY}:deploy_${{ github.run_id }}_${{ github.run_attempt }}

      - name: Check terraform changes on LLM Ops ECS Service
        working-directory: src/deployment/app
        run: |
          planfile=$(mktemp)

          ../../scripts/DONOT_RUN_MANUALLY_terraform plan \
            -target=module.llmops_server_fg_service.aws_ecs_service.service \
            -input=false -out=$planfile > /dev/null

          terraform show $planfile

          terraform apply -auto-approve \
            -target=module.llmops_server_fg_service.aws_ecs_service.service \
            -input=false $planfile |\
          grep -v "Refreshing state...\|Reading...\|Read complete after"

          rm $planfile

      - name: Release remote lock
        if: ${{ always() && steps.lock.conclusion == 'success' }}
        run: $TFTOOLS unlock ${TFLOCKKEY}:deploy_${{ github.run_id }}_${{ github.run_attempt }}

  check-on-service:
    runs-on: ubuntu-latest
    needs: [deploy]
    steps:
      - name: Checkout code
        uses: actions/checkout@v5
        with:
          sparse-checkout: |
            .github

      - name: Configure AWS Credentials
        uses: ./.github/actions/setup-aws-credentials
        with:
          aws-account-id: ${{ env.AWS_ACCOUNT_ID }}

      - name: Wait until service is ready
        run: |
          aws ecs wait services-stable --cluster default_app_cluster --services llmops-server-service

      - name: Send Slack notification
        if: ${{ success() }}
        uses: slackapi/slack-github-action@v2.1.0
        with:
          webhook: ${{ secrets.INSURED_ENG_NOTIFICATION_SLACK_WEBHOOK_URL }}
          webhook-type: webhook-trigger
          payload: |
            status: "✅ Success"
            app_name: "LLMOps Service"
            commit_hash: "${{ github.sha }}"
            branch_name: "${{ github.ref }}"

      - name: Send PagerDuty alert
        if: ${{ failure() }}
        shell: bash
        run: |
          curl --request 'POST' \
          --url 'https://events.pagerduty.com/v2/enqueue' \
          --header 'Content-Type: application/json' \
          --data '{
            "payload": {
                "summary": "❌ Failed to deploy LLMOps Service",
                "severity": "critical",
                "source": "github-actions"
            },
            "routing_key": ${{ secrets.PAGERDUTY_CLAIMS_ROUTING_KEY }},
            "event_action": "trigger"
          }'
