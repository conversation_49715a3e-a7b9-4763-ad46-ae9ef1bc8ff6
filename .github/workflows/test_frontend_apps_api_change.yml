name: Build REST & GraphQL API and build images

permissions: write-all

"on":
  merge_group:
    types: [checks_requested]
  pull_request:
    branches: [main]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  # JOB to run change detection in specs directory
  changes:
    runs-on: ubuntu-latest
    # Required permissions
    permissions:
      pull-requests: read
    # Set job outputs to values from filter step
    outputs:
      oapi: ${{ steps.filter.outputs.oapi }}
      graphql: ${{ steps.filter.outputs.graphql }}
    steps:
      # For pull requests it's not necessary to checkout the code
      - uses: dorny/paths-filter@v3
        id: filter
        with:
          filters: |
            oapi:
              - "src/nirvana/openapi-specs/**"
              - "src/nirvana/client/package.json"
              - "src/nirvana/client/yarn.lock"
              - "src/nirvana/client/turbo.json"
              - "src/nirvana/**/spec.yaml"
              - "src/nirvana/**/spec.yml"
              - ".github/workflows/test_frontend_apps_api_change.yml"
            graphql:
              - "src/nirvana/gqlschema/**"
              - "src/nirvana/gqlgen/**"
              - "src/nirvana/client/**/*.graphql"
              - "src/nirvana/client/package.json"
              - "src/nirvana/client/yarn.lock"
              - "src/nirvana/client/turbo.json"
              - ".github/workflows/test_frontend_apps_api_change.yml"

  # Job to generate openapi client on spec changes
  # Run only when there are changes in openapi specs
  generate_specs_and_build_apps:
    needs: changes
    if: ${{ github.event.pull_request.head.repo.full_name == github.repository && (needs.changes.outputs.oapi == 'true' || needs.changes.outputs.graphql == 'true') }}
    runs-on: linux-arm-8-cores
    steps:
      - uses: actions/checkout@v5

      - name: Install yarn packages
        uses: ./.github/actions/setup-frontend-apps-node

      - name: Build Apps
        env:
          TURBO_TOKEN: ${{ secrets.TURBOREPO_TOKEN }}
          TURBO_TEAM: ${{ vars.TURBOREPO_TEAM }}
          SENTRY_AUTH_TOKEN: ${{ secrets.QUOTING_SENTRY_AUTH_TOKEN }}
        run: cd src/nirvana/client && yarn build:production
      - name: Check if API / GraphQL types have been modified
        id: check-files
        run: |
          # Only consider files matching the two patterns
          OAPI_CHANGED_FILES=$(git diff --name-only | grep -E '^src/nirvana/client/packages/api/' || true)
          GQL_CHANGED_FILES=$(git diff --name-only | grep -E '^src/nirvana/client/.*/types/graphql-types\.tsx?$' || true)
          GQLGEN_CHANGED_FILES=$(git diff --name-only | grep -E '^src/nirvana/client/.*/types/gqlgen-types\.tsx?$' || true)
          ALL_CHANGED_FILES=$(echo -e "$OAPI_CHANGED_FILES\n$GQL_CHANGED_FILES\n$GQLGEN_CHANGED_FILES" | grep -v '^$' | sort | uniq)
          CHANGED_FILES_FULL_TEXT=$(git diff)
          if [ -n "$ALL_CHANGED_FILES" ]; then
            echo "changed_files<<EOF" >> $GITHUB_OUTPUT
            echo "$ALL_CHANGED_FILES" >> $GITHUB_OUTPUT
            echo "EOF" >> $GITHUB_OUTPUT
            echo "Files have been modified after running `yarn build:production`:"
            echo "$CHANGED_FILES_FULL_TEXT"
            echo "[api-change]"
            exit 1
          else
            echo "No relevant files were modified after running yarn `build:production`"
            echo "[api-change]"
          fi
      - name: Find Comment
        uses: peter-evans/find-comment@v3
        if: ${{ failure() && steps.check-files.outputs.changed_files != '' }}
        id: fc
        with:
          issue-number: ${{ github.event.pull_request.number }}
          comment-author: "github-actions[bot]"
          body-includes: "[api-change]"
      - name: Comment PR with error message
        if: ${{ failure() && steps.check-files.outputs.changed_files != '' }}
        uses: peter-evans/create-or-update-comment@v4
        with:
          comment-id: ${{ steps.fc.outputs.comment-id }}
          issue-number: ${{ github.event.pull_request.number }}
          body: |
            The following files have been modified after running yarn `build:production`:

            ${{ steps.check-files.outputs.changed_files }}
            
            Please run `yarn build:api-types` and commit the changes.
          edit-mode: replace