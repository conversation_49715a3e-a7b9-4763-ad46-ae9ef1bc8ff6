name: Re-deploy all ML models to prod

permissions:
  contents: read
  id-token: write

on: workflow_dispatch

defaults:
  run:
    shell: bash
    working-directory: src/ds

concurrency:
  group: "datascience"
  cancel-in-progress: false

jobs:
  model-deployment:
    name: Deploy ML models
    runs-on: ubuntu-latest
    env:
      ENV: prod

    steps:
      - name: Checkout
        uses: actions/checkout@v5

      - name: Setup terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: 1.7.5
          terraform_wrapper: false # ! Removing this will break our scripts which run jq on terraform output

      - name: Set up environment
        id: setup-environment
        uses: ./.github/actions/setup-ds-env

      - name: Configure AWS credentials
        uses: ./.github/actions/setup-aws-credentials

      - name: Copy model artifacts to S3
        run: |
          task python -- -m models.cicd upload ../infra/platform/cdktf.out/stacks/default-production-ml-models/models.json

      - name: Set git commit hash as image tag
        id: get-tag
        run: |
          echo "Image tag is $(git rev-parse --short HEAD)"
          echo "tag=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT

      - name: Print image tag to be used
        run: |
          echo "Image tag is ${{ steps.get-tag.outputs.tag }}"

      - name: Build datascience docker image
        id: build-image
        uses: ./.github/actions/push-python-image
        with:
          image_tag: ${{ steps.get-tag.outputs.tag }}
          inline_cache: ${{ github.ref_name == 'main' }}

      - name: Tag image as latest
        if: ${{ (github.ref == 'refs/heads/main') }}
        run: |
          docker tag ${{ steps.build-image.outputs.image }} ${{ steps.build-image.outputs.repo }}:latest
          docker push ${{ steps.build-image.outputs.repo }}:latest

      - name: Play & apply infrastructural changes
        working-directory: src/infra/platform/cdktf.out/stacks/default-production-ml-models
        run: |
          terraform init
          planfile=$(mktemp)
          terraform plan -var='imageTag=${{ steps.get-tag.outputs.tag }}' \
            -input=false -out=$planfile
          terraform show $planfile

          # TODO: add plan verification

          terraform apply -auto-approve -input=false $planfile
          rm $planfile
