name: Deploy data-science service(s) to production

permissions:
  contents: read
  id-token: write

on:
  workflow_dispatch:
    inputs:
      flowDeployment:
        description: Deploy flows
        required: true
        default: true
        type: boolean
      forceFlowDeployment:
        description: Force deploy all flows even if not required
        required: true
        default: true # TODO: set this to false when we have multiple production flows
        type: boolean
      boardsDeployment:
        description: Deploy boards app
        required: true
        default: true
        type: boolean
      grpcDeployment:
        description: Deploy gRPC service
        required: true
        default: true
        type: boolean
      demoBoardsDeployment:
        description: Deploy demo-boards app
        required: true
        default: false
        type: boolean

defaults:
  run:
    shell: bash
    working-directory: src/ds

concurrency:
  group: "datascience"
  cancel-in-progress: false

jobs:
  terraflow:
    name: Run Terraflow
    runs-on: ubuntu-latest
    env:
      ENV: prod
    steps:
      - name: Checkout
        uses: actions/checkout@v5

      - name: Print inputs
        run: |
          echo flowDeployment = ${{ inputs.flowDeployment }}
          echo forceFlowDeployment = ${{ inputs.forceFlowDeployment }}
          echo boardsDeployment = ${{ inputs.boardsDeployment }}
          echo gRPCDeployment = ${{ inputs.grpcDeployment }}
          echo demoBoardsDeployment = ${{ inputs.demoBoardsDeployment }}

      - name: Set up environment
        id: setup-environment
        uses: ./.github/actions/setup-ds-env

      - name: Configure AWS credentials
        uses: ./.github/actions/setup-aws-credentials

      - name: Fail if tf diff not clean
        if: ${{ (github.ref == 'refs/heads/main') && (inputs.flowDeployment == true) }}
        run: task -v terraflow -- diff

      - name: Fail if tf protogen not clean
        if: ${{ (github.ref == 'refs/heads/main') && (inputs.flowDeployment == true) }}
        run: |
          task -v terraflow -- protogen
          git diff --stat --exit-code

      - name: Force taint all flows
        if: ${{ (github.ref == 'refs/heads/main') && (inputs.forceFlowDeployment == true) }}
        run: task -v terraflow -- taint --no-interaction

      - name: Set git commit hash as image tag
        id: get-tag
        run: |
          echo "Image tag is $(git rev-parse --short HEAD)"
          echo "tag=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT

      - name: Print image tag to be used
        run: |
          echo "Image tag is ${{ steps.get-tag.outputs.tag }}"

      - name: Build datascience docker image
        id: build-image
        uses: ./.github/actions/push-python-image
        with:
          image_tag: ${{ steps.get-tag.outputs.tag }}
          inline_cache: true

      - name: Tag image as latest
        if: ${{ (github.ref == 'refs/heads/main') }}
        run: |
          docker tag ${{ steps.build-image.outputs.image }} ${{ steps.build-image.outputs.repo }}:latest
          docker push ${{ steps.build-image.outputs.repo }}:latest

      - name: Deploy Flows
        if: ${{ (github.ref == 'refs/heads/main') && ((inputs.flowDeployment == true) || (inputs.forceFlowDeployment == true)) }}
        env:
          # enforce metaflow_user to
          #   1. not require auth token every time,
          #   2. prevent normal users from deploying to prod
          METAFLOW_USER: github
          ENV: prod
        run: task -v terraflow -- deploy --no-interaction

      - name: Deploy boards app to ECS
        if: inputs.boardsDeployment == true
        run: |
          docker tag ${{ steps.build-image.outputs.image }} ${{ steps.build-image.outputs.repo }}:boards-prod
          docker push ${{ steps.build-image.outputs.repo }}:boards-prod
          aws ecs update-service --cluster default_internal_tools --service boards-server-service --force-new-deployment

      - name: Deploy gRPC service to ECS
        if: inputs.grpcDeployment == true
        run: |
          docker tag ${{ steps.build-image.outputs.image }} ${{ steps.build-image.outputs.repo }}:grpc-prod
          docker push ${{ steps.build-image.outputs.repo }}:grpc-prod
          aws ecs update-service --cluster default_internal_tools --service metaflow-grpc --force-new-deployment

      - name: Deploy demo boards app to ECS
        if: inputs.demoBoardsDeployment == true
        run: |
          docker tag ${{ steps.build-image.outputs.image }} ${{ steps.build-image.outputs.repo }}:boards-demo
          docker push ${{ steps.build-image.outputs.repo }}:boards-demo
          aws ecs update-service --cluster default_internal_tools --service demo-boards-server-service --force-new-deployment
