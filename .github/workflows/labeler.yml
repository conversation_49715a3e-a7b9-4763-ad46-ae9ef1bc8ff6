name: Label Pull Requests
# TODO: Merge this and bazel_diff_image_deps.yml in to one workflow

on:
  pull_request:
    branches: [ main ]

permissions: write-all

jobs:
  # TODO: Use check_changes here (similar to go_ci.yml)
  check_changes:
    outputs:
      migration_change: ${{ steps.check_files.outputs.migration_change || false }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v5
        with:
          fetch-depth: 2
      - name: check modified files
        id: check_files
        run: |
         chmod +x ./.github/scripts/check-diff.sh
         ./.github/scripts/check-diff.sh 
        shell: bash
  comment:
    runs-on: ubuntu-latest
    needs: check_changes
    steps:
      - uses: actions/github-script@v7
        with:
          script: |

            let migrationLabel = 'has_db_migrations'
            let isMigrationLabelPresent =  ${{contains(github.event.pull_request.labels.*.name, 'has_db_migrations')}}
            let isMigrationChangesPresent = ${{needs.check_changes.outputs.migration_change}}
            console.log(isMigrationLabelPresent,isMigrationChangesPresent)
            // If migration changes are present and label is missing
            // then add a label and comment
            if (isMigrationChangesPresent && !isMigrationLabelPresent){
              github.rest.issues.addLabels({
                  issue_number: context.issue.number,
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  labels: [migrationLabel]
              });
              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: `Since this Pull Request contains db migrations make sure to go over this [guide](https://coda.io/d/Engineering-Wiki_dmBWLoxkuyN/Database-Migrations-Guide-2-0_suJ5F#_lu7CL) and [checklist](https://coda.io/d/Engineering-Wiki_dmBWLoxkuyN/WIP-Database-Migration-Practices_sujS0) before merging.`
              })
             ;}
            // If migration changes are not  present and label is added
            // then remove the label
            else if (!isMigrationChangesPresent && isMigrationLabelPresent){
              github.rest.issues.deleteLabel({
                  issue_number: context.issue.number,
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  name: migrationLabel,
              })
            ;}
