name: Redeploy application service(s)

permissions:
  contents: read
  id-token: write

on:
  workflow_dispatch:
    inputs:
      service:
        type: string
        description: JSON array of service names to deploy
      account_id:
        type: string
        description: AWS account ID to use. Defaults to production account (************).
        default: ************

defaults:
  run:
    shell: bash
    working-directory: src

env:
  # Note: to release a new version run task release-executable:tftools
  TFTOOLS: terraform_7bca0ddf6c

concurrency:
  group: "terraform-singleton"
  cancel-in-progress: false

jobs:
  acquire-lock:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5

      - name: Fail if job_processor is deployed using this action
        if: ${{ contains(fromJSON(inputs.service), 'job_processor') || contains(fromJSON(inputs.service), 'quoting_job_processor') || contains(fromJSON(inputs.service), 'safety_job_processor') || contains(fromJSON(inputs.service), 'event_job_processor') }}
        run: |
          echo "Deploying job_processor other than data_infra is not supported via this action. Please use the redeploy_jobber.yml action"
          exit 1

      - name: Setup Terraform environment
        uses: ./.github/actions/setup-tf-env
        with:
          tftools: $TFTOOLS
          account_id: ${{ inputs.account_id }}

      - name: Acquire remote lock
        id: lock
        run: $TFTOOLS lock gha:deploy_${{ github.run_id }}_${{ github.run_attempt }}

      - name: Eagerly check terraform plan and fail early
        working-directory: src/deployment/app
        run: |
          planfile=$(mktemp)

          ../../scripts/DONOT_RUN_MANUALLY_terraform plan \
            -input=false -out=$planfile > /dev/null
          terraform show $planfile

          $TFTOOLS verify ecs-only <(terraform show -json $planfile) null
          rm $planfile

      - name: Get migration info
        id: migration-info
        uses: ./.github/actions/migrations

      - name: Fail if migration incompatible with service deployment
        run: |
          current_nirvana=${{ steps.migration-info.outputs.current_nirvana }}
          production_nirvana=${{ steps.migration-info.outputs.production_nirvana }}
          current_fmcsa=${{ steps.migration-info.outputs.current_fmcsa }}
          production_fmcsa=${{ steps.migration-info.outputs.production_fmcsa }}

          echo "Source versions: nirvana=$current_nirvana fmcsa=$current_fmcsa"
          echo "Production versions: nirvana=$production_nirvana fmcsa=$production_fmcsa"

          # This check can be relaxed, but for now here we are:
          [[ $current_nirvana -eq $production_nirvana && $current_fmcsa -eq $production_fmcsa ]]

      - name: Release remote lock if this action fails
        if: ${{ always() && steps.lock.conclusion == 'success' && job.status != 'success' }}
        run: $TFTOOLS unlock gha:deploy_${{ github.run_id }}_${{ github.run_attempt }}

  push-images:
    needs: acquire-lock
    uses: ./.github/workflows/reusable_push_images.yaml
    with:
      service: ${{ inputs.service }}
      account_id: ${{ inputs.account_id }}

  release-lock-on-failure:
    needs: [acquire-lock, push-images]
    if: ${{ always() && needs.push-images.result != 'success' }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5

      - name: Setup Terraform environment
        uses: ./.github/actions/setup-tf-env
        with:
          tftools: $TFTOOLS
          account_id: ${{ inputs.account_id }}

      - name: Release remote lock
        run: $TFTOOLS unlock gha:deploy_${{ github.run_id }}_${{ github.run_attempt }}

  deploy:
    needs: [acquire-lock, push-images]
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v5

      - name: Setup Terraform environment
        uses: ./.github/actions/setup-tf-env
        with:
          tftools: $TFTOOLS
          account_id: ${{ inputs.account_id }}

      ### CHECK PRECONDITIONS
      - name: Ensure that we're still holding the remote lock
        id: lock
        run: $TFTOOLS lock gha:deploy_${{ github.run_id }}_${{ github.run_attempt }}

      - name: Print image tag to be used
        run: |
          echo "Image tag is ${{ needs.push-images.outputs.image_tag }}"

      - name: Write image tags to vars.tfvars
        working-directory: src/deployment/app
        run: |
          for service in $(echo '${{ inputs.service }}' | jq -r '.[]'); do
            echo "Writing tag for $service"
            echo "${service}_tag = \"${{ needs.push-images.outputs.image_tag }}\"" >> vars.tfvars
          done
      - name: Print vars.tfvars
        working-directory: src/deployment/app
        run: |
          cat vars.tfvars

      #### APPLY CHANGES AND GOOD BYE

      - name: Plan & Apply infrastructure changes
        working-directory: src/deployment/app
        run: |
          planfile=$(mktemp)

          ../../scripts/DONOT_RUN_MANUALLY_terraform plan \
            -var-file=vars.tfvars \
            -input=false -out=$planfile \
            > /dev/null
          terraform show $planfile

          $TFTOOLS verify ecs-only <(terraform show -json $planfile) ${{ needs.push-images.outputs.image_tag }}

          terraform apply -auto-approve -input=false $planfile |\
            grep -v "Refreshing state...\|Reading...\|Read complete after"
          rm $planfile

      - name: Release remote lock
        if: ${{ always()  && steps.lock.conclusion == 'success' }}
        run: $TFTOOLS unlock gha:deploy_${{ github.run_id }}_${{ github.run_attempt }}
