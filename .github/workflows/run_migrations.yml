name: Run database migration(s)

permissions:
  contents: read
  id-token: write

on:
  workflow_dispatch:
    inputs:
      force_deploy:
        type: boolean
        description: Force deploy lambda

defaults:
  run:
    shell: bash
    working-directory: src
env:
  TFTOOLS: terraform_7bca0ddf6c
  LAMBDA_ARN: arn:aws:lambda:us-east-2:************:function:db_migrate_lambda
  AWS_ACCOUNT_ID: ************

concurrency:
  group: "terraform-singleton"
  cancel-in-progress: false

jobs:
  migrate:
    runs-on:
      group: "Default Larger Runners"
      labels: "ubuntu-latest-16-cores-new"
    if: ${{ github.ref == 'refs/heads/main' }}
    steps:
      - uses: actions/checkout@v5

      - name: Configure AWS Credentials
        uses: ./.github/actions/setup-aws-credentials

      - name: Setup terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: 1.7.5
          terraform_wrapper: false # ! Removing this will break our scripts which run jq on terraform output

      - name: Setup tftools
        run: |
          aws s3 cp s3://cloud.nirvanatech.com/tools/"$TFTOOLS" $HOME/.local/bin/"$TFTOOLS"
          chmod +x $HOME/.local/bin/"$TFTOOLS"

      - name: Acquire remote lock
        id: lock
        run: $TFTOOLS lock gha_migrate_${{ github.run_id }}_${{ github.run_attempt }}

      - name: Get migration info
        id: migration-info
        uses: ./.github/actions/migrations

      - name: Check if migration needed for Nirvana DB
        id: check-nirvana-migration
        run: |
          current_nirvana=${{ steps.migration-info.outputs.current_nirvana }}
          production_nirvana=${{ steps.migration-info.outputs.production_nirvana }}

          [[ $current_nirvana -ge $production_nirvana ]] || { echo "mismatch"; exit 1; }
          if [[ $current_nirvana -eq $production_nirvana ]]
          then
            echo "needed=false" >> $GITHUB_OUTPUT
          else
            echo "needed=true" >> $GITHUB_OUTPUT
          fi

      - name: Check if migration needed for fmcsa DB
        id: check-fmcsa-migration
        run: |
          current_fmcsa=${{ steps.migration-info.outputs.current_fmcsa }}
          production_fmcsa=${{ steps.migration-info.outputs.production_fmcsa }}

          [[ $current_fmcsa -ge $production_fmcsa ]] || { echo "mismatch"; exit 1; }
          if [[ $current_fmcsa -eq $production_fmcsa ]]
          then
            echo "needed=false" >> $GITHUB_OUTPUT
          else
            echo "needed=true" >> $GITHUB_OUTPUT
          fi

      - name: Check if re-deployment necessary
        id: check-redeployment
        run: |
          needed=${{ steps.check-nirvana-migration.outputs.needed == 'true' || steps.check-fmcsa-migration.outputs.needed == 'true' || inputs.force_deploy == true }}
          echo "needed=$needed" >> $GITHUB_OUTPUT

      - name: Mount bazel cache
        if: ${{ steps.check-redeployment.outputs.needed == 'true' }}
        id: mount-bazel-cache
        uses: ./.github/actions/mount-cache
        with:
          prefix: bazel
          skipSave: "true"
          path: |
            ~/.cache/bazel
            ~/.cache/bazelisk

      - name: Set up Bazel using version from src/.bazelversion
        if: ${{ steps.check-redeployment.outputs.needed == 'true' }}
        uses: bazelbuild/setup-bazelisk@v3

      - name: Install docker-credential-ecr-login
        if: ${{ steps.check-redeployment.outputs.needed == 'true' }}
        run: go install github.com/awslabs/amazon-ecr-credential-helper/ecr-login/cli/docker-credential-ecr-login@latest

      - name: Create docker config
        if: ${{ steps.check-redeployment.outputs.needed == 'true' }}
        shell: bash
        run: |
          mkdir -p ~/.docker
          cat <<EOT > ~/.docker/config.json
          {
              "credHelpers": {
                  "${{ env.AWS_ACCOUNT_ID }}.dkr.ecr.us-east-2.amazonaws.com": "ecr-login"
              }
          }
          EOT

      - name: Set git commit hash as image tag
        if: ${{ steps.check-redeployment.outputs.needed == 'true' }}
        id: get-tag
        run: |
          echo "Image tag is $(git rev-parse --short HEAD)"
          echo "tag=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT

      - name: Re-deploy migration lambda
        if: ${{ steps.check-redeployment.outputs.needed == 'true' }}
        timeout-minutes: 10
        run: |
          # push image
          bazel run --platforms=@io_bazel_rules_go//go/toolchain:linux_amd64 \
          --@io_bazel_rules_go//go/config:pure \
          //nirvana/images/db_migrate_lambda:push_image \
          --define=image_tag=${{ steps.get-tag.outputs.tag }} \
          --define=repository=db_migrate_lambda \
          --define=account_id=${{ env.AWS_ACCOUNT_ID }}

          # re-deploy lambda
          aws lambda update-function-code \
            --function-name $LAMBDA_ARN \
            --image-uri "${{ env.AWS_ACCOUNT_ID }}.dkr.ecr.us-east-2.amazonaws.com/db_migrate_lambda:${{ steps.get-tag.outputs.tag }}"

          # wait for lambda to become active again
          state="unknown"
          while [[ $state != "Successful" ]]
          do
            state=$(
              aws lambda get-function --function-name $LAMBDA_ARN |\
              jq -r '.Configuration.LastUpdateStatus'
            )
            echo "Latest state = $state"
          done

      - name: Run migrations on Nirvana DB
        id: run-nirvana
        if: ${{ steps.check-nirvana-migration.outputs.needed == 'true' }}
        run: |
          tempfile=$(mktemp)

          aws lambda invoke \
            --function-name $LAMBDA_ARN --cli-binary-format raw-in-base64-out \
            --payload '{ "action": "migrate", "dbname": "nirvana" }' \
            $tempfile
          cat $tempfile && echo

          current_nirvana=${{ steps.migration-info.outputs.current_nirvana }}
          production_nirvana=$(jq -e -r .current_version $tempfile)
          [[ $current_nirvana -eq $production_nirvana ]] || {
            echo "our=$current_nirvana != their=$production_nirvana"
            exit 1
          }

      - name: Run migrations on FMCSA DB
        id: run-fmcsa
        if: ${{ steps.check-fmcsa-migration.outputs.needed == 'true' }}
        run: |
          tempfile=$(mktemp)

          aws lambda invoke \
            --function-name $LAMBDA_ARN --cli-binary-format raw-in-base64-out \
            --payload '{ "action": "migrate", "dbname": "fmcsa" }' \
            $tempfile
          cat $tempfile && echo

          current_fmcsa=${{ steps.migration-info.outputs.current_fmcsa }}
          production_fmcsa=$(jq -e -r .current_version $tempfile)
          [[ $current_fmcsa -eq $production_fmcsa ]] || {
            echo "our=$current_fmcsa != their=$production_fmcsa"
            exit 1
          }

      - name: Send pagerduty event on failure
        if: ${{ failure() && (steps.run-nirvana.conclusion == 'failure' || steps.run-fmcsa.conclusion == 'failure') }}
        uses: Entle/action-pagerduty-alert@1.0.2
        with:
          pagerduty-integration-key: "${{ secrets.PAGERDUTY_INFRA_INTEGRATION_KEY }}"
          pagerduty-dedup-key: ${{ github.run_id }}

      - name: Release remote lock
        if: ${{ always()  && steps.lock.conclusion == 'success' }}
        run: $TFTOOLS unlock gha_migrate_${{ github.run_id }}_${{ github.run_attempt }}
