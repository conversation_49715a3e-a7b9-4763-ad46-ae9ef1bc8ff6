name: Block DB Migrations

on:
  pull_request:
    branches: [ main ]

permissions: write-all

jobs:
  check_changes:
    outputs:
      migration_change: ${{ steps.check_files.outputs.migration_change || false }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v5
        with:
          fetch-depth: 2
      - name: check modified files
        id: check_files
        run: |
         chmod +x ./.github/scripts/check-diff.sh
         ./.github/scripts/check-diff.sh 
        shell: bash
  block_if_applicable:
    runs-on: ubuntu-latest
    needs: check_changes
    steps:
      - uses: actions/github-script@v7
        with:
          script: |

            let isMigrationChangesPresent = ${{needs.check_changes.outputs.migration_change}}
            console.log("isMigrationChangesPresent", isMigrationChangesPresent)
            if (isMigrationChangesPresent) {
                core.setFailed(`Database migrations are currently blocked due to ongoing maintenance. Please try again later.`);
            }
