name: Go CI

permissions: read-all

env:
  GOPROXY: "http://athens.ci.nirvana.internal:3000" #This adds a self hosted GO Module Proxy using the Athens Project. https://docs.gomods.io/index.html

on:
  workflow_dispatch:
    inputs:
      concurrencyKeyCustomSuffix:
        description: |
          A custom suffix to append to the concurrency key. Useful for re-running
          a workflow with a different concurrency key to avoid cancellations. If
          not provided, `github.sha` will be used.
        required: false
        type: string
  merge_group:
    types: [ checks_requested ]
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  schedule:
    - cron: '5 0 * * *' # Every day at 00:05 AM

defaults:
  run:
    shell: bash
    working-directory: src/nirvana


concurrency:
  group: >-
    ${{ github.workflow }}-${{ github.event_name }}-
    ${{ github.event.pull_request.number || inputs.concurrencyKeyCustomSuffix || github.sha }}
  cancel-in-progress: true

jobs:
  # JOB to run change detection
  changes:
    runs-on: ubuntu-latest
    # Set job outputs to values from filter step
    outputs:
      go: ${{ steps.filter.outputs.go }}
      oapi: ${{ steps.filter.outputs.oapi }}
      copyist: ${{ steps.filter.outputs.copyist }}
      infra-config: ${{ steps.filter.outputs.infra-config }}
    steps:
    - uses: actions/checkout@v5
    - uses: dorny/paths-filter@v3
      id: filter
      with:
        filters: |
          infra-config:
            - 'src/nirvana/infra/**/*.yaml'
            - 'src/nirvana/infra/**/*.yml'
            - '**/config.proto'
          go:
            - 'src/nirvana/!(client)/**'
            - 'src/proto/**'
            - '**.bazel'
            - 'src/WORKSPACE'
          oapi:
            - 'src/nirvana/openapi-specs/**/**.yaml'
          copyist:
            - 'src/nirvana/rating/**/**.copyist'
          uw_scheduler:
            - 'src/nirvana/common-go/application-util/underwriter_details.json'

  # We don't have seperate jobs for build and test because jobs don't share
  # artifacts by default.
  go_build_and_test:
    env:
      SHOULD_RUN_GO_STEPS: ${{ needs.changes.outputs.go == 'true' || github.ref_name == 'main' }}
    needs: [changes]
    if: github.ref_name == 'main' || needs.changes.outputs.go == 'true' || needs.changes.outputs.copyist == 'true' || needs.changes.outputs.uw_scheduler == 'true'
    runs-on:
      - runs-on # https://runs-on.com/configuration/job-labels/#available-labels
      - family=m7gd.12xlarge+m8g.12xlarge+m8g.16xlarge+c8g.24xlarge # Determined based on spot failure rate, price and memory
      - image=ubuntu22-full-arm64
      - spot=${{ !startsWith(github.head_ref || github.ref_name, 'nmq-') && 'capacity-optimized' }} # disable spot instance for nmq's speculative checks
      - run-id=${{ github.run_id }}
    timeout-minutes: 30
    permissions:
      contents: read
      id-token: write

    steps:

    - uses: actions/checkout@v5
      with:
        fetch-depth: 15
        # By default, github checks out merge commit instead of PR head
        # See: https://github.com/actions/checkout/issues/426
        ref: ${{ ( github.event_name == 'pull_request' && github.event.pull_request.head.sha ) || github.sha }}

    - name: Configure AWS Credentials
      uses: ./.github/actions/setup-aws-credentials

    # We use ramdisk to avoid writing to the disk, thereby speeding up I/O operations.
    # As there is no direct way to change the bazel cache location, this step creates a symlink to utilize
    # ramdisk for the bazel cache.
    - name: Setup cache directory
      # Dynamic ramdisk sizing
      run: |
        echo "=== Initial Memory Status (for Ramdisk Sizing) ==="
        TOTAL_MEM_KB_RAW=$(grep MemTotal /proc/meminfo | awk '{print $2}')
        TOTAL_MEM_GB=1 # Default to 1GB if parsing fails
        if [[ "$TOTAL_MEM_KB_RAW" =~ ^[0-9]+$ ]] && [ -n "$TOTAL_MEM_KB_RAW" ] && [ "$TOTAL_MEM_KB_RAW" -gt 0 ]; then
          TOTAL_MEM_GB=$((TOTAL_MEM_KB_RAW / 1024 / 1024))
          if [ "$TOTAL_MEM_GB" -lt 1 ]; then TOTAL_MEM_GB=1; fi # Ensure at least 1GB
        else
          echo "Warning: Could not reliably determine total memory. Using default for calculation: ${TOTAL_MEM_GB}GB"
        fi
        echo "Total System Memory: ${TOTAL_MEM_GB}GB"

        TARGET_RAMDISK_PERCENTAGE=75
        CONFIGURED_MIN_RAMDISK_GB=135
        CONFIGURED_MAX_RAMDISK_GB=180
        OS_SAFETY_CAP_PERCENTAGE=90
        FALLBACK_RAMDISK_GB=1

        RAMDISK_SIZE=$(((TOTAL_MEM_GB * TARGET_RAMDISK_PERCENTAGE) / 100))
        MAX_ABSOLUTE_SAFE_RAMDISK_GB=$(((TOTAL_MEM_GB * OS_SAFETY_CAP_PERCENTAGE) / 100))
        if [ "$MAX_ABSOLUTE_SAFE_RAMDISK_GB" -lt "$FALLBACK_RAMDISK_GB" ]; then MAX_ABSOLUTE_SAFE_RAMDISK_GB=$FALLBACK_RAMDISK_GB; fi

        if [ "$RAMDISK_SIZE" -lt "$CONFIGURED_MIN_RAMDISK_GB" ]; then
          if [ "$CONFIGURED_MIN_RAMDISK_GB" -le "$MAX_ABSOLUTE_SAFE_RAMDISK_GB" ]; then
            RAMDISK_SIZE=$CONFIGURED_MIN_RAMDISK_GB
          fi
        fi
        if [ "$RAMDISK_SIZE" -gt "$CONFIGURED_MAX_RAMDISK_GB" ]; then
          RAMDISK_SIZE=$CONFIGURED_MAX_RAMDISK_GB
        fi
        if [ "$RAMDISK_SIZE" -gt "$MAX_ABSOLUTE_SAFE_RAMDISK_GB" ]; then
          RAMDISK_SIZE=$MAX_ABSOLUTE_SAFE_RAMDISK_GB
        fi
        if [ "$RAMDISK_SIZE" -le 0 ]; then
          RAMDISK_SIZE=$FALLBACK_RAMDISK_GB
        fi

        echo "Final Determined Ramdisk Size to be mounted: ${RAMDISK_SIZE}GB"
        echo "Instance Type: $(curl -s --connect-timeout 5 --max-time 10 http://***************/latest/meta-data/instance-type 2>/dev/null || echo 'Unknown')"
        
        sudo mkdir -p /mnt/ramdisk
        sudo mount -t tmpfs -o size=${RAMDISK_SIZE}G tmpfs /mnt/ramdisk || { echo "Mount failed"; exit 1; }
        sudo chmod 777 /mnt/ramdisk
        export BAZEL_DISK_CACHE=/mnt/ramdisk/bazel
        export BAZEL_DEFAULT_CACHE=$HOME/.cache/bazel
        echo "BAZEL_DISK_CACHE=/mnt/ramdisk/bazel" >> $GITHUB_ENV
        echo "BAZEL_DEFAULT_CACHE=$HOME/.cache/bazel" >> $GITHUB_ENV
        
        mkdir -p "$BAZEL_DISK_CACHE" || { echo "Failed to create ramdisk cache dir"; exit 1; }
        mkdir -p ~/.cache || { echo "Failed to create home cache dir"; exit 1; }
        
        rm -f "$BAZEL_DEFAULT_CACHE"
        ln -s "$BAZEL_DISK_CACHE" "$BAZEL_DEFAULT_CACHE" || { echo "Failed to create symlink"; exit 1; }
        
        if [ -L "$BAZEL_DEFAULT_CACHE" ] && [ -d "$BAZEL_DISK_CACHE" ]; then
          echo "Symlink created successfully: $BAZEL_DEFAULT_CACHE -> $BAZEL_DISK_CACHE"
        else
          echo "Symlink verification failed"; exit 1
        fi
        echo "Ramdisk mounted and Bazel cache configured. Current usage:"
        df -h /mnt/ramdisk
        free -h | head -n 2 # Show total and used/free memory

    - name: Set up Bazelisk
      uses: bazelbuild/setup-bazelisk@v3

    - name: Install go-task
      uses: arduino/setup-task@v2
      with:
        version: 3.43.2
        repo-token: ${{ secrets.GITHUB_TOKEN }}

    - name: Append images.bazelignore to .bazelignore
      shell: bash
      run: cat ../images.bazelignore >> ../.bazelignore

    # FIX: Disabling CI flag forces the use of ramdisk based psql container instead
    # of service container.
    - name: Disable CI flag in GITHUB_ENV
      run: |
        echo "CI=false" >> $GITHUB_ENV

    - name: Run bazel build
      if: env.SHOULD_RUN_GO_STEPS == 'true'
      run: |
        bazel build //...

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2

    - name: Migrate postgres, neo4j, redis, and dynamodb containers
      env:
        PULL_THROUGH_CACHE_BASE: ${{ steps.login-ecr.outputs.registry }}/public-docker-hub
      id: start-containers
      run: |
        task container:start

    - name: Run default bazel tests
      if: env.SHOULD_RUN_GO_STEPS == 'true'
      id: bazel-tests # This ID is used for logsdir output
      run: |
        echo "Setting up for Bazel tests and capturing test logs directory..."
        echo "logsdir=$(bazel info bazel-testlogs)" >> $GITHUB_OUTPUT
        bazel query "tests(//...) except attr(tags, 'manual', //...)" | xargs bazel test --test_output=errors --experimental_ui_max_stdouterr_bytes=********

    - name: Run bazel tests requiring snowflake credentials
      if: env.SHOULD_RUN_GO_STEPS == 'true'
      id: bazel-snowflake-tests
      env:
        SNOWFLAKE_ADMIN_ACCOUNT_NAME: "ad91921.us-east-2.aws"
        SNOWFLAKE_ADMIN_USER_NAME: "abhaymitra"
        SNOWFLAKE_ADMIN_PASSWORD: ${{ secrets.SNOWFLAKE_ADMIN_PASSWORD }}
      run: |
        bazel query "attr("tags", "snowflake", kind(.*test, //... - //nirvana/api-server/...))" |\
          xargs bazel test --config=snowflake --test_output=errors --experimental_ui_max_stdouterr_bytes=********

    - name: Run bazel tests requiring aws credentials
      if: env.SHOULD_RUN_GO_STEPS == 'true'
      id: bazel-aws-tests
      run: |
        bazel query "attr("tags", "aws", kind(.*test, //...))" |\
          xargs bazel test --config=aws --test_output=errors --experimental_ui_max_stdouterr_bytes=********

    - name: (If test fails) Persist all bazel test logs
      if: ${{ failure() && !cancelled() && ( steps.bazel-tests.conclusion == 'failure' || steps.bazel-aws-tests.conclusion == 'failure' || steps.bazel-snowflake-tests.conclusion == 'failure' || steps.bazel-RateML-tests.conclusion == 'failure' || steps.bazel-RateML-merge-tests-Post-Merge == 'failure' ) }}
      uses: actions/upload-artifact@v4
      with:
        name: bazel-test-logs
        path: ${{ steps.bazel-tests.outputs.logsdir }}
        retention-days: 3

  go_generators:
    needs: [changes]
    if: |
      github.ref_name == 'main' ||
      needs.changes.outputs.go == 'true' ||
      needs.changes.outputs.oapi == 'true'
    runs-on: 
      - runs-on
      - family=m8g.4xlarge
      - image=ubuntu22-full-arm64
      - run-id=${{ github.run_id }}
    timeout-minutes: 30
    permissions:
      contents: read
      id-token: write

    steps:
    - uses: actions/checkout@v5
      with:
        fetch-depth: 15
        ref: ${{ github.sha }}

    - name: Configure AWS Credentials
      uses: ./.github/actions/setup-aws-credentials

    - name: System Resource Check
      run: |
        echo "=== Generator Job Resource Info ==="
        echo "Instance Type: $(curl -s --connect-timeout 5 --max-time 10 http://***************/latest/meta-data/instance-type 2>/dev/null || echo 'Unknown')"
        echo "vCPUs: $(nproc)"
        free -h
        df -h /

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: 1.24.1

    - name: Mount go cache
      id: mount-go-cache
      uses: ./.github/actions/mount-s3-cache
      with:
        prefix: go-arm
        # Only the cron job starts from a fresh slate, rest every CI run restores the
        # artifacts written by the cron job.
        skipRestore: ${{ github.event_name == 'schedule' }}
        # Only the cron job gets to write to the cache.
        skipSave: ${{ github.event_name != 'schedule' }}
        path: |
          ~/go

    - name: Store go build cache path
      id: go-build-cache-path
      shell: bash
      run: echo "build_cache_path=$(go env GOCACHE)" >> $GITHUB_OUTPUT

    - name: Set up Bazelisk
      uses: bazelbuild/setup-bazelisk@v3

    - name: Install go-task
      uses: arduino/setup-task@v2
      with:
        version: 3.43.2
        repo-token: ${{ secrets.GITHUB_TOKEN }}

    - name: Mount go build cache
      id: mount-go-build-cache
      uses: ./.github/actions/mount-s3-cache
      with:
        prefix: go-build-generators-arm
        # Only the cron job starts from a fresh slate, rest every CI run restores the
        # artifacts written by the cron job.
        skipRestore: ${{ github.event_name == 'schedule' }}
        # Only the cron job gets to write to the cache.
        skipSave: ${{ github.event_name != 'schedule' }}
        path: ${{ steps.go-build-cache-path.outputs.build_cache_path }}

    - name: Run go mod tidy
      shell: bash
      run: |
        go mod tidy
        git checkout HEAD go.sum
        cd ..

    # FIX: Disabling CI flag forces the use of ramdisk based psql container instead
    # of service container.
    - name: Update GITHUB_ENV with CI flag
      run: |
        echo "CI=false" >> $GITHUB_ENV

    - name: Run OpenAPI spec codegen
      if: needs.changes.output.oapi == 'true'
      shell: bash
      run: go generate ./openapi-specs/...

    - name: Run Telematics codegenerator(s)
      shell: bash
      run: go generate ./telematics/...

    - name: Validate Standard Jobber Scheduler State
      shell: bash
      run: |
        OUTPUT=$(git ls-tree -r origin/main -- jobber/standard_jobber/schedules/state.json)
        if [ -z "$OUTPUT" ]; then
          echo "Standard Jobber state file not found in main branch"
        else
          git show origin/main:src/nirvana/jobber/standard_jobber/schedules/state.json > temp_standard.json
          go run cmd/jobber/main.go generate_state standard --path=jobber/standard_jobber/schedules/state.json --previous-state=temp_standard.json --force
          rm temp_standard.json
          STANDARD_STATE=$(git status --porcelain=v1 jobber/standard_jobber/schedules/state.json)
          if [ -n "$STANDARD_STATE" ]; then
              git diff jobber/standard_jobber/schedules/state.json
              echo "Please regenerate state from latest main"
              exit 1
          fi
        fi

    - name: Validate Data-Infra Jobber Scheduler State
      shell: bash
      run: |
        OUTPUT=$(git ls-tree -r origin/main -- data_infra_jobber/schedules/state.json)
        if [ -z "$OUTPUT" ]; then
          echo "Data-Infra Jobber state file not found in main branch"
        else
          git show origin/main:src/nirvana/data_infra_jobber/schedules/state.json > temp_data_infra.json
          go run cmd/jobber/main.go generate_state data-infra --path=data_infra_jobber/schedules/state.json --previous-state=temp_data_infra.json --force
          rm temp_data_infra.json
          DATA_INFRA_STATE=$(git status --porcelain=v1 data_infra_jobber/schedules/state.json)
          if [ -n "$DATA_INFRA_STATE" ]; then
              git diff data_infra_jobber/schedules/state.json
              echo "Please regenerate state from latest main"
              exit 1
          fi
        fi

    - name: Validate Quoting Jobber Scheduler State
      shell: bash
      run: |
        OUTPUT=$(git ls-tree -r origin/main -- api-server/quoting_jobber/schedules/state.json)
        if [ -z "$OUTPUT" ]; then
          echo "Quoting Jobber state file not found in main branch"
        else
          git show origin/main:src/nirvana/api-server/quoting_jobber/schedules/state.json > temp_quoting.json
          go run cmd/jobber/main.go generate_state quoting --path=api-server/quoting_jobber/schedules/state.json --previous-state=temp_quoting.json --force
          rm temp_quoting.json
          QUOTING_STATE=$(git status --porcelain=v1 api-server/quoting_jobber/schedules/state.json)
          if [ -n "$QUOTING_STATE" ]; then
              git diff api-server/quoting_jobber/schedules/state.json
              echo "Please regenerate state from latest main"
              exit 1
          fi
        fi

    - name: Validate Safety Jobber Scheduler State
      shell: bash
      run: |
        OUTPUT=$(git ls-tree -r origin/main -- safety_jobber/schedules/state.json)
        if [ -z "$OUTPUT" ]; then
          echo "Safety Jobber state file not found in main branch"
        else
          git show origin/main:src/nirvana/safety_jobber/schedules/state.json > temp_safety.json
          go run cmd/jobber/main.go generate_state safety --path=safety_jobber/schedules/state.json --previous-state=temp_safety.json --force
          rm temp_safety.json
          SAFETY_STATE=$(git status --porcelain=v1 safety_jobber/schedules/state.json)
          if [ -n "$SAFETY_STATE" ]; then
              git diff safety_jobber/schedules/state.json
              echo "Please regenerate state from latest main"
              exit 1
          fi
        fi

    - name: Validate Event Jobber Scheduler State
      shell: bash
      run: |
        OUTPUT=$(git ls-tree -r origin/main -- jobber/event/schedules/state.json)
        if [ -z "$OUTPUT" ]; then
          echo "Event Jobber state file not found in main branch"
        else
          git show origin/main:src/nirvana/jobber/event/schedules/state.json > temp_event.json
          go run cmd/jobber/main.go generate_state event --path=jobber/event/schedules/state.json --previous-state=temp_event.json --force
          rm temp_event.json
          EVENT_STATE=$(git status --porcelain=v1 jobber/event/schedules/state.json)
          if [ -n "$EVENT_STATE" ]; then
              git diff jobber/event/schedules/state.json
              echo "Please regenerate state from latest main"
              exit 1
          fi
        fi
      # similarly add for other jobbers

    - name: Run task gazelle:update-repos
      run: task gazelle:update-repos

    - name: Run task gazelle:build
      run: task gazelle:build

    - name: Run task graphql:schema
      run: task graphql:schema

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2

    - name: Migrate postgres container
      id: start-containers
      env:
        PULL_THROUGH_CACHE_BASE: ${{ steps.login-ecr.outputs.registry }}/public-docker-hub
      run: |
        task container:start -- postgres

    - name: Run sqlboiler
      run: |
        bazel run '//nirvana/cmd/db' -- codegen
        task gazelle:build

    - name: Git status
      id: git-status
      run: |
        echo "num-lines=$(git status --porcelain=v1 | wc -l)" >> $GITHUB_OUTPUT
        git diff
      shell: bash

    - name: Fail if worktree is dirty
      if: ${{ steps.git-status.outputs.num-lines != '0' }}
      shell: bash
      run: |
        git status --porcelain=v1
        exit 1

  # This job is created so that we can parallelize certain tasks that
  # require fewer dependencies (e.g., do not need AWS, cache).
  go_generators_light:
    needs: [changes]
    if: |
      github.ref_name == 'main' ||
      needs.changes.outputs.go == 'true'
    runs-on:
      - runs-on
      - family=m8g.4xlarge
      - image=ubuntu22-full-arm64
      - run-id=${{ github.run_id }}
    timeout-minutes: 10
    permissions:
      contents: read
      id-token: write

    steps:
      - uses: actions/checkout@v5
        with:
          fetch-depth: 15
          ref: ${{ github.sha }}

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: 1.24.1

      - name: Set up Bazelisk
        uses: bazelbuild/setup-bazelisk@v3

      - name: Install go-task
        uses: arduino/setup-task@v2
        with:
          version: 3.43.2
          repo-token: ${{ secrets.GITHUB_TOKEN }}

      - name: Run task go:protogen
        run: task go:protogen

      - name: Run task go:enumer
        shell: bash
        run: task go:enumer

      - name: Run task go:mockgen
        shell: bash
        run: task go:mockgen

      - name: Git status
        id: git-status
        run: |
          echo "num-lines=$(git status --porcelain=v1 | wc -l)" >> $GITHUB_OUTPUT
          git diff
        shell: bash

      - name: Fail if worktree is dirty
        if: ${{ steps.git-status.outputs.num-lines != '0' }}
        shell: bash
        run: |
          git status --porcelain=v1
          exit 1

  go_lint:
    needs: [changes]
    if: |
      github.ref_name == 'main' ||
      needs.changes.outputs.go == 'true' ||
      needs.changes.outputs.oapi == 'true'
    runs-on: runs-on,runner=4cpu-linux-x64,run-id=${{ github.run_id }}
    timeout-minutes: 20
    steps:

    - uses: actions/checkout@v5
      with:
        fetch-depth: 100
        ref: ${{ github.event.pull_request.head.sha }}

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: 1.24.1

    - name: Mount lint cache
      id: mount-lint-cache
      uses: ./.github/actions/mount-s3-cache
      with:
        prefix: lint
        # Only the cron job starts from a fresh slate, rest every CI run restores the
        # artifacts written by the cron job.
        skipRestore: ${{ github.event_name == 'schedule' }}
        # Only the cron job gets to write to the cache.
        skipSave: ${{ github.event_name != 'schedule' }}
        path: |
          ~/.cache/golangci-lint
          ~/.cache/go-build

    - name: Mount go cache
      id: mount-go-cache
      uses: ./.github/actions/mount-s3-cache
      with:
        prefix: go
        skipSave: 'true'
        path: |
          ~/go

    - name: Run go mod tidy
      shell: bash
      run: |
        go mod tidy
        git checkout HEAD go.sum
        cd ..

    - name: golangci-lint
      uses: golangci/golangci-lint-action@v7
      with:
        # Optional: version of golangci-lint to use in form of v1.2 or v1.2.3 or `latest` to use the latest version
        version: v2.0.2

        # Optional: working directory, useful for monorepos
        working-directory: src/nirvana

        skip-cache: true

        # Optional: if set to true then the action don't cache or restore ~/go/pkg.
        # skip-pkg-cache: true

        # Optional: if set to true then the action don't cache or restore ~/.cache/go-build.
        # skip-build-cache: true

  # DB test job
  go_db_tests:
    needs: [changes]
    # Containers must run in Linux based operating systems
    runs-on: runs-on,runner=8cpu-linux-x64,run-id=${{ github.run_id }}
    if: github.ref_name == 'main' || needs.changes.outputs.go == 'true'
    timeout-minutes: 30
    permissions:
      contents: read
      id-token: write

    # Service containers to run with `container-job`
    services:
      # Label used to access the service container
      postgres:
        # Docker Hub image
        image: postgres
        # Provide the password for postgres
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: dontuse
        ports:
          - 5432:5432
        # Set health checks to wait until postgres has started
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      # Downloads a copy of the code in your repository before running CI tests
    - name: Check out repository code
      uses: actions/checkout@v5

    - name: Configure AWS Credentials
      uses: ./.github/actions/setup-aws-credentials

    - name: DB Test Job Resource Check
      env:
        POSTGRES_SERVICE_ID_FOR_LOGGING: ${{ job.services.postgres.id }}
      run: |
        echo "=== DB Test Job System Info ==="
        echo "Instance Type: $(curl -s --connect-timeout 5 --max-time 10 http://***************/latest/meta-data/instance-type 2>/dev/null || echo 'Unknown')"
        echo "vCPUs: $(nproc)"
        free -h | head -n 2
        df -h / | head -n 2
        echo "Available PostgreSQL service ID: $POSTGRES_SERVICE_ID_FOR_LOGGING"

    - name: Populate PostgreSQL
      # Runs a script that creates a PostgreSQL schema and populates it.
      run: bash ../scripts/setup_db_from_cloud.sh
      env:
        # The hostname used to communicate with the PostgreSQL service container
        POSTGRES_HOST: localhost
        # The default PostgreSQL port
        POSTGRES_PORT: 5432

    - name: Set up Bazel using version from src/.bazelversion
      uses: bazelbuild/setup-bazelisk@v3

    - name: Store external dependencies bazel path
      id: bazel-external
      shell: bash
      run: echo "deps_path=$(bazel info output_base)/external" >> $GITHUB_OUTPUT

    - name: Restore Bazel external dependencies cache
      id: restore-dependencies
      uses: ./.github/actions/mount-s3-cache
      with:
        prefix: bazel-db-test-deps
        path: ${{ steps.bazel-external.outputs.deps_path }}
        # Only the cron job starts from a fresh slate, rest every CI run restores the
        # artifacts written by the cron job.
        skipRestore: ${{ github.event_name == 'schedule' }}
        # Only the cron job gets to write to the cache.
        skipSave: ${{ github.event_name != 'schedule' }}

    - name: Mount go cache
      id: mount-go-cache
      uses: ./.github/actions/mount-s3-cache
      with:
        prefix: go
        skipSave: 'true'
        path: |
          ~/go

    - name: Create login script
      shell: bash
      run: |
        cat <<EOT > login_in_loop.bash
        #!/bin/bash
        STATUS_CODE=0
        while [ \$STATUS_CODE -ne 200 ];
        do STATUS_CODE=\$(curl -o /dev/null -s -w "%{http_code}\n" --location --request POST 'http://localhost:8080/auth/login' \
        --header 'Content-Type: application/json' \
        --data-raw '{
           "email": "<EMAIL>",
           "password": "lebowski"
        }')
        sleep 1
        done
        echo \$STATUS_CODE, Logged in successfully
        EOT

    - name: Test api_server
      shell: bash
      env:
        ENV: TEST
      run: |
        set -e
        set -x
        bazel run //nirvana/cmd/db -- migrate up nirvana --yes-to-all
        bazel run //nirvana/cmd/db -- migrate up fmcsa --yes-to-all
        bazel build //nirvana/api-server/cmd/api_server_local
        bazel run //nirvana/api-server/cmd/api_server_local >& ~/api_server_logs &
        timeout 60 bash login_in_loop.bash
        killall bazel

    - name: Final DB Test Resource Summary
      if: always()
      run: |
        echo "=== DB Test Final Resource Summary ==="
        free -h
        df -h /
        uptime
        echo "=== Top Memory Consumers ==="
        ps aux --sort=-%mem | head -5

    - name: Upload api_server_logs
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: api-server-log
        path: ~/api_server_logs
  
  config_change_pr_check:
    needs: [changes]
    if: |
      needs.changes.outputs.infra-config == 'true' &&
      github.event_name == 'pull_request'
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: write
    steps:
      - name: Find Comment
        uses: peter-evans/find-comment@v3
        id: find-comment
        with:
          issue-number: ${{ github.event.pull_request.number }}
          comment-author: "github-actions[bot]"
          body-includes: This PR contains changes to the config.yaml and/or config.proto files.

      - uses: peter-evans/create-or-update-comment@v4
        if: ${{ steps.find-comment.outputs.comment-id == '' }}
        with:
          issue-number: ${{ github.event.pull_request.number }}
          body: |
            This PR contains changes to the config.yaml and/or config.proto files. Please make sure you check on the following before deploying:
            1. Add tests to `loader_test.go` to confirm the environment variable(s) name that will override the config.yaml in production.
            2. Add environment variables on the services via Terraform. If it's a secret, ensure the ARN is added to the ECS IAM policy (`ecs.tf`) if needed.

  # This job detects runner shutdowns and emits a metric to Datadog. The metric is 
  # used to ignore test failures due to runner shutdowns in because jobs are
  # automatically retried on runner shutdown.
  check_runner_shutdowns:
    needs: [go_build_and_test, go_generators, go_generators_light, go_lint, go_db_tests]
    if: always()  # This ensures the job runs even if previous jobs fail
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v5
        with:
          sparse-checkout: |
            .github
      - name: Check for shutdowns and emit metrics
        uses: ./.github/actions/push-runner-shutdown-metric
        with:
          datadog-api-key: ${{ secrets.DATADOG_API_KEY }}
    
  
