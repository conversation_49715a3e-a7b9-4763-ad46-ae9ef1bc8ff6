name: Push Docker Image to ECR

on:
  workflow_call:
    inputs:
      artifact-name:
        required: true
        type: string
      aws-account-id:
        required: true
        type: string

    outputs:
      url:
        description: The URL of the pushed image
        value: ${{ jobs.run.outputs.url }}

env:
  ECR_REPO: llmops-service

jobs:
  run:
    runs-on: ubuntu-latest
    outputs:
      url: ${{ steps.push-image.outputs.url }}
    steps:
      - name: Download artifact
        uses: actions/download-artifact@v5
        with:
          name: ${{ inputs.artifact-name }}
          path: /tmp

      - name: Load image
        run: docker load --input /tmp/${{ inputs.artifact-name }}.tar

      - name: Checkout for .github folder
        uses: actions/checkout@v5
        with:
          sparse-checkout: .github

      - name: Configure AWS Credentials
        uses: ./.github/actions/setup-aws-credentials
        with:
          aws-account-id: ${{ inputs.aws-account-id }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Tag and push image
        id: push-image
        env:
          REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          REPOSITORY: ${{ env.ECR_REPO }}
          IMAGE_TAG: ${{ github.sha }}
        run: |
          docker tag ${{ inputs.artifact-name }} $REGISTRY/$REPOSITORY:$IMAGE_TAG
          docker push $REGISTRY/$REPOSITORY:$IMAGE_TAG
          echo "url=$REGISTRY/$REPOSITORY:$IMAGE_TAG" >> $GITHUB_OUTPUT
