name: Deploy ECS Task

on:
  workflow_call:
    inputs:
      aws-account-id:
        required: true
        type: string
      url:
        required: true
        type: string

jobs:
  run:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v5
        with:
          sparse-checkout: .github

      - name: Configure AWS Credentials
        uses: ./.github/actions/setup-aws-credentials
        with:
          aws-account-id: ${{ inputs.aws-account-id }}

      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v2

      - name: Download ECS task definition
        run: |
          aws ecs describe-task-definition --task-definition llmops-server-td --query taskDefinition \
            | jq 'del(
                .taskDefinitionArn,
                .requiresAttributes,
                .compatibilities,
                .revision,
                .status,
                .registeredAt,
                .registeredBy,
                .runtimePlatform
            )' > task-definition.json

      - name: Update ECS task definition
        id: update-td
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: task-definition.json
          container-name: llmops-server
          image: ${{ inputs.url }}

      - name: Deploy ECS service
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        with:
          task-definition: ${{ steps.update-td.outputs.task-definition }}
          service: llmops-server-service
          cluster: default_app_cluster
          wait-for-service-stability: false
