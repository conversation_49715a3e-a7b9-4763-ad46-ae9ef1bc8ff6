name: Frozen Lockfile

permissions:
  contents: read

on:
  merge_group:
    types: [checks_requested]
  pull_request:
    branches: [main]
    paths:
      - "src/nirvana/client/yarn.lock"
      - "src/nirvana/client/package.json"
      - "src/nirvana/client/**/package.json"

jobs:
  frozen-yarn-lockfile:
    name: Frozen Yarn Lockfile
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5

      - name: Install yarn packages
        uses: ./.github/actions/setup-frontend-apps-node

      - name: Check for yarn.lock diffs
        working-directory: src/nirvana/client
        run: git diff --exit-code yarn.lock
