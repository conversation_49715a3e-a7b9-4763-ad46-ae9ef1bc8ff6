name: Auto-generate CODEOWNERS and raise Pull Request

on:
  workflow_dispatch:
  schedule:
    # Every working day (monday - friday) at 00:10 AM UTC
    # GitHub docs mention that `schedule` events can be delayed during
    # periods of high loads (start of an an hour being one). If the load
    # is sufficiently high enough, some queued jobs may be dropped. They
    # recommend scheduling your workflow to run at a different time of the hour.
    # https://docs.github.com/en/actions/using-workflows/events-that-trigger-workflows#schedule
    - cron: '10 0 * * 1-5'

env:
  WORKFLOW_URL: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}

jobs:
  detect-and-commit:
    permissions: write-all
    runs-on: 'ubuntu-latest-4-cores'
    env:
      PUSHED_BRANCH_NAME: nirvana-codegen-bot/codeowners
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v5
        with:
          fetch-depth: 0

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: 1.24.1

      - name: Set up Bazelisk
        uses: bazelbuild/setup-bazelisk@v3

      - name: Install go-task
        uses: arduino/setup-task@v2
        with:
          version: 3.x
          repo-token: ${{ secrets.GITHUB_TOKEN }}

      - name: Checkout to branch or create if it doesn't exist on remote
        # We also update the branch with latest changes from main, if it exists.
        # The username & email must follow a specific convention for commits to properly
        # attributed to the bot. See also: 
        # https://github.com/orgs/community/discussions/24664#discussioncomment-3244950
        id: get-or-create-branch
        run: |
          git config user.name "nirvanacodegenbot[bot]"
          git config user.email "142149880+nirvanacodegenbot[bot]@users.noreply.github.com"
          if git ls-remote --heads origin $PUSHED_BRANCH_NAME | grep $PUSHED_BRANCH_NAME; then
            git checkout $PUSHED_BRANCH_NAME
            git merge main
          else
            git checkout -b $PUSHED_BRANCH_NAME
          fi

      - name: Execute the script
        working-directory: ./src/nirvana
        run: task git:codeowners

      - name: Check for Changes
        id: diff-check
        run: |
          git diff --name-only --exit-code .github/CODEOWNERS || echo "changes=yes" >> $GITHUB_OUTPUT

      # We use a custom GitHub App to generate a token with write access to the repo.
      # This is done to workaround GitHub's restriction to not allow workflow runs
      # for commits/PRs created by the default `GITHUB_TOKEN`
      # See: https://docs.github.com/en/actions/using-workflows/triggering-a-workflow#triggering-a-workflow-from-a-workflow
      - uses: tibdex/github-app-token@v2
        if: steps.diff-check.outputs.changes == 'yes'
        id: generate-token
        with:
          app_id: ${{ secrets.NIRVANA_CODEGEN_BOT_APP_ID }}
          private_key: ${{ secrets.NIRVANA_CODEGEN_BOT_APP_PRIVATE_KEY }}

      - name: Handle changes
        if: steps.diff-check.outputs.changes == 'yes'
        uses: ./.github/actions/push-and-raise-pr
        with:
          branch-name: ${{ env.PUSHED_BRANCH_NAME }}
          gh-token: ${{ steps.generate-token.outputs.token }}
          commit-message: |
            [Bot] Update .github/CODEOWNERS

            Generated by [this workflow](${{ env.WORKFLOW_URL }})
          skip-push-comment-body: |
            Detected new changes, but skipping push to branch since this PR is already approved.
            Workflow URL: ${{ env.WORKFLOW_URL }}
          pr-title: "[Bot] Update .github/CODEOWNERS"
          pr-body: |
            With this PR, we update the repo-level unified CODEOWNERS.

            This PR was created by the CODEOWNERS generation workflow.
          pr-labels: 'merge_ready'
