package client

import (
	"bytes"
	"context"
	"encoding/csv"
	"fmt"
	"net/url"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/jszwec/csvutil"
	"github.com/xuri/excelize/v2"

	"nirvanatech.com/nirvana/claims/reporting/enums"
	db "nirvanatech.com/nirvana/claims/reporting/internal/db/postgres/bordereaux_reports"
	file_enums "nirvanatech.com/nirvana/common-go/file_upload_lib/enums"
	"nirvanatech.com/nirvana/common-go/tracing"
	"nirvanatech.com/nirvana/common-go/uuid_utils"
	"nirvanatech.com/nirvana/infra/authz"
)

// GenerateBordereauxReport generates a bordereaux report for the given carrier, and returns the
// resulting report ID and a temporary download URL.
func (c *Client) GenerateBordereauxReport(
	ctx context.Context,
	carrier enums.Carrier,
) (uuid.UUID, *url.URL, error) {
	ctx, span := tracing.Start(ctx, "client.GenerateBordereauxReport")
	defer span.End()

	claimsRowsCSV, err := c.getBordereauxClaimsCSV(ctx, carrier)
	if err != nil {
		return uuid.Nil, nil, errors.Wrap(err, "failed to get bordereaux claims CSV")
	}

	reserveSummariesRowsCSV, err := c.getBordereauxReserveSummariesCSV(ctx, carrier)
	if err != nil {
		return uuid.Nil, nil, errors.Wrap(err, "failed to get bordereaux reserve summaries CSV")
	}

	generatedAt := c.deps.Clk.Now()

	xlsxBuffer, err := buildBordereauxWorkbook(claimsRowsCSV, reserveSummariesRowsCSV, generatedAt)
	if err != nil {
		return uuid.Nil, nil, errors.Wrap(err, "failed to generate xlsx")
	}

	xlsxName := fmt.Sprintf("Snapsheet_Loss_Runs_%s_%s.xlsx", carrier, generatedAt.Format("01_02_2006"))
	fileHandleId, err := c.uploadExcel(ctx, xlsxBuffer, xlsxName, carrier)
	if err != nil {
		return uuid.Nil, nil, errors.Wrap(err, "failed to upload xlsx file")
	}

	reportId, err := c.storeBordereauxReport(ctx, carrier, fileHandleId, generatedAt)
	if err != nil {
		return uuid.Nil, nil, errors.Wrap(err, "failed to store bordereaux report")
	}

	downloadURL, err := c.generateDownloadURL(ctx, fileHandleId)
	if err != nil {
		return uuid.Nil, nil, errors.Wrap(err, "failed to generate temporary download URL")
	}

	return reportId, downloadURL, nil
}

func (c *Client) getBordereauxClaimsCSV(ctx context.Context, carrier enums.Carrier) ([]byte, error) {
	claimsRows, err := c.deps.SnowflakeWrapper.GetBordereauxClaimsRows(ctx, carrier)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get bordereaux claims rows")
	}
	return csvutil.Marshal(claimsRows)
}

func (c *Client) getBordereauxReserveSummariesCSV(ctx context.Context, carrier enums.Carrier) ([]byte, error) {
	reserveSummariesRows, err := c.deps.SnowflakeWrapper.GetBordereauxReserveSummariesRows(ctx, carrier)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get bordereaux reserve summaries rows")
	}
	return csvutil.Marshal(reserveSummariesRows)
}

// uploadExcel uploads the given xlsx buffer to S3, and returns the file handle ID.
func (c *Client) uploadExcel(
	ctx context.Context,
	xlsxBuffer *bytes.Buffer,
	xlsxName string,
	carrier enums.Carrier,
) (uuid.UUID, error) {
	fileHandleId := uuid.New()
	accountId := uuid_utils.StableUUID(carrier.String())
	err := c.deps.FilesManager.UploadFile(
		ctx,
		bytes.NewReader(xlsxBuffer.Bytes()),
		fileHandleId,
		file_enums.FileTypeReport,
		xlsxName,
		accountId,
		file_enums.FileDestinationGroupClaims,
	)
	if err != nil {
		return uuid.Nil, errors.Wrap(err, "failed to upload xlsx file")
	}
	return fileHandleId, nil
}

// buildBordereauxWorkbook creates an XLSX workbook with the following sheets:
// - Claims
// - ReserveSummaries
// - ClaimLevelPlGl
// - ReserveLevelPlGl
// - ReserveLevelWC
// from the provided CSV contents and returns the workbook as a buffer.
func buildBordereauxWorkbook(claimsCSV, reserveSummariesCSV []byte, generatedAt time.Time) (*bytes.Buffer, error) {
	f := excelize.NewFile()
	sheets := []struct {
		name  string
		title string // this is what will appear on the first row
		data  []byte
	}{
		{
			name:  "ALL - Claim Level (AUTO)",
			title: fmt.Sprintf("Claim Loss Run as of %s", generatedAt.Format("01/02/2006")),
			data:  claimsCSV,
		},
		{
			name:  "ALL - Reserve Level (AUTO)",
			title: fmt.Sprintf("Reserve Loss Run as of %s", generatedAt.Format("01/02/2006")),
			data:  reserveSummariesCSV,
		},
		{
			name:  "ALL - Claim Level (PL and GL)",
			title: "",
			data:  nil,
		},
		{
			name:  "ALL - Reserve Level (PL and GL)",
			title: "",
			data:  nil,
		},
		{
			name:  "ALL - Reserve Level (WC)",
			title: "",
			data:  nil,
		},
	}

	f.SetSheetName(f.GetSheetName(0), sheets[0].name)
	if sheets[0].data != nil {
		if err := writeCsvToSheet(f, sheets[0].name, sheets[0].title, sheets[0].data); err != nil {
			return nil, errors.Wrapf(err, "failed to write %s sheet", sheets[0].name)
		}
	}

	for i := 1; i < len(sheets); i++ {
		sheet := sheets[i]
		_ = f.NewSheet(sheet.name)
		if sheet.data != nil {
			if err := writeCsvToSheet(f, sheet.name, sheet.title, sheet.data); err != nil {
				return nil, errors.Wrapf(err, "failed to write %s sheet", sheet.name)
			}
		}
	}

	f.SetActiveSheet(0)

	buf := new(bytes.Buffer)
	if err := f.Write(buf); err != nil {
		return nil, errors.Wrap(err, "failed to write workbook to buffer")
	}
	return buf, nil
}

// writeCsvToSheet reads CSV bytes and writes rows into the given sheet.
// If a non-empty title is provided, it is written as the first row above the CSV header.
func writeCsvToSheet(f *excelize.File, sheetName, title string, csvBytes []byte) error {
	reader := csv.NewReader(bytes.NewReader(csvBytes))

	// Read header row first (ensures presence even for empty datasets)
	rows, err := reader.ReadAll()
	if err != nil {
		return errors.Wrap(err, "failed to parse csv contents")
	}

	// Write title on row 1
	currentRowIdx := 1
	if title != "" {
		axis, err := excelize.CoordinatesToCellName(1, currentRowIdx)
		if err != nil {
			return errors.Wrap(err, "failed to compute cell axis for title")
		}
		// SetSheetRow expects a pointer to a slice
		titleRow := []string{title}
		if err := f.SetSheetRow(sheetName, axis, &titleRow); err != nil {
			return errors.Wrap(err, "failed to set sheet title row")
		}
		currentRowIdx++
	}

	for _, row := range rows {
		axis, err := excelize.CoordinatesToCellName(1, currentRowIdx)
		if err != nil {
			return errors.Wrap(err, "failed to compute cell axis")
		}

		// SetSheetRow expects a pointer to a slice
		r := make([]string, len(row))
		copy(r, row)
		if err := f.SetSheetRow(sheetName, axis, &r); err != nil {
			return errors.Wrap(err, "failed to set sheet row")
		}
		currentRowIdx++
	}
	return nil
}

func (c *Client) storeBordereauxReport(
	ctx context.Context,
	carrier enums.Carrier,
	fileHandleId uuid.UUID,
	generatedAt time.Time,
) (uuid.UUID, error) {
	generatedBy := authz.UserFromContext(ctx).ID
	report := db.NewBordereauxReportBuilder().
		WithCarrier(carrier).
		WithGeneratedAt(generatedAt).
		WithGeneratedBy(generatedBy).
		WithFileHandleId(fileHandleId).
		Build()
	if err := c.deps.BordereauxReportWrapper.Insert(ctx, report); err != nil {
		return uuid.Nil, errors.Wrap(err, "failed to insert bordereaux report")
	}
	return report.Id, nil
}
