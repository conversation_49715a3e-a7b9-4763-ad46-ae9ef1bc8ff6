package client_test

import (
	"bytes"
	"context"
	"encoding/csv"
	"io"
	"net/url"
	"time"

	"github.com/google/uuid"
	"github.com/jszwec/csvutil"
	"github.com/xuri/excelize/v2"
	"go.uber.org/mock/gomock"

	"nirvanatech.com/nirvana/claims/reporting/enums"
	"nirvanatech.com/nirvana/claims/reporting/internal/db/snowflake"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/infra/authz"
)

func (s *clientTestSuite) TestGenerateBordereauxReport() {
	testCases := []struct {
		name                       string
		generatedAt                time.Time
		userId                     uuid.UUID
		carrier                    enums.Carrier
		bordereauxClaims           []snowflake.BordereauxClaimRow
		bordereauxReserveSummaries []snowflake.BordereauxReserveSummaryRow
		expectedFileNameContains   []string
		expectedClaimSheetTitle    string
		expectedReserveSheetTitle  string
		expectedEmptySheets        []string
		expectedNonEmptySheets     []string
	}{
		{
			name:        "MSTransverse carrier with claims and reserves",
			generatedAt: time_utils.NewDate(2025, time.August, 15).ToTime(),
			userId:      uuid.New(),
			carrier:     enums.CarrierMSTransverse,
			bordereauxClaims: []snowflake.BordereauxClaimRow{
				{
					POLICY_NUMBER: "TINCA0100010-24",
					CLAIM_NUMBER:  "CLAIM-111",
				},
				{
					POLICY_NUMBER: "TINCA0200020-25",
					CLAIM_NUMBER:  "CLAIM-222",
				},
			},
			bordereauxReserveSummaries: []snowflake.BordereauxReserveSummaryRow{
				{
					CLAIM_NUMBER: "CLAIM-111",
					AOE_PAID:     1_000,
				},
			},
			expectedFileNameContains:  []string{"Snapsheet_Loss_Runs_MSTransverse_", ".xlsx"},
			expectedClaimSheetTitle:   "Claim Loss Run as of",
			expectedReserveSheetTitle: "Reserve Loss Run as of",
			expectedEmptySheets: []string{
				"ALL - Claim Level (PL and GL)",
				"ALL - Reserve Level (PL and GL)",
				"ALL - Reserve Level (WC)",
			},
			expectedNonEmptySheets: []string{
				"ALL - Claim Level (AUTO)",
				"ALL - Reserve Level (AUTO)",
			},
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			s.Clk.Set(tc.generatedAt)

			ctx := authz.WithUser(context.Background(), authz.User{
				UserInfo: authz.UserInfo{ID: tc.userId},
			})

			s.SnowflakeWrapper.
				EXPECT().
				GetBordereauxClaimsRows(gomock.Any(), tc.carrier).
				Return(tc.bordereauxClaims, nil).
				Times(1)

			s.SnowflakeWrapper.
				EXPECT().
				GetBordereauxReserveSummariesRows(gomock.Any(), tc.carrier).
				Return(tc.bordereauxReserveSummaries, nil).
				Times(1)

			reportId, downloadURL, err := s.Client.GenerateBordereauxReport(ctx, tc.carrier)
			s.Require().NoError(err)

			storedReports, err := s.BordereauxReportDataWrapper.Get(ctx)
			s.Require().NoError(err)
			s.Require().Equal(1, len(storedReports))

			gotReport := storedReports[0]
			s.Equal(reportId, gotReport.Id)
			s.Equal(tc.carrier, gotReport.Carrier)
			s.Equal(tc.userId, gotReport.GeneratedBy)
			s.Equal(tc.generatedAt, gotReport.GeneratedAt)
			s.True(isValidURL(downloadURL.String()))

			gotUploadedFileName, err := s.FileUploadManager.GetFileNameByHandleID(ctx, gotReport.FileHandleId)
			s.Require().NoError(err)
			for _, expectedStr := range tc.expectedFileNameContains {
				s.Contains(gotUploadedFileName, expectedStr)
			}

			excelBytes := s.getUploadedFileBytes(ctx, gotReport.FileHandleId)
			s.Require().NoError(err)

			f, err := excelize.OpenReader(bytes.NewReader(excelBytes))
			s.Require().NoError(err)

			for _, sheetName := range tc.expectedNonEmptySheets {
				rows, err := f.GetRows(sheetName)
				s.Require().NoError(err)
				s.Require().GreaterOrEqual(len(rows), 2, "sheet %s should have at least title and header rows", sheetName)

				if sheetName == "ALL - Claim Level (AUTO)" {
					s.Contains(rows[0][0], tc.expectedClaimSheetTitle)
					s.Require().Equal(wantSortedClaimsHeaders, rows[1])

					csvBytes := s.rowsToCSVBytes(rows[1:])
					var gotUploadedBordereauxClaims []snowflake.BordereauxClaimRow
					s.Require().NoError(csvutil.Unmarshal(csvBytes, &gotUploadedBordereauxClaims))
					s.ElementsMatch(tc.bordereauxClaims, gotUploadedBordereauxClaims)
				}

				if sheetName == "ALL - Reserve Level (AUTO)" {
					s.Contains(rows[0][0], tc.expectedReserveSheetTitle)
					s.Require().Equal(wantSortedReserveHeaders, rows[1])

					csvBytes := s.rowsToCSVBytes(rows[1:])
					var gotUploadedBordereauxReserves []snowflake.BordereauxReserveSummaryRow
					s.Require().NoError(csvutil.Unmarshal(csvBytes, &gotUploadedBordereauxReserves))
					s.ElementsMatch(tc.bordereauxReserveSummaries, gotUploadedBordereauxReserves)
				}
			}

			for _, sheetName := range tc.expectedEmptySheets {
				rows, err := f.GetRows(sheetName)
				s.Require().NoError(err)
				s.Require().Equal(0, len(rows), "sheet %s should be empty", sheetName)
			}
		})
	}
}

func (s *clientTestSuite) getUploadedFileBytes(
	ctx context.Context,
	handleId uuid.UUID,
) []byte {
	reader, err := s.FileUploadManager.DownloadFile(ctx, handleId)
	s.Require().NoError(err)
	b, err := io.ReadAll(reader)
	s.Require().NoError(err)
	return b
}

func (s *clientTestSuite) rowsToCSVBytes(rows [][]string) []byte {
	var buf bytes.Buffer
	w := csv.NewWriter(&buf)
	w.UseCRLF = true
	for _, r := range rows {
		s.Require().NoError(w.Write(r))
	}
	w.Flush()
	s.Require().NoError(w.Error())
	return buf.Bytes()
}

func isValidURL(str string) bool {
	u, err := url.ParseRequestURI(str)
	return err == nil && u.Scheme != "" && u.Host != ""
}

var wantSortedClaimsHeaders = []string{
	"Client Name",
	"Client Location",
	"Org Location Name",
	"LOB",
	"Retail Broker/Agent",
	"Insured",
	"Insured State",
	"Insurer DBA Name",
	"Driver",
	"Vehicle VIN",
	"Insured Vehicle Total Loss (Y/N)",
	"Policy Nbr",
	"Risk St",
	"Eff Date",
	"Exp Date",
	"Policy Cancel Date",
	"Claim Nbr",
	"Claim Type",
	"Prev Claim Nbr",
	"Is Denied",
	"Denial Reason",
	"Contested (Y/N)",
	"Incident Only",
	"Litigation (Y/N)",
	"Acc Date",
	"Acc Desc",
	"Acc Code",
	"Acc Location",
	"Acc City",
	"Acc County",
	"Acc State",
	"Claim Status",
	"Claim Adjuster",
	"Claim Team",
	"Dt Reported",
	"Reported Method",
	"Lag Time",
	"Dt Open",
	"Dt Closed",
	"Dt Reopen",
	"Days Open",
	"Catastrophe Nbr",
	"Exceeds Client Authority (Y/N)",
	"Ind Paid",
	"Med Paid",
	"Exp Paid",
	"DCC Paid",
	"AOE Paid",
	"Ind Res",
	"Med Res",
	"Exp Res",
	"DCC Res",
	"AOE Res",
	"Gross Incurred",
	"Total Recoveries",
	"Subro Rec",
	"Salvage Rec",
	"Deduct Rec",
	"Other Recoveries",
	"Net Incurred",
}

var wantSortedReserveHeaders = []string{
	"Client Name",
	"Client Location",
	"Org Location Name",
	"LOB",
	"Retail Broker/Agent",
	"Insured",
	"Insured State",
	"Insurer DBA Name",
	"Driver",
	"Vehicle VIN",
	"Insured Vehicle Total Loss (Y/N)",
	"Vehicle Make",
	"Vehicle Model",
	"Policy Nbr",
	"Risk St",
	"Eff Date",
	"Exp Date",
	"Policy Cancel Date",
	"Reserve Nbr",
	"Reserve Status",
	"Claim Nbr",
	"Reserve Reference Nbr",
	"Claim Type",
	"Prev Claim Nbr",
	"Is Denied",
	"Denial Reason",
	"Contested (Y/N)",
	"Litigation (Y/N)",
	"Acc Date",
	"Acc Desc",
	"Acc Code",
	"Acc Location",
	"Acc City",
	"Acc County",
	"Acc State",
	"Jurisdiction State",
	"Claim Status",
	"Line Code",
	"Coverage Code",
	"Coverage Type",
	"Claimant",
	"Reserve Adjuster",
	"Reserve Supervisor",
	"Dt Reported",
	"Reported Method",
	"Lag Time",
	"Dt Open",
	"Dt Closed",
	"Dt Reopen",
	"Reserve Dt Open",
	"Reserve Dt Closed",
	"Reserve Dt Reopen",
	"Days Reserve Open",
	"Catastrophe Nbr",
	"Exceeds Client Authority (Y/N)",
	"Ind Paid",
	"Med Paid",
	"Exp Paid",
	"DCC Paid",
	"AOE Paid",
	"Ind Res",
	"Med Res",
	"Exp Res",
	"DCC Res",
	"AOE Res",
	"Gross Incurred",
	"Total Recoveries",
	"Subro Rec",
	"Salvage Rec",
	"Deduct Rec",
	"Other Recoveries",
	"Net Incurred",
}
