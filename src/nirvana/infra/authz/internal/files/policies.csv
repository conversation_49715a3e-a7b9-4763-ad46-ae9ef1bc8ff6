# Rules that appear earlier in this policy file have higher priority.
#
p, user:UNAUTHENTICATED, *, *, deny

# QA Underwriter Testing:
# - The QA Underwriters should have underwriter permissions only to the QA agency.
# - Superusers,Power Users and actual underwriters should not have access to QA, since it would clutter up their UI.
# - KiwiQA:
p, role:underwriter/qa, /agency/f7e00a5b-66b0-407a-88de-557c43d23fda/applications/*, (read|write), allow
p, role:underwriter/qa, /agency/f7e00a5b-66b0-407a-88de-557c43d23fda/endorsement-reviews/*, (read|write), allow
p, role:underwriter/qa, /agency/f7e00a5b-66b0-407a-88de-557c43d23fda/form_compilations/*, (read|write), allow
p, role:superuser, /agency/f7e00a5b-66b0-407a-88de-557c43d23fda/applications/*, *, deny
p, role:poweruser, /agency/f7e00a5b-66b0-407a-88de-557c43d23fda/applications/*, *, deny
p, role:underwriter/senior, /agency/f7e00a5b-66b0-407a-88de-557c43d23fda/applications/*, *, deny
p, role:support/senior, /agency/f7e00a5b-66b0-407a-88de-557c43d23fda/applications/*, *, deny
p, role:underwriter/manager, /agency/f7e00a5b-66b0-407a-88de-557c43d23fda/applications/*, *, deny
p, role:superuser, /agency/f7e00a5b-66b0-407a-88de-557c43d23fda/endorsement-requests/*, *, deny
p, role:poweruser, /agency/f7e00a5b-66b0-407a-88de-557c43d23fda/endorsement-requests/*, *, deny
p, role:underwriter/senior, /agency/f7e00a5b-66b0-407a-88de-557c43d23fda/endorsement-requests/*, *, deny
p, role:support/senior, /agency/f7e00a5b-66b0-407a-88de-557c43d23fda/endorsement-requests/*, *, deny
p, role:underwriter/manager, /agency/f7e00a5b-66b0-407a-88de-557c43d23fda/endorsement-requests/*, *, deny
p, role:superuser, /agency/f7e00a5b-66b0-407a-88de-557c43d23fda/endorsement-reviews/*, *, deny
p, role:poweruser, /agency/f7e00a5b-66b0-407a-88de-557c43d23fda/endorsement-reviews/*, *, deny
p, role:support/senior, /agency/f7e00a5b-66b0-407a-88de-557c43d23fda/endorsement-reviews/*, *, deny
p, role:underwriter/senior, /agency/f7e00a5b-66b0-407a-88de-557c43d23fda/endorsement-reviews/*, *, deny
p, role:underwriter/manager, /agency/f7e00a5b-66b0-407a-88de-557c43d23fda/endorsement-reviews/*, *, deny
p, role:superuser, /agency/f7e00a5b-66b0-407a-88de-557c43d23fda/form_compilations/*, *, deny
p, role:poweruser, /agency/f7e00a5b-66b0-407a-88de-557c43d23fda/form_compilations/*, *, deny
p, role:support/senior, /agency/f7e00a5b-66b0-407a-88de-557c43d23fda/form_compilations/*, *, deny
p, role:underwriter/senior, /agency/f7e00a5b-66b0-407a-88de-557c43d23fda/form_compilations/*, *, deny
p, role:underwriter/manager, /agency/f7e00a5b-66b0-407a-88de-557c43d23fda/form_compilations/*, *, deny
p, role:policy/admin/reader, /agency/f7e00a5b-66b0-407a-88de-557c43d23fda/policies/*, *, deny
# - Whitesmith:
p, role:underwriter/qa, /agency/57877c0e-ef11-43d8-b5ed-c2d9b37db37c/applications/*, (read|write), allow
p, role:underwriter/qa, /agency/57877c0e-ef11-43d8-b5ed-c2d9b37db37c/endorsement-reviews/*, (read|write), allow
p, role:superuser, /agency/57877c0e-ef11-43d8-b5ed-c2d9b37db37c/applications/*, *, deny
p, role:poweruser, /agency/57877c0e-ef11-43d8-b5ed-c2d9b37db37c/applications/*, *, deny
p, role:underwriter/senior, /agency/57877c0e-ef11-43d8-b5ed-c2d9b37db37c/applications/*, *, deny
p, role:underwriter/manager, /agency/57877c0e-ef11-43d8-b5ed-c2d9b37db37c/applications/*, *, deny
p, role:superuser, /agency/57877c0e-ef11-43d8-b5ed-c2d9b37db37c/endorsement-requests/*, *, deny
p, role:poweruser, /agency/57877c0e-ef11-43d8-b5ed-c2d9b37db37c/endorsement-requests/*, *, deny
p, role:underwriter/senior, /agency/57877c0e-ef11-43d8-b5ed-c2d9b37db37c/endorsement-requests/*, *, deny
p, role:underwriter/manager, /agency/57877c0e-ef11-43d8-b5ed-c2d9b37db37c/endorsement-requests/*, *, deny
p, role:superuser, /agency/57877c0e-ef11-43d8-b5ed-c2d9b37db37c/endorsement-reviews/*, *, deny
p, role:poweruser, /agency/57877c0e-ef11-43d8-b5ed-c2d9b37db37c/endorsement-reviews/*, *, deny
p, role:underwriter/senior, /agency/57877c0e-ef11-43d8-b5ed-c2d9b37db37c/endorsement-reviews/*, *, deny
p, role:underwriter/manager, /agency/57877c0e-ef11-43d8-b5ed-c2d9b37db37c/endorsement-reviews/*, *, deny
p, role:policy/admin/reader, /agency/57877c0e-ef11-43d8-b5ed-c2d9b37db37c/policies/*, *, deny
# - Treemouse:
p, role:underwriter/qa, /agency/6c157a2e-d946-469f-9e24-6ca9fb12d43a/applications/*, (read|write), allow
p, role:underwriter/qa, /agency/6c157a2e-d946-469f-9e24-6ca9fb12d43a/endorsement-reviews/*, (read|write), allow
p, role:superuser, /agency/6c157a2e-d946-469f-9e24-6ca9fb12d43a/applications/*, *, deny
p, role:poweruser, /agency/6c157a2e-d946-469f-9e24-6ca9fb12d43a/applications/*, *, deny
p, role:underwriter/senior, /agency/6c157a2e-d946-469f-9e24-6ca9fb12d43a/applications/*, *, deny
p, role:underwriter/manager, /agency/6c157a2e-d946-469f-9e24-6ca9fb12d43a/applications/*, *, deny
p, role:superuser, /agency/6c157a2e-d946-469f-9e24-6ca9fb12d43a/endorsement-requests/*, *, deny
p, role:poweruser, /agency/6c157a2e-d946-469f-9e24-6ca9fb12d43a/endorsement-requests/*, *, deny
p, role:underwriter/senior, /agency/6c157a2e-d946-469f-9e24-6ca9fb12d43a/endorsement-requests/*, *, deny
p, role:underwriter/manager, /agency/6c157a2e-d946-469f-9e24-6ca9fb12d43a/endorsement-requests/*, *, deny
p, role:superuser, /agency/6c157a2e-d946-469f-9e24-6ca9fb12d43a/endorsement-reviews/*, *, deny
p, role:poweruser, /agency/6c157a2e-d946-469f-9e24-6ca9fb12d43a/endorsement-reviews/*, *, deny
p, role:underwriter/senior, /agency/6c157a2e-d946-469f-9e24-6ca9fb12d43a/endorsement-reviews/*, *, deny
p, role:underwriter/manager, /agency/6c157a2e-d946-469f-9e24-6ca9fb12d43a/endorsement-reviews/*, *, deny
p, role:policy/admin/reader, /agency/6c157a2e-d946-469f-9e24-6ca9fb12d43a/policies/*, *, deny

# - Canopius:
# Allow Canopius UW to only be able to have read access to application reviews
p, role:underwriter/canopius, /agency/*/applications/*/review/*, (read), allow
p, role:underwriter/canopius, /agency/*/applications/*/review/*/files/*, (read), allow
p, role:underwriter/canopius, /agency/*/files/*, (read), allow
p, role:underwriter/qa, /agency/fad43a71-dd78-40a0-81b4-84b31f134428/applications/*, *, deny
p, role:underwriter/qa, /agency/fad43a71-dd78-40a0-81b4-84b31f134428/endorsement-reviews/*, *, deny
p, role:superuser, /agency/fad43a71-dd78-40a0-81b4-84b31f134428/applications/*, *, deny
p, role:poweruser, /agency/fad43a71-dd78-40a0-81b4-84b31f134428/applications/*, *, deny
p, role:underwriter/senior, /agency/fad43a71-dd78-40a0-81b4-84b31f134428/applications/*, *, deny
p, role:underwriter/manager, /agency/fad43a71-dd78-40a0-81b4-84b31f134428/applications/*, *, deny
p, role:superuser, /agency/fad43a71-dd78-40a0-81b4-84b31f134428/endorsement-requests/*, *, deny
p, role:poweruser, /agency/fad43a71-dd78-40a0-81b4-84b31f134428/endorsement-requests/*, *, deny
p, role:underwriter/senior, /agency/fad43a71-dd78-40a0-81b4-84b31f134428/endorsement-requests/*, *, deny
p, role:underwriter/manager, /agency/fad43a71-dd78-40a0-81b4-84b31f134428/endorsement-requests/*, *, deny
p, role:superuser, /agency/fad43a71-dd78-40a0-81b4-84b31f134428/endorsement-reviews/*, *, deny
p, role:poweruser, /agency/fad43a71-dd78-40a0-81b4-84b31f134428/endorsement-reviews/*, *, deny
p, role:underwriter/senior, /agency/fad43a71-dd78-40a0-81b4-84b31f134428/endorsement-reviews/*, *, deny
p, role:underwriter/manager, /agency/fad43a71-dd78-40a0-81b4-84b31f134428/endorsement-reviews/*, *, deny
p, role:policy/admin/reader, /agency/fad43a71-dd78-40a0-81b4-84b31f134428/policies/*, *, deny

# Superuser role should have access to everything (except the QA agencies)
p, role:superuser, *, *, allow

# Senior underwriters should have permissions to all applications, reviews, safety and forms data.
p, role:underwriter/senior, /agency/*/applications/*, (read|write), allow
p, role:underwriter/senior, /agency/*/endorsement-requests/*, (read|write), allow
p, role:underwriter/senior, /agency/*/endorsement-reviews/*, (read|write), allow
p, role:underwriter/senior, /agency/*/applications/*/review/*/files/*, (read|write|create), allow
p, role:underwriter/senior, /agency/*/files/*, (read|write|create), allow
p, role:underwriter/senior, /safety/reports/*, read, allow
p, role:underwriter/senior, /fleets/*, read, allow
p, role:underwriter/senior, /agency/*/form_compilations/*, *, allow

#  Underwriter Manager should have permissions to all applications, reviews and safety data.
p, role:underwriter/manager, /agency/*/applications/*, (read|write), allow
p, role:underwriter/manager, /agency/*/endorsement-requests/*, (read|write), allow
p, role:underwriter/manager, /agency/*/endorsement-reviews/*, (read|write), allow
p, role:underwriter/manager, /agency/*/applications/*/review/*/files/*, (read|write|create), allow
p, role:underwriter/manager, /agency/*/files/*, (read|write|create), allow
p, role:underwriter/manager, /safety/reports/*, read, allow
p, role:underwriter/manager, /fleets/*, read, allow

# Level 1 underwriters should have permissions to all applications, reviews and safety data.
p, role:underwriter/level_1, /agency/*/applications/*, (read|write), allow
p, role:underwriter/level_1, /agency/*/endorsement-requests/*, (read|write), allow
p, role:underwriter/level_1, /agency/*/endorsement-reviews/*, (read|write), allow
p, role:underwriter/level_1, /agency/*/applications/*/review/*/files/*, (read|write|create), allow
p, role:underwriter/level_1, /agency/*/files/*, (read|write|create), allow
p, role:underwriter/level_1, /safety/reports/*, read, allow
p, role:underwriter/level_1, /fleets/*, read, allow

# Level 2 underwriters should have permissions to all applications, reviews and safety data.
p, role:underwriter/level_2, /agency/*/applications/*, (read|write), allow
p, role:underwriter/level_2, /agency/*/endorsement-requests/*, (read|write), allow
p, role:underwriter/level_2, /agency/*/endorsement-reviews/*, (read|write), allow
p, role:underwriter/level_2, /agency/*/applications/*/review/*/files/*, (read|write|create), allow
p, role:underwriter/level_2, /agency/*/files/*, (read|write|create), allow
p, role:underwriter/level_2, /safety/reports/*, read, allow
p, role:underwriter/level_2, /fleets/*, read, allow

# Level 3 underwriters should have permissions to all applications, reviews and safety data.
p, role:underwriter/level_3, /agency/*/applications/*, (read|write), allow
p, role:underwriter/level_3, /agency/*/endorsement-requests/*, (read|write), allow
p, role:underwriter/level_3, /agency/*/endorsement-reviews/*, (read|write), allow
p, role:underwriter/level_3, /agency/*/applications/*/review/*/files/*, (read|write|create), allow
p, role:underwriter/level_3, /agency/*/files/*, (read|write|create), allow
p, role:underwriter/level_3, /safety/reports/*, read, allow
p, role:underwriter/level_3, /fleets/*, read, allow

# Level 4 underwriters should have permissions to all applications, reviews and safety data.
p, role:underwriter/level_4, /agency/*/applications/*, (read|write), allow
p, role:underwriter/level_4, /agency/*/endorsement-requests/*, (read|write), allow
p, role:underwriter/level_4, /agency/*/endorsement-reviews/*, (read|write), allow
p, role:underwriter/level_4, /agency/*/applications/*/review/*/files/*, (read|write|create), allow
p, role:underwriter/level_4, /agency/*/files/*, (read|write|create), allow
p, role:underwriter/level_4, /safety/reports/*, read, allow
p, role:underwriter/level_4, /fleets/*, read, allow

# Level 5 underwriters should have permissions to all applications, reviews and safety data.
p, role:underwriter/level_5, /agency/*/applications/*, (read|write), allow
p, role:underwriter/level_5, /agency/*/endorsement-requests/*, (read|write), allow
p, role:underwriter/level_5, /agency/*/endorsement-reviews/*, (read|write), allow
p, role:underwriter/level_5, /agency/*/applications/*/review/*/files/*, (read|write|create), allow
p, role:underwriter/level_5, /agency/*/files/*, (read|write|create), allow
p, role:underwriter/level_5, /safety/reports/*, read, allow
p, role:underwriter/level_5, /fleets/*, read, allow

# Level 6 underwriters should have permissions to all applications, reviews and safety data.
p, role:underwriter/level_6, /agency/*/applications/*, (read|write), allow
p, role:underwriter/level_6, /agency/*/endorsement-requests/*, (read|write), allow
p, role:underwriter/level_6, /agency/*/endorsement-reviews/*, (read|write), allow
p, role:underwriter/level_6, /agency/*/applications/*/review/*/files/*, (read|write|create), allow
p, role:underwriter/level_6, /agency/*/files/*, (read|write|create), allow
p, role:underwriter/level_6, /safety/reports/*, read, allow
p, role:underwriter/level_6, /fleets/*, read, allow

# Level 7 underwriters should have permissions to all applications, reviews and safety data.
p, role:underwriter/level_7, /agency/*/applications/*, (read|write), allow
p, role:underwriter/level_7, /agency/*/endorsement-requests/*, (read|write), allow
p, role:underwriter/level_7, /agency/*/endorsement-reviews/*, (read|write), allow
p, role:underwriter/level_7, /agency/*/applications/*/review/*/files/*, (read|write|create), allow
p, role:underwriter/level_7, /agency/*/files/*, (read|write|create), allow
p, role:underwriter/level_7, /safety/reports/*, read, allow
p, role:underwriter/level_7, /fleets/*, read, allow


# Support user should have access to read underwriter reviews and form compilations.
p, role:support/senior, /agency/*/applications/*/review/*, read, allow
p, role:support/senior, /agency/*/endorsement-reviews/*, read, allow
# Support user should have read and write access to form compilations.
p, role:support/senior, /agency/*/form_compilations/*, (read|write), allow

# Deny access to underwriter reviews for agency roles.
p, role:agency/admin, /agency/*/applications/*/review/*, *, deny
p, role:agency/producer, /agency/*/applications/*/review/*, *, deny
p, role:agency/account_manager, /agency/*/applications/*/review/*, *, deny
p, role:agency/reader, /agency/*/applications/*/review/*, *, deny
p, role:support/senior, /agency/*/applications/*/review/*, *, deny
p, role:poweruser, /agency/*/applications/*/review/*, *, deny
p, role:agency/service_member, /agency/*/applications/*/review/*, *, deny
p, role:agency/admin, /agency/*/endorsement-reviews/*, *, deny
p, role:agency/producer, /agency/*/endorsement-reviews/*, *, deny
p, role:agency/account_manager, /agency/*/endorsement-reviews/*, *, deny
p, role:agency/reader, /agency/*/endorsement-reviews/*, *, deny
p, role:support/senior, /agency/*/endorsement-reviews/*, *, deny
p, role:poweruser, /agency/*/endorsement-reviews/*, *, deny
p, role:agency/service_member, /agency/*/endorsement-reviews/*, *, deny

# Deny access to RateML quote artifacts for agency roles.
p, role:agency/admin, /agency/*/applications/*/quote_artifacts, *, deny
p, role:agency/producer, /agency/*/applications/*/quote_artifacts, *, deny
p, role:agency/account_manager, /agency/*/applications/*/quote_artifacts, *, deny
p, role:agency/reader, /agency/*/applications/*/quote_artifacts, *, deny

# Grant access to agency and safety domains for support roles.
p, role:support/senior, /agency, (create|read), allow
p, role:support/senior, /agency/*, (read|write), allow
p, role:support/senior, /agency/*/users/*, (create|read|write), allow
p, role:support/senior, /users, create, allow
p, role:support/senior, /users/*, (read|write), allow
p, role:support/senior, /fleets/*, (read|write), allow
p, role:support/senior, /safety/reports/*, read, allow

# Grant access to agency and safety domains for support roles.
p, role:poweruser, /agency, (create|read), allow
p, role:poweruser, /agency/*, (read|write), allow
p, role:poweruser, /agency/*/users/*, (create|read|write), allow
p, role:poweruser, /users, create, allow
p, role:poweruser, /users/*, (read|write), allow
p, role:poweruser, /fleets/*, (read|write), allow
p, role:poweruser, /safety/reports/*, read, allow

# Grant access to agency for contractor roles
# Nirvana contractors for marketer can create applications for any agency
p, role:nirvana/contractor_for_marketer, /agency/*/applications, create, allow

# Grant access to manage policies on behalf of the agency, Deny access to applications
p, role:agency/service_member, /agency/*/endorsement-requests/*, (read|write|create), allow
p, role:agency/service_member, /users/*/endorsement-requests/*, (read|write|create), allow
p, role:agency/service_member, /agency/*/applications/*, *, deny
p, role:agency/service_member, /users/*/applications/*, *, deny

# For now, agency/admin is identical to agency/producer and agency/account_manager.
p, role:agency/admin, /agency/{agencyID}, (read|write|create), allow
p, role:agency/admin, /agency/{agencyID}/*, (read|write|create), allow
p, role:agency/producer, /agency/{agencyID}, (read|write|create), allow
p, role:agency/producer, /agency/{agencyID}/*, (read|write|create), allow
p, role:agency/account_manager, /agency/{agencyID}, (read|write|create), allow
p, role:agency/account_manager, /agency/{agencyID}/*, (read|write|create), allow

p, role:agency/reader, /agency/{agencyID}, read, allow
p, role:agency/reader, /agency/{agencyID}/*, read, allow
p, role:fleet/admin, /fleets/{fleetID}, (read|write), allow
p, role:fleet/admin, /fleets/{fleetID}/*, (read|write), allow
p, role:fleet/reader, /fleets/{fleetID}, read, allow
p, role:fleet/reader, /fleets/{fleetID}/*, read, allow

p, role:user/owner, /users/{userID}, (read|write), allow
p, role:user/owner, /users/{userID}/*, (read|write), allow

# Shared safety report with anonymous users.
p, role:shared/safety/reader, /safety/reports/{reportID}, read, allow
p, role:shared/safety/reader, /safety/reports/{reportID}/*, read, allow
p, role:shared/fleet/telematics, /fleets/{fleetID}/telematics/{handleID}, read, allow
p, role:shared/fleet/telematics, /fleets/{fleetID}/dot_pin_access, read, allow

p, role:safety/consultant/reader, /safety/reports/*, read, allow
p, role:safety/consultant/reader, /fleets/*/telematics/*, read, allow
p, role:safety/consultant/reader, /fleets/*/dot_pin_access, read, allow

# Grant read access to all policies to this role
p, role:policy/admin/reader, /agency/*/policies/*, read, allow
