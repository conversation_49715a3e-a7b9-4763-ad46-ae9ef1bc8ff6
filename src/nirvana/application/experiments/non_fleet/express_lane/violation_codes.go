package express_lane

// NOTE: Replace these placeholder values with actual violation codes when available.
var (
	drugRelatedViolationCodes = []string{
		"111300",
		"111305",
		"111310",
		"111311",
		"111312",
		"111314",
		"111315",
		"111316",
		"111317",
		"111318",
		"111319",
		"111320",
		"111321",
		"111328",
		"111335",
		"111340",
		"111395",
		"111400",
		"111401",
		"111405",
		"111900",
		"311334",
	}
	fifteenOverSpeedViolationCodes = []string{
		"421470",
		"421480",
		"421510",
		"421520",
		"421530",
		"421540",
		"421550",
		"421560",
		"421611",
		"421615",
		"421620",
		"421624",
		"421625",
		"421630",
		"421634",
		"421635",
		"421640",
		"421653",
		"421751",
		"421752",
		"421760",
		"421762",
		"421763",
		"421770",
		"421775",
		"421880",
	}
	dotReportableViolationCodes = []string{
		"241000",
		"241210",
		"241213",
		"241220",
		"241230",
		"241240",
		"241250",
		"241270",
		"241310",
		"241320",
		"241330",
		"241340",
		"241350",
		"241370",
		"241400",
		"241500",
		"241505",
		"241515",
		"242100",
		"242110",
		"242120",
		"242200",
		"242210",
		"242220",
		"242230",
		"541260",
		"541360",
	}
)
