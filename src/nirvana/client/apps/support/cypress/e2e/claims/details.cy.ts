import { CyHttpMessages, RoutesHelper } from '@nirvana/core/testUtils';
import { noteFactory } from '../../support/factories/claimsFactory';
import {
  defaultClaimUuid,
  claims,
  claimById,
  fixedClaimNotesUuid,
  unsupportedTspClaimUuid,
  htmlNotesClaimUuid,
} from '../../support/handlers/claims';
import { safetyReportHandler } from '../../support/handlers/safetyReportHandler';

const routesHelper = new RoutesHelper();
const baseUrl = routesHelper.getBaseUrl();

describe('Claims Details', () => {
  beforeEach(() => {
    cy.login();
    routesHelper.overrideGraphqlResponse('claims', claims).as('claims');
    routesHelper
      .overrideGraphqlResponse('ClaimById', claimById)
      .as('claimById');
    routesHelper
      .overrideGraphqlResponse('fleetSafetyReport', safetyReportHandler)
      .as('fleetSafetyReport');
    cy.visit(`${baseUrl}/claims/list/${defaultClaimUuid}`);
  });

  it('Renders the claims details', () => {
    cy.wait('@claimById');
    cy.get('[data-testid="claim-details"]').should('be.visible');
  });

  it('Renders the claims notes', () => {
    cy.wait('@claimById');

    cy.get('[data-testid="claim-notes"]').should('be.visible');
    cy.get(
      '[data-testid="claim-notes"]>tbody>tr[data-testid="skeleton-row"]',
    ).should('have.length', 0);
    cy.get('[data-testid="claim-notes"]>tbody>tr').should('be.visible');
  });

  describe('When the claim has notes', () => {
    beforeEach(() => {
      cy.visit(`${baseUrl}/claims/list/${fixedClaimNotesUuid}`);
    });

    it('Allows to filter claim notes by category', () => {
      cy.wait('@claimById');
      cy.get('[data-testid="claim-activity-filter-menu"]').click();
      cy.get('[data-testid="category-filter"]').click();
      cy.get('li:contains("coverage")').click();
      cy.get('[data-testid="claim-notes"]>tbody>tr').should('have.length', 1);
    });
  });

  describe('When the claim has a unsupported TSP', () => {
    beforeEach(() => {
      cy.visit(`${baseUrl}/claims/list/${unsupportedTspClaimUuid}`);
    });

    // TODO(IE-967): reactivate this once permissions to fetch TSPs for claims adjusters are fixed.
    // eslint-disable-next-line mocha/no-skipped-tests
    it.skip('Does not allow to view the telematics data', () => {
      cy.get('[data-testid="claim-details"]').should('be.visible');
      cy.get('[data-testid="telematics"]').should('not.exist');
    });
  });

  it('It allows to view the telematics data', () => {
    cy.wait('@claimById');
    cy.wait('@fleetSafetyReport');
    cy.get('[data-telematics-support-loading="false"]').should('be.visible');
    cy.get('[data-testid="telematics"]').click();
    cy.get('[data-testid="telematics-data"]').should('be.visible');
  });

  it('Hides the sidebar when the user clicks on the button', () => {
    cy.wait('@claimById');
    cy.get('[data-testid="claim-list-toggle"]').click();
    cy.get('[data-testid="claims-list"]').should('not.be.visible');
    cy.get('[data-testid="claim-list-toggle"]')
      .invoke('attr', 'aria-expanded')
      .should('equal', 'false');
  });

  it('Shows the sidebar when the user clicks on the button twice', () => {
    cy.wait('@claimById');
    cy.get('[data-testid="claim-list-toggle"]').click();
    cy.get('[data-testid="claim-list-toggle"]').click();
    cy.get('[data-testid="claims-list"]').should('be.visible');
    cy.get('[data-testid="claim-list-toggle"]')
      .invoke('attr', 'aria-expanded')
      .should('equal', 'true');
  });

  describe('When a note contains HTML content', () => {
    beforeEach(() => {
      cy.visit(`${baseUrl}/claims/list/${htmlNotesClaimUuid}`);
    });

    it('renders HTML elements inside the note value', () => {
      cy.wait('@claimById');
      cy.get('[data-testid="claim-notes"] strong').should(
        'contain',
        'Important:',
      );
      cy.get('[data-testid="claim-notes"] a')
        .should('have.attr', 'href', 'https://example.com')
        .and('contain', 'details');
    });
  });
});
