import { RoutesHelper } from '@nirvana/core/testUtils';
import { claimById, claims } from '../../support/handlers/claims';

const routesHelper = new RoutesHelper();
const baseUrl = routesHelper.getBaseUrl();

describe('Claims', () => {
  describe('Claims List', () => {
    beforeEach(() => {
      cy.login();
      routesHelper.overrideGraphqlResponse('claims', claims).as('claims');
      routesHelper
        .overrideGraphqlResponse('ClaimById', claimById)
        .as('claimById');
      cy.visit(`${baseUrl}/claims`);
    });

    it('Renders the claims table', () => {
      cy.wait('@claims');
      cy.get('table>tbody>tr[data-testid="skeleton-row"]').should(
        'have.length',
        0,
      );
      cy.get('table>tbody>tr').should('be.visible');
    });

    it('Allows to search by Claim ID', () => {
      cy.wait('@claims');
      cy.get('[data-testid="search-input"]').type('NIT-123456789');
      cy.get('table>tbody>tr').should('have.length', 1);
    });

    it('Allows to search by Insured Name', () => {
      cy.wait('@claims');
      cy.get('[data-testid="search-input"]').type('Nirvana Trucking Inc');
      cy.get('table>tbody>tr').should('have.length', 1);
    });

    it('Allows to filter by Status', () => {
      cy.wait('@claims');
      cy.get('[data-testid="filter-menu"]').click();
      cy.get('[data-testid="status-filter"]').click();
      cy.get('li:contains("Open")').click();
      cy.get('[data-testid="claim-status-closed"]').should('not.exist');
      cy.get('[data-testid="claim-status-reopen"]').should('not.exist');
      cy.get('[data-testid="claim-status-open"]').should('exist');
    });

    it('Allows to filter by Modified At', () => {
      const DAYS_SINCE_LAST_MODIFIED = 7;
      cy.wait('@claims');
      cy.get('[data-testid="filter-menu"]').click();
      cy.get('[data-testid="modified-at-filter"]').click();
      cy.get(`li:contains("${DAYS_SINCE_LAST_MODIFIED} days")`).click();
      cy.get('[data-cy-modified-at-days]').each((row) => {
        const days = Number(row.attr('data-cy-modified-at-days'));
        cy.wrap(days).should('be.gte', DAYS_SINCE_LAST_MODIFIED);
      });
    });

    it('Allows to filter by Reported At', () => {
      const DAYS_SINCE_CLAIM_REPORTED = 7;
      cy.wait('@claims');
      cy.get('[data-testid="filter-menu"]').click();
      cy.get('[data-testid="reportedAt-filter"').click();
      cy.get(`li:contains("${DAYS_SINCE_CLAIM_REPORTED} days")`).click();
      cy.get('[data-cy-reported-at-days]').each((row) => {
        const days = Number(row.attr('data-cy-reported-at-days'));
        cy.wrap(days).should('be.lt', DAYS_SINCE_CLAIM_REPORTED);
      });
    });

    it("Doesn't show the test policy claims by default", () => {
      cy.wait('@claims');
      cy.get('[data-cy-policy-test="true"]').should('have.length', 0);
    });

    it('Allows to see the test policy claims', () => {
      cy.wait('@claims');
      cy.get('[data-testid="filter-menu"]').click();
      cy.get('[data-testid="policy-test-filter"]').click();
      cy.get('[data-cy-policy-test="true"]').should('have.length.at.least', 1);
    });

    it('Removes the filter when the user clicks on the same filter twice', () => {
      const DAYS_SINCE_LAST_MODIFIED = 7;
      const textString = `${DAYS_SINCE_LAST_MODIFIED} days`;
      cy.wait('@claims');
      // Apply filter
      cy.get('[data-testid="filter-menu"]').click();
      cy.get('[data-testid="modified-at-filter"]').click();
      cy.get(`li:contains("${textString}")`).click();

      // The filter should appear
      cy.contains(textString).should('exist');

      // Click on the filter again
      cy.get(`li:contains("${textString}")`).click();

      // The filter should not appear
      cy.get(`li:contains("${textString}")`).should('not.exist');
    });

    it('Renders a claim when a row is clicked', () => {
      cy.wait('@claims');
      cy.get('table>tbody>tr[data-testid="skeleton-row"]').should(
        'have.length',
        0,
      );
      cy.get('table>tbody>tr').first().click();
      cy.get('[data-testid="claims-details-container"]').should('be.visible');
    });

    it('Shows the list of claims when the list of claims is closed and the user goes back to the list', () => {
      cy.wait('@claims');
      cy.get('table>tbody>tr[data-testid="skeleton-row"]').should(
        'have.length',
        0,
      );
      cy.get('table>tbody>tr').first().click();
      cy.get('[data-testid="claims-details-container"]').should('be.visible');
      cy.get('[data-testid="claim-list-toggle"]').click();
      cy.get('[data-testid="claims-list"]').should('not.be.visible');

      cy.go('back');

      cy.get('[data-testid="claims-list"]').should('be.visible');
    });

    it('Keeps the claim when clicked twice', () => {
      cy.wait('@claims');
      cy.get('table>tbody>tr[data-testid="skeleton-row"]').should(
        'have.length',
        0,
      );
      cy.get('table>tbody>tr').first().click();
      cy.get('[data-testid="claims-details-container"]').should('be.visible');
      cy.get('table>tbody>tr').first().click();
      cy.get('[data-testid="claims-details-container"]').should('be.visible');
    });

    describe('When is mobile', () => {
      beforeEach(() => {
        cy.viewport('iphone-x');
      });

      it('Allows to open another claim after closing the current one', () => {
        cy.wait('@claims');
        cy.get('table>tbody>tr[data-testid="skeleton-row"]').should(
          'have.length',
          0,
        );
        cy.get('table>tbody>tr').first().click();
        cy.get('[data-testid="claims-details-container"]').should('be.visible');
        cy.get('[data-testid="back-to-claims-list"]').click();
        cy.get('table>tbody>tr').first().click();
        cy.get('[data-testid="claims-details-container"]').should('be.visible');
      });
    });
  });
});
