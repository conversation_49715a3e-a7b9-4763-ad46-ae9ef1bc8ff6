import {
  getVariable,
  GraphQlResponse,
  CyHttpMessages,
} from '@nirvana/core/testUtils';
import { subDays } from 'date-fns';
import {
  claimFactory,
  claimQueryFactory,
  claimsQueryFactory,
  noteFactory,
} from '../factories/claimsFactory';
import { ClaimByIdQuery } from '../../../src/types/graphql-types';
import {
  policyFactoryResponse,
  policyFactory,
} from '../factories/policyFactory';
import { DOT_WITH_UNSUPPORTED_CAMERAS_TSP } from './safetyReportHandler';

export const defaultClaimUuid = 'b68e5e0d-a8fc-4611-8467-5fa9f57bb0bb';
export const fixedClaimNotesUuid = '5605965b-2935-4b7a-b126-40c20298e88e';
export const unsupportedTspClaimUuid = '82d6b523-01f6-4712-bdba-0d7a434096ca';
export const failingClaimIQUuid = 'ab75234b-9fb8-4684-b56f-451f26329836';
export const notFetchedClaimUuid = '010ab606-d0b1-42c6-b628-f359cee0e569';
export const htmlNotesClaimUuid = '0d6d3ced-cc24-4c14-b2b8-35b984e8d123';
const DAYS_SINCE_LAST_MODIFIED = 8;
const DAYS_SINCE_LAST_REPORT = 6;
export const claims = () => {
  const claimWithFixedId = claimFactory.build({
    externalId: 'NIT-123456789',
  });

  const claimWithFixedInsuredName = claimFactory.build({
    policy: policyFactory.build({
      insuredName: 'Nirvana Trucking Inc',
    }),
  });

  const claimWithModifietAtOlderThan7Days = claimFactory.build({
    modifiedAt: subDays(new Date(), DAYS_SINCE_LAST_MODIFIED).toISOString(),
  });

  const claimWithReportedAtOlderThan7Days = claimFactory.build({
    reportedAt: subDays(new Date(), DAYS_SINCE_LAST_MODIFIED).toISOString(),
  });
  const claimWithReportedAtNewerThan7Days = claimFactory.build({
    reportedAt: subDays(new Date(), DAYS_SINCE_LAST_REPORT).toISOString(),
  });
  const claimWithTestPolicy = claimFactory.build({
    policy: {
      isTest: true,
    },
  });

  return claimsQueryFactory.build(
    {},
    {
      transient: {
        extraClaims: [
          claimFactory.build({
            claimNumber: 'NOTFETCHED',
            id: notFetchedClaimUuid,
          }),
          claimFactory.build({
            claimNumber: 'FETCHED',
          }),
          claimFactory.build({
            claimNumber: 'FAILING',
            id: failingClaimIQUuid,
          }),
          claimWithTestPolicy,
          claimWithFixedId,
          claimWithFixedInsuredName,
          claimWithModifietAtOlderThan7Days,
          claimWithReportedAtOlderThan7Days,
          claimWithReportedAtNewerThan7Days,
        ],
      },
    },
  );
};

export const claimById = (req: CyHttpMessages.IncomingHttpRequest) => {
  const claimId = getVariable(req, 'id');

  const overrides: Partial<GraphQlResponse<ClaimByIdQuery>> = {};
  if (claimId === fixedClaimNotesUuid) {
    overrides.data = {
      claimById: claimFactory.build({
        id: claimId,
        notes: [
          noteFactory.build({
            category: 'COVERAGE',
          }),
          noteFactory.build({
            category: 'CORRESPONDENCE OUT',
          }),
        ],
      }),
    };
  }
  if (claimId === unsupportedTspClaimUuid) {
    overrides.data = {
      claimById: claimFactory.build({
        id: claimId,
        policy: policyFactory.build({
          insuredDOTNumber: DOT_WITH_UNSUPPORTED_CAMERAS_TSP,
        }),
      }),
    };
  }
  if (claimId === failingClaimIQUuid) {
    overrides.data = {
      claimById: claimFactory.build({
        id: claimId,
        externalId: 'FAILING',
      }),
    };
  }

  if (claimId === htmlNotesClaimUuid) {
    overrides.data = {
      claimById: claimFactory.build({
        id: claimId,
        notes: [
          noteFactory.build({
            value:
              '<strong>Important:</strong> see <a href="https://example.com">details</a>',
            category: 'COVERAGE',
          }),
          noteFactory.build({
            value: 'Please reach out to John Doe <<EMAIL>> for help',
            category: 'CORRESPONDENCE IN',
          }),
        ],
      }),
    };
  }

  return claimQueryFactory.build(overrides, {
    transient: { claimId },
  });
};

export const policyHandler = () => {
  return policyFactoryResponse.build();
};
