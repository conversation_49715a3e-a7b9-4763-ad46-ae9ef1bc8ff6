/* eslint-disable no-magic-numbers */
import { Factory } from 'fishery';
import { faker } from '@faker-js/faker';
import { format } from 'date-fns';
import { GraphQlResponse } from '@nirvana/core/testUtils';
import {
  ClaimByIdQuery,
  Claims,
  ClaimsQuery,
  ClaimStatus,
  ClaimsProvider,
  Note,
} from '../../../src/types/graphql-types';
import { policyFactory } from './policyFactory';

type Claim = Omit<Claims, 'insuredName' | 'insuredDOTNumber'>; // TODO: remove this once we remove the insuredName and insuredDOTNumber fields from the Claims type

type Policy = {
  id: string;
  number: string;
  startDate: string;
  endDate: string;
  claims: Claim[];
};

export type Fleet = {
  id: string;
  dotNumber: string;
  name: string;
  policies: Policy[];
};

export const noteFactory = Factory.define<Note>(() => ({
  createdAt: faker.date.recent().toISOString(),
  value: faker.lorem.sentence(),
  category: faker.lorem.word(),
  claimExternalId: faker.string.uuid(),
  externalId: faker.string.uuid(),
  modifiedAt: faker.date.recent().toISOString(),
  updatedAt: faker.date.recent().toISOString(),
  source: faker.helpers.arrayElement([
    ClaimsProvider.Nars,
    ClaimsProvider.Snapsheet,
  ]),
  id: faker.string.uuid(),
}));

// Claim factory
export const claimFactory = Factory.define<Claim>(({ params }) => {
  const id = params.id ?? faker.string.uuid();
  return {
    id,
    __key: id,
    __typename: 'claims',
    claimNumber: `ClaimNumber${faker.string.numeric(8)}`,
    externalId: `NITNI${faker.string.numeric(8)}`,
    lossDatetime: format(faker.date.past(), "yyyy-MM-dd'T'HH:mm:ss'Z'"),
    adjusterEmail: faker.internet.email(),
    adjusterName: `${faker.person.firstName()} ${faker.person.lastName()}`,
    lineOfBusiness: faker.helpers.arrayElement([
      'Auto Liability - Bodily Injury',
      'Auto Liability - Property Damage',
    ]),
    policyNumber: `NISTK${faker.string.numeric(8)}-${faker.string.numeric(2)}`,
    status: faker.helpers.arrayElement([
      ClaimStatus.Open,
      ClaimStatus.Closed,
      ClaimStatus.Reopen,
    ]),
    source: ClaimsProvider.Nars,
    reportedAt: format(faker.date.past(), "yyyy-MM-dd'T'HH:mm:ss'Z'"),
    modifiedAt: format(faker.date.recent(), "yyyy-MM-dd'T'HH:mm:ss'Z'"),
    reportedBy: 'J Doe',
    feedbacks: [],
    canSubmitFeedback: false,
    notes: noteFactory.buildList(faker.number.int({ min: 10, max: 40 })),
    statusChanges: [],
    hasNotesSinceLastScheduleSummary: true,
    policy: policyFactory.build({
      insuredName: faker.company.name(),
      insuredDOTNumber: faker.string.numeric(8).toString(),
      isTest: false,
    }),
  };
});

type ClaismFactoryTransient = {
  extraClaims: Claim[];
};

export const claimsQueryFactory = Factory.define<
  GraphQlResponse<ClaimsQuery>,
  ClaismFactoryTransient
>(({ transientParams }) => ({
  data: {
    claims: [
      ...(transientParams.extraClaims || []),
      ...claimFactory.buildList(faker.number.int({ min: 30, max: 80 })),
    ],
  },
  errors: null,
}));

export const claimQueryFactory = Factory.define<
  GraphQlResponse<ClaimByIdQuery>,
  { claimId: string }
>(() => ({
  data: {
    claimById: claimFactory.build(),
  },
  errors: null,
}));
