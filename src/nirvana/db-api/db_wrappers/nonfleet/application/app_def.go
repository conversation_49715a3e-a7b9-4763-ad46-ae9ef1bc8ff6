package application

import (
	"time"

	"nirvanatech.com/nirvana/jobber/jtypes"

	"nirvanatech.com/nirvana/policy_common/constants"

	"github.com/google/uuid"

	"nirvanatech.com/nirvana/common-go/short_id"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/enums"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
)

type Application[T AppInfo] struct {
	ID   uuid.UUID
	Info T

	ProgramType   policy_enums.ProgramType
	ShortID       short_id.ShortID
	AgencyID      uuid.UUID
	ProducerID    uuid.UUID
	CreatedBy     uuid.UUID
	UnderwriterID uuid.UUID

	State                enums.AppState
	PageState            enums.PageState
	QuoteSubmissionID    uuid.UUID
	UwSubmissionID       *uuid.UUID
	BindableSubmissionID uuid.UUID

	DeclineReason           string
	StateMetadata           StateMetadata
	EffectiveDate           time.Time
	EffectiveDateTo         time.Time
	CreatedAt               time.Time
	UpdatedAt               time.Time
	MarketerId              uuid.UUID
	DataContextId           *uuid.UUID
	AssignedBD              *uuid.UUID
	InsuranceCarrier        *constants.InsuranceCarrier
	FilingType              *constants.FilingType
	RenewalMetadata         *RenewalMetadata
	PreTelematicsQuoteState *enums.PreTelematicsQuoteState
	ExpressLaneMetadata     *ExpressLaneMetadata
}

type RenewalMetadata struct {
	// OriginalApplicationId corresponds to the first application ever for a given lineage of renewals. In other words,
	// if application A1 resulted in policy P1, which got renewed into policy P2 via application A2, which in turn got
	// renewed into policy P3 via application A3, then for both A2 and A3 this value will hold A1's ID.
	OriginalApplicationId string

	// PreviousApplicationsIds holds the lineage of every application that came before the current application in terms
	// of renewals, and in ascending order according to their creation dates. In other words, if application A1 resulted
	// in policy P1, which got renewed into policy P2 via application A2, which in turn got renewed into policy P3 via
	// application A3, then A3's slice will hold [A1.ID, A2.ID].
	PreviousApplicationsIds *[]string
	// JobRunId stores the ID of the job run that created indications the renewal application. This is used to track the job run
	// and show loader in frontend
	JobRunId *jtypes.JobRunId
}

// IsRenewal returns whether the application is a renewal one or not, irrespective of the renewal algorithm version.
func (appObj *Application[T]) IsRenewal() bool {
	return appObj.RenewalMetadata != nil && appObj.RenewalMetadata.OriginalApplicationId != ""
}

type ExpressLaneMetadata struct {
	State                    enums.ExpressLaneState
	LastStateUpdateTimeStamp time.Time
}

// IsExpressLaneApplication returns true if the express lane state is one of the
// states that qualify the application for Express Lane surfaces and behavior.
// This mirrors the states considered in getExpressLaneValidStates used by
// application review queries.
func (m *ExpressLaneMetadata) IsExpressLaneApplication() bool {
	if m == nil {
		return false
	}
	switch m.State {
	case enums.ExpressLaneStateAppInProgress,
		enums.ExpressLaneStateWaitingForTelematics,
		enums.ExpressLaneStateAutomationCompleted:
		return true
	default:
		return false
	}
}
