package test_utils

import (
	"context"
	"fmt"
	"time"

	"github.com/benb<PERSON><PERSON><PERSON>/clock"
	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/api-server/handlers/forms/generate_inputs"
	application_deps "nirvanatech.com/nirvana/api-server/interceptors/application/deps"
	audit_log_deps "nirvanatech.com/nirvana/api-server/interceptors/audit_log/deps"
	auth_deps "nirvanatech.com/nirvana/api-server/interceptors/auth/deps"
	email_deps "nirvanatech.com/nirvana/api-server/interceptors/emails/deps"
	endorsemenet_deps "nirvanatech.com/nirvana/api-server/interceptors/endorsement/deps"
	external_deps "nirvanatech.com/nirvana/api-server/interceptors/external/deps"
	forms_deps "nirvanatech.com/nirvana/api-server/interceptors/forms/deps"
	nf_app_deps "nirvanatech.com/nirvana/api-server/interceptors/nonfleet_application/deps"
	nf_uw_deps "nirvanatech.com/nirvana/api-server/interceptors/nonfleet_underwriting/deps"
	uw_deps "nirvanatech.com/nirvana/api-server/interceptors/underwriting/deps"
	_ "nirvanatech.com/nirvana/api-server/quoting_jobber/jobs"
	"nirvanatech.com/nirvana/common-go/file_upload_lib/enums"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/random_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/agency"
	"nirvanatech.com/nirvana/db-api/db_wrappers/agency_bd_mapping"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	"nirvanatech.com/nirvana/db-api/db_wrappers/file_upload"
	formEnums "nirvanatech.com/nirvana/db-api/db_wrappers/forms/enums"
	policyEnums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/forms/model"
	"nirvanatech.com/nirvana/infra/authz"
	fgModel "nirvanatech.com/nirvana/policy_common/forms_generator/cmd/model"
	"nirvanatech.com/nirvana/policy_common/forms_generator/compilation"
	"nirvanatech.com/nirvana/policy_common/forms_generator/forms"
	quoting_impl "nirvanatech.com/nirvana/quoting/jobs/impl"
)

const (
	CompanyName                    = "FakeCompany"
	defaultUWFirstName             = "Taylor"
	defaultUWLastName              = "Gosiaco"
	defaultHighestAuthorityUWEmail = "<EMAIL>"
	defaultLowestAuthorityUWEmail  = "<EMAIL>"
	newdefaultUWFirstName          = "Amanda"
	newdefaultUWLastName           = "Hensel"
	newdefaultUWEmail              = "<EMAIL>"
	defaultUWPassword              = "FakePass123"
	defaultSuperUserFirstName      = "Super"
	defaultSuperUserLastName       = "User"
	defaultSuperUserEmail          = "<EMAIL>"
	defaultSuperUserPassword       = "SuperUser@123"
	defaultAgentFirstName          = "Joe"
	defaultAgentLastName           = "Pet"
	defaultAgentEmail              = "<EMAIL>"
	defaultAgentPassword           = "FakePass123"
	defaultSupportFirstName        = "Tyler"
	defaultSupportLastName         = "Durden"
	defaultSupportEmail            = "<EMAIL>"
	defaultSupportPassword         = "fightclub123"
	defaultProducerFirstName       = "Producer"
	defaultProducerLastName        = "Guy"
	defaultProducerEmail           = "<EMAIL>"
	defaultProducerPassword        = "Producer123"
	defaultBDFirstName             = "Ashley"
	defaultBDLastName              = "Estrada"
	defaultBDEmail                 = "<EMAIL>"
	defaultBDPassword              = "BD12345678910"
)

var defaultAgencyId = uuid.New()

func NewApiServerHarness(
	underwritingDeps uw_deps.Deps,
	applicationDeps application_deps.Deps,
	authDeps auth_deps.Deps,
	externalDeps external_deps.Deps,
	formsDeps forms_deps.Deps,
	generateFormInputDeps generate_inputs.GenerateInputsDeps,
	auditLogDeps audit_log_deps.Deps,
	endorsementDeps endorsemenet_deps.Deps,
	nfAppDeps nf_app_deps.Deps,
	nfUWDeps nf_uw_deps.Deps,
	emailDeps email_deps.Deps,
	quotingJobsDeps quoting_impl.Deps,
) (*ApiServerHarness, error) {
	ctx := context.Background()
	clk := clock.NewMock()
	harness := &ApiServerHarness{
		Ctx:                   ctx,
		Clk:                   clk,
		ApplicationDeps:       applicationDeps,
		UnderwritingDeps:      underwritingDeps,
		AuthDeps:              authDeps,
		ExternalDeps:          externalDeps,
		FormsDeps:             formsDeps,
		GenerateFormInputDeps: generateFormInputDeps,
		AuditLogDeps:          auditLogDeps,
		EndorsementDeps:       endorsementDeps,
		NFUWDeps:              nfUWDeps,
		NFAppDeps:             nfAppDeps,
		EmailDeps:             emailDeps,
		QuotingJobsDeps:       quotingJobsDeps,
	}
	harness.Api = api{h: harness}
	harness.Metadata = metadata{h: harness}
	harness.TSPConnManagerHelper = tspConnManagerHelper{h: harness}

	// TestWrappers creators
	harness.TestWrappers = testWrappers{
		AppTestWrapperCreator: appTestWrapperCreator(harness, applicationDeps.ApplicationWrapper),
	}
	harness.TestWrappers.AppReviewTestWrapperCreator = appReviewTestWrapperCreator(harness)
	harness.TestWrappers.QuoteTestWrapperCreator = quoteTestWrapperCreator(ctx, harness)
	return harness, nil
}

// DEPRECATED: Avoid injecting this struct into your tests. It introduces a heavy load of dependencies, causing:
//  1. Large test binaries
//  2. Slow test execution
//
// With Bazel, the goal is to optimize the build and test process so that changes in unrelated modules don't trigger
// rebuilds or retests of unaffected modules. Excessive dependencies defeat Bazel's efficiency benefits.
// Instead, inject only the dependencies necessary for the specific test at hand. If we need to reuse helpers defined by
// this struct, then we can split them into smaller, more focused helpers that can be injected individually.
type ApiServerHarness struct {
	Ctx                   context.Context
	Clk                   clock.Clock
	UnderwritingDeps      uw_deps.Deps
	ApplicationDeps       application_deps.Deps
	AuthDeps              auth_deps.Deps
	ExternalDeps          external_deps.Deps
	FormsDeps             forms_deps.Deps
	GenerateFormInputDeps generate_inputs.GenerateInputsDeps
	AuditLogDeps          audit_log_deps.Deps
	EndorsementDeps       endorsemenet_deps.Deps
	NFAppDeps             nf_app_deps.Deps
	NFUWDeps              nf_uw_deps.Deps
	EmailDeps             email_deps.Deps
	QuotingJobsDeps       quoting_impl.Deps

	// Test helpers
	Metadata             metadata
	Api                  api
	TSPConnManagerHelper tspConnManagerHelper
	TestWrappers         testWrappers
}

func (h *ApiServerHarness) CreateDefaultHighestAuthorityUW() error {
	uw, err := auth.NewUser(uuid.New(), newdefaultUWFirstName, newdefaultUWLastName, newdefaultUWEmail,
		defaultUWPassword, time.Now(), nil, nil, nil)
	if err != nil {
		return err
	}
	seniorUnderwriterRole, err := authz.NewNirvanaRole(uw.ID, authz.SeniorUnderwriterRole, "*")
	if err != nil {
		return err
	}
	level6UnderwriterRole, err := authz.NewNirvanaRole(uw.ID, authz.Level6UnderwriterRole, "*")
	if err != nil {
		return err
	}
	return h.ApplicationDeps.AuthWrapper.CreateUser(h.Ctx, uw, *seniorUnderwriterRole, *level6UnderwriterRole)
}

func (h *ApiServerHarness) CreateDefaultHighestAuthorityUWWithName(
	firstName, lastName, email string,
) (uuid.UUID, error) {
	uw, err := auth.NewUser(uuid.New(), firstName, lastName, email,
		defaultUWPassword, time.Now(), nil, nil, nil)
	if err != nil {
		return uuid.Nil, err
	}
	seniorUnderwriterRole, err := authz.NewNirvanaRole(uw.ID, authz.SeniorUnderwriterRole, "*")
	if err != nil {
		return uuid.Nil, err
	}
	level6UnderwriterRole, err := authz.NewNirvanaRole(uw.ID, authz.Level6UnderwriterRole, "*")
	if err != nil {
		return uuid.Nil, err
	}
	return uw.ID, h.ApplicationDeps.AuthWrapper.CreateUser(h.Ctx, uw, *seniorUnderwriterRole, *level6UnderwriterRole)
}

func (h *ApiServerHarness) CreateDefaultLowestAuthorityUW() error {
	uw, err := auth.NewUser(uuid.New(), defaultUWFirstName, defaultUWLastName, defaultLowestAuthorityUWEmail,
		defaultUWPassword, time.Now(), nil, nil, nil)
	if err != nil {
		return err
	}
	seniorUnderwriterRole, err := authz.NewNirvanaRole(uw.ID, authz.SeniorUnderwriterRole, "*")
	if err != nil {
		return err
	}
	level5UnderwriterRole, err := authz.NewNirvanaRole(uw.ID, authz.Level1UnderwriterRole, "*")
	if err != nil {
		return err
	}
	return h.ApplicationDeps.AuthWrapper.CreateUser(h.Ctx, uw, *seniorUnderwriterRole, *level5UnderwriterRole)
}

func (h *ApiServerHarness) CreateUnderwritingManager() error {
	uw, err := auth.NewUser(uuid.New(), "Manager", "UW", "<EMAIL>",
		defaultUWPassword, time.Now(), nil, nil, nil)
	if err != nil {
		return err
	}
	underwriterManagerRole, err := authz.NewNirvanaRole(uw.ID, authz.UnderwriterManagerRole, "*")
	if err != nil {
		return err
	}
	seniorUnderwriterRole, err := authz.NewNirvanaRole(uw.ID, authz.SeniorUnderwriterRole, "*")
	if err != nil {
		return err
	}
	return h.ApplicationDeps.AuthWrapper.CreateUser(h.Ctx, uw, *underwriterManagerRole, *seniorUnderwriterRole)
}

func (h *ApiServerHarness) CreateDefaultSuperUser() error {
	uw, err := auth.NewUser(uuid.New(), defaultSuperUserFirstName, defaultSuperUserLastName, defaultSuperUserEmail,
		defaultSuperUserPassword, time.Now(), nil, nil, nil)
	if err != nil {
		return err
	}
	role, err := authz.NewNirvanaRole(uw.ID, authz.SuperuserRole, "*")
	if err != nil {
		return err
	}
	return h.ApplicationDeps.AuthWrapper.CreateUser(h.Ctx, uw, *role)
}

func (h *ApiServerHarness) GetDefaultHighestAuthorityUW() (uuid.UUID, error) {
	roles, err := h.ApplicationDeps.AuthWrapper.FetchNirvanaRoles(h.Ctx)
	if err != nil {
		return uuid.Nil, errors.Wrap(err, "unable to fetch nirvana roles")
	}
	for _, r := range roles {
		if r.Group == authz.Level6UnderwriterRole {
			user, err := h.ApplicationDeps.AuthWrapper.FetchAuthzUser(h.Ctx, r.UserID)
			if err != nil {
				return uuid.Nil, errors.Wrapf(err, "unable to fetch authz user %s", r.UserID)
			}
			if user.Email != newdefaultUWEmail {
				continue
			}
			return user.ID, nil
		}
	}
	return uuid.Nil, errors.New("Couldn't find UW")
}

func (h *ApiServerHarness) LoginDefaultHighestAuthorityUW() error {
	session, err := h.ApplicationDeps.AuthWrapper.LoginUser(h.Ctx, newdefaultUWEmail, defaultUWPassword)
	if err != nil {
		return errors.Wrapf(err, "failed to login user with email %s", newdefaultUWEmail)
	}
	userId := session.UserID
	roles, err := h.ApplicationDeps.AuthWrapper.FetchNirvanaRoles(h.Ctx, userId)
	if err != nil {
		return errors.Wrap(err, "unable to fetch nirvana roles")
	}
	if len(roles) > 0 {
		user, err := h.ApplicationDeps.AuthWrapper.FetchUserInfo(h.Ctx, userId)
		if err != nil {
			return errors.Wrap(err, "unable to fetch user")
		}
		const domain = "*"
		h.Ctx, err = addUserToContext(h.Ctx, user, domain, []authz.RoleGroupEnum{authz.SeniorUnderwriterRole, authz.Level6UnderwriterRole}, nil, nil)
		if err != nil {
			return errors.Wrapf(err, "failed to add default highest authority underwriter %+v to context", user)
		}
	}
	return nil
}

func (h *ApiServerHarness) LoginDefaultLowestAuthorityUW() error {
	session, err := h.ApplicationDeps.AuthWrapper.LoginUser(h.Ctx, defaultLowestAuthorityUWEmail, defaultUWPassword)
	if err != nil {
		return errors.Wrapf(err, "failed to login user with email %s", defaultLowestAuthorityUWEmail)
	}
	userId := session.UserID
	roles, err := h.ApplicationDeps.AuthWrapper.FetchNirvanaRoles(h.Ctx, userId)
	if err != nil {
		return errors.Wrap(err, "unable to fetch nirvana roles")
	}
	if len(roles) > 0 {
		user, err := h.ApplicationDeps.AuthWrapper.FetchUserInfo(h.Ctx, userId)
		if err != nil {
			return errors.Wrap(err, "unable to fetch user")
		}
		const domain = "*"
		h.Ctx, err = addUserToContext(h.Ctx, user, domain, []authz.RoleGroupEnum{authz.SeniorUnderwriterRole, authz.Level1UnderwriterRole}, nil, nil)
		if err != nil {
			return errors.Wrapf(err, "failed to add default lowest authority underwriter %+v to context", user)
		}
	}
	return nil
}

func (h *ApiServerHarness) LoginDefaultSuperUser() error {
	session, err := h.ApplicationDeps.AuthWrapper.LoginUser(h.Ctx, defaultSuperUserEmail, defaultSuperUserPassword)
	if err != nil {
		return errors.Wrapf(err, "failed to login user with email %s", defaultSuperUserEmail)
	}
	userId := session.UserID
	roles, err := h.ApplicationDeps.AuthWrapper.FetchNirvanaRoles(h.Ctx, userId)
	if err != nil {
		return errors.Wrap(err, "unable to fetch nirvana roles")
	}
	if len(roles) > 0 {
		user, err := h.ApplicationDeps.AuthWrapper.FetchUserInfo(h.Ctx, userId)
		if err != nil {
			return errors.Wrap(err, "unable to fetch user")
		}
		const domain = "*"
		h.Ctx, err = addUserToContext(h.Ctx, user, domain, []authz.RoleGroupEnum{authz.SuperuserRole}, nil, nil)
		if err != nil {
			return errors.Wrapf(err, "failed to add default underwriter %+v to context", user)
		}
	}
	return nil
}

func (h *ApiServerHarness) CreateAgencyC(agencyId uuid.UUID) (*agency.Agency, error) {
	ag := agency.Agency{
		ID:        agencyId,
		Name:      CompanyName,
		CreatedAt: time.Now(),
	}
	if err := h.ApplicationDeps.AgencyWrapper.InsertAgency(h.Ctx, &ag); err != nil {
		return nil, errors.Wrap(err, "failed to insert agency")
	}
	err := h.CreateBDForAgency(fmt.Sprintf("%<EMAIL>", random_utils.GenerateRandomString(10)), agencyId)
	if err != nil {
		return nil, errors.Wrap(err, "unable to create BD for agency")
	}
	return &ag, nil
}

func (h *ApiServerHarness) CreateAgency() (*agency.Agency, error) {
	return h.CreateAgencyC(uuid.New())
}

func (h *ApiServerHarness) CreateSession(ctx context.Context, email, password string) (*auth.Session, error) {
	session, err := h.ApplicationDeps.AuthWrapper.LoginUser(ctx, email, password)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to login user with email %s", email)
	}
	userId := session.UserID
	user, err := h.ApplicationDeps.AuthWrapper.FetchAuthzUser(ctx, userId)
	if err != nil {
		return nil, errors.Wrap(err, "unable to fetch user")
	}
	h.Ctx = authz.WithUser(h.Ctx, authz.User{
		UserInfo: authz.UserInfo{
			ID: user.ID,
		},
		Roles: user.Roles,
	})
	return session, nil
}

func (h *ApiServerHarness) CreateAgencyAdmin(
	firstName, lastName, email, password string, agencyID uuid.UUID,
) (*auth.User, error) {
	return h.CreateAgencyUserC(firstName, lastName, email, password, agencyID, authz.AgencyAdminRole)
}

func (h *ApiServerHarness) CreateAgencyUserC(
	firstName, lastName, email, password string, agencyID uuid.UUID, role authz.RoleGroupEnum,
) (*auth.User, error) {
	userId := uuid.New()
	newUser, err := auth.NewUser(userId, firstName, lastName, email, password,
		time.Now(), nil, nil, nil)
	if err != nil {
		return nil, errors.Wrap(err, "unable to create new user")
	}
	domain := fmt.Sprintf("/agency/%s/*", agencyID.String())
	agencyRole, err := authz.NewAgencyRole(userId, role, domain, agencyID)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to create agency role")
	}
	err = h.ApplicationDeps.AuthWrapper.CreateUser(h.Ctx, newUser, *agencyRole)
	if err != nil {
		return nil, errors.Wrap(err, "unable to insert user")
	}
	return newUser, nil
}

func (h *ApiServerHarness) CreateDefaultAgency() error {
	_, err := h.CreateAgencyC(defaultAgencyId)
	if err != nil {
		return errors.Wrap(err, "unable to create default agency")
	}
	return nil
}

func (h *ApiServerHarness) CreateDefaultAgent() error {
	_, err := h.CreateAgencyAdmin(defaultAgentFirstName, defaultAgentLastName, defaultAgentEmail,
		defaultAgentPassword, defaultAgencyId)
	if err != nil {
		return errors.Wrap(err, "unable to create default agent")
	}
	_, err = h.CreateSession(h.Ctx, defaultAgentEmail, defaultAgentPassword)
	if err != nil {
		return errors.Wrap(err, "unable to create session for default agent")
	}
	return nil
}

func (h *ApiServerHarness) CreateDefaultSupportUser() error {
	userId := uuid.New()
	newUser, err := auth.NewUser(userId, defaultSupportFirstName,
		defaultSupportLastName, defaultSupportEmail, defaultSupportPassword,
		time.Now(), nil, nil, nil)
	if err != nil {
		return errors.Wrap(err, "unable to create new user")
	}
	const domain = "*"
	supportRole, err := authz.NewNirvanaRole(userId, authz.SeniorSupportRole, domain)
	if err != nil {
		return errors.Wrapf(err, "unable to create agency role")
	}
	err = h.ApplicationDeps.AuthWrapper.CreateUser(h.Ctx, newUser, *supportRole)
	if err != nil {
		return errors.Wrap(err, "unable to insert user")
	}
	return nil
}

func (h *ApiServerHarness) CreateDefaultProducerUser() error {
	_, err := h.CreateAgencyUserC(defaultProducerFirstName,
		defaultProducerLastName, defaultProducerEmail,
		defaultProducerPassword, defaultAgencyId,
		authz.AgencyProducerRole)
	if err != nil {
		return errors.Wrap(err, "unable to create default producer")
	}
	return nil
}

func (h *ApiServerHarness) LoginDefaultSupportUser() error {
	session, err := h.ApplicationDeps.AuthWrapper.LoginUser(h.Ctx, defaultSupportEmail, defaultSupportPassword)
	if err != nil {
		return errors.Wrapf(err, "failed to login user with email %s", defaultSupportEmail)
	}
	userID := session.UserID
	roles, err := h.ApplicationDeps.AuthWrapper.FetchNirvanaRoles(h.Ctx, userID)
	if err != nil {
		return errors.Wrap(err, "unable to fetch nirvana roles")
	}
	if len(roles) > 0 {
		user, err := h.ApplicationDeps.AuthWrapper.FetchUserInfo(h.Ctx, userID)
		if err != nil {
			return errors.Wrap(err, "unable to fetch user")
		}
		const domain = "*"
		h.Ctx, err = addUserToContext(h.Ctx, user, domain,
			[]authz.RoleGroupEnum{authz.SeniorSupportRole}, nil, nil)
		if err != nil {
			return errors.Wrapf(err, "failed to add default support user %+v to context", user)
		}
	}
	return nil
}

func (h *ApiServerHarness) LoginDefaultAgent() error {
	_, err := h.CreateSession(h.Ctx, defaultAgentEmail, defaultAgentPassword)
	if err != nil {
		return errors.Wrap(err, "unable to login default agent")
	}
	return nil
}

func (h *ApiServerHarness) LoginUser(email, password string) error {
	_, err := h.CreateSession(h.Ctx, email, password)
	if err != nil {
		return errors.Wrap(err, "unable to create session for user")
	}
	return nil
}

func (h *ApiServerHarness) LoginDefaultProducer() error {
	_, err := h.CreateSession(h.Ctx, defaultProducerEmail, defaultProducerPassword)
	if err != nil {
		return errors.Wrap(err, "unable to login default producer")
	}
	return nil
}

// CreateRandomUserAndSession creates a new organization and a user.
func (h *ApiServerHarness) CreateRandomUserAndSession() (session *auth.Session, err error) {
	sess, _, _, _, err := h.CreateRandomUserAndSessionC()
	return sess, err
}

// CreateRandomUserAndSessionC creates a new organization and a user, returning
// the email and password used.
func (h *ApiServerHarness) CreateRandomUserAndSessionC() (
	session *auth.Session, email, password string, agencyID *uuid.UUID, err error,
) {
	firstName := random_utils.GenerateRandomString(20)
	lastName := random_utils.GenerateRandomString(20)
	email = firstName + lastName + "@test.com"
	password = random_utils.GenerateRandomString(20)
	agencyID = pointer_utils.UUID(uuid.New())
	_, err = h.CreateAgencyC(*agencyID)
	if err != nil {
		return nil, email, password, nil, errors.Wrap(err, "unable to create agency")
	}
	_, err = h.CreateAgencyAdmin(firstName, lastName, email, password, *agencyID)
	if err != nil {
		return nil, email, password, nil, errors.Wrap(err, "unable to create agency admin")
	}
	session, err = h.CreateSession(h.Ctx, email, password)
	if err != nil {
		return nil, email, password, nil, errors.Wrap(err, "unable to create session")
	}
	return session, email, password, agencyID, nil
}

// addUserToContext adds authz users with agency admin roles
func addUserToContext(
	ctx context.Context,
	user *authz.UserInfo,
	domain string,
	roleGroups []authz.RoleGroupEnum,
	agencyID *uuid.UUID,
	fleetID *uuid.UUID,
) (context.Context, error) {
	var roles []authz.Role
	for _, role := range roleGroups {
		if agencyID != nil {
			agencyRole, err := authz.NewAgencyRole(user.ID, role, domain, *agencyID)
			if err != nil {
				return ctx, errors.Wrapf(err, "failed to create agency role")
			}
			roles = append(roles, *agencyRole)
		} else if fleetID != nil {
			fleetRole, err := authz.NewFleetRole(user.ID, role, domain, *fleetID)
			if err != nil {
				return ctx, errors.Wrapf(err, "failed to create fleet role")
			}
			roles = append(roles, *fleetRole)
		} else {
			nirvanaRole, err := authz.NewNirvanaRole(user.ID, role, domain)
			if err != nil {
				return ctx, errors.Wrapf(err, "failed to create nirvana role")
			}
			roles = append(roles, *nirvanaRole)
		}
	}
	authzUser := authz.User{
		UserInfo: authz.UserInfo{
			ID: user.ID,
		},
		Roles: roles,
	}
	return authz.WithUser(ctx, authzUser), nil
}

// CreateFileHandle creates a NewFileHandle and returns the handleId
func (h *ApiServerHarness) CreateFileHandle(
	key string, accountId uuid.UUID,
	destinationGroup enums.FileDestinationGroup,
) (*uuid.UUID, error) {
	handleID := uuid.New()
	file := file_upload.NewFile(handleID, accountId, key, destinationGroup)
	err := h.ApplicationDeps.FileUploadWrapper.InsertFile(h.Ctx, file)
	if err != nil {
		return nil, err
	}
	return &handleID, nil
}

func (h *ApiServerHarness) GetDefaultSuperUser() (uuid.UUID, error) {
	roles, err := h.ApplicationDeps.AuthWrapper.FetchNirvanaRoles(h.Ctx)
	if err != nil {
		return uuid.Nil, errors.Wrap(err, "unable to fetch nirvana roles")
	}
	for _, r := range roles {
		if r.Group == authz.SuperuserRole {
			user, err := h.ApplicationDeps.AuthWrapper.FetchAuthzUser(h.Ctx, r.UserID)
			if err != nil {
				return uuid.Nil, errors.Wrapf(err, "unable to fetch authz user %s", r.UserID)
			}
			if user.Email != defaultSuperUserEmail {
				continue
			}
			return user.ID, nil
		}
	}
	return uuid.Nil, errors.New("Couldn't find the default super user")
}

func (h *ApiServerHarness) CreateDefaultBD() error {
	randomEmail := fmt.Sprintf("%<EMAIL>", random_utils.GenerateRandomString(10))
	bd, err := auth.NewUser(uuid.New(), defaultBDFirstName, defaultBDLastName, randomEmail,
		defaultBDPassword, time.Now(), nil, nil, nil)
	if err != nil {
		return err
	}
	bdRole, err := authz.NewNirvanaRole(bd.ID, authz.FleetBDRole, "*")
	if err != nil {
		return err
	}

	// Check if the agency already has a BD
	_, err = h.ApplicationDeps.AgencyBDMapping.GetBDForAgency(h.Ctx, defaultAgencyId, policyEnums.ProgramTypeFleet)
	if err == nil {
		return nil
	}

	err = h.ApplicationDeps.AgencyBDMapping.Insert(h.Ctx, &agency_bd_mapping.AgencyBDMapping{
		ID:          uuid.New(),
		AgencyID:    defaultAgencyId,
		AgencyName:  CompanyName,
		UserID:      bd.ID,
		FirstName:   defaultBDFirstName,
		LastName:    defaultBDLastName,
		ProgramType: policyEnums.ProgramTypeFleet,
	})
	if err != nil {
		return err
	}

	return h.ApplicationDeps.AuthWrapper.CreateUser(h.Ctx, bd, *bdRole)
}

func (h *ApiServerHarness) CreateBDForAgency(email string, agencyId uuid.UUID) error {
	BD, err := auth.NewUser(uuid.New(), defaultBDFirstName, defaultBDLastName, email,
		defaultBDPassword, time.Now(), nil, nil, nil)
	if err != nil {
		return err
	}
	BdRole, err := authz.NewNirvanaRole(BD.ID, authz.FleetBDRole, "*")
	if err != nil {
		return err
	}

	// Check if the agency already has a BD
	_, err = h.ApplicationDeps.AgencyBDMapping.GetBDForAgency(h.Ctx, agencyId, policyEnums.ProgramTypeFleet)
	if err == nil {
		return nil
	}

	err = h.ApplicationDeps.AgencyBDMapping.Insert(h.Ctx, &agency_bd_mapping.AgencyBDMapping{
		ID:          uuid.New(),
		AgencyID:    agencyId,
		AgencyName:  CompanyName,
		UserID:      BD.ID,
		FirstName:   defaultBDFirstName,
		LastName:    defaultBDLastName,
		ProgramType: policyEnums.ProgramTypeFleet,
	})
	if err != nil {
		return err
	}

	return h.ApplicationDeps.AuthWrapper.CreateUser(h.Ctx, BD, *BdRole)
}

func (h *ApiServerHarness) CreateDefaultFormSchedules() error {
	appPDF := model.FormSchedule{
		FullFormCode:     "App PDF",
		FormCode:         "App PDF",
		FormName:         "Application PDF",
		OrderCategory:    forms.OrderCategoryM,
		FormTemplateType: formEnums.FormTemplateTypeComposedPDF,
		ApplicabilityRules: []model.FormScheduleApplicabilityRule{{
			CoreRule: &model.CoreRule{
				ApplicableCoveragePackages: map[string]fgModel.PackageType{
					"CoverageAutoLiability": fgModel.AllPackages,
				},
				FormCompilationTypes: []compilation.CompilationType{compilation.CompilationTypeSignaturePacket},
				ProgramTypes: []policyEnums.ProgramType{
					policyEnums.ProgramTypeFleet,
					policyEnums.ProgramTypeNonFleetAdmitted,
				},
			},
		}},
		Metadata: &model.FormScheduleMetadata{},
	}

	quotePDF := model.FormSchedule{
		FullFormCode:     "Quote PDF",
		FormCode:         "Quote PDF",
		FormName:         "Quote PDF",
		OrderCategory:    forms.OrderCategoryA,
		FormTemplateType: formEnums.FormTemplateTypeComposedPDF,
		ApplicabilityRules: []model.FormScheduleApplicabilityRule{{
			CoreRule: &model.CoreRule{
				ApplicableCoveragePackages: map[string]fgModel.PackageType{
					"CoverageAutoLiability": fgModel.AllPackages,
				},
				FormCompilationTypes: []compilation.CompilationType{compilation.CompilationTypeSignaturePacket},
				ProgramTypes: []policyEnums.ProgramType{
					policyEnums.ProgramTypeFleet,
					policyEnums.ProgramTypeNonFleetAdmitted,
				},
			},
		}},
		Metadata: &model.FormScheduleMetadata{},
	}

	affidavitPDF := model.FormSchedule{
		FullFormCode:     "Affidavit PDF",
		FormCode:         "Affidavit PDF",
		FormName:         "Affidavit PDF",
		OrderCategory:    forms.OrderCategoryN,
		FormTemplateType: formEnums.FormTemplateTypeComposedPDF,
		ApplicabilityRules: []model.FormScheduleApplicabilityRule{{
			CoreRule: &model.CoreRule{
				ApplicableCoveragePackages: map[string]fgModel.PackageType{
					"CoverageAutoLiability": fgModel.AllPackages,
				},
				FormCompilationTypes: []compilation.CompilationType{compilation.CompilationTypeSignaturePacket},
				ProgramTypes:         []policyEnums.ProgramType{policyEnums.ProgramTypeFleet},
			},
		}},
		Metadata: &model.FormScheduleMetadata{},
	}

	dummyPolicyForm := model.FormSchedule{
		FullFormCode:     "CA 04 43 11 20", // Getting treated as dummy, ensures that this actually available in S3
		FormCode:         "CA 04 43",
		FormName:         "Dummy",
		OrderCategory:    forms.OrderCategoryN,
		FormTemplateType: formEnums.FormTemplateTypeComposedPDF,
		ApplicabilityRules: []model.FormScheduleApplicabilityRule{{
			CoreRule: &model.CoreRule{
				ApplicableCoveragePackages: map[string]fgModel.PackageType{
					"CoverageAutoLiability":      fgModel.AllPackages,
					"CoverageAutoPhysicalDamage": fgModel.AllPackages,
					"CoverageGeneralLiability":   fgModel.AllPackages,
					"CoverageMotorTruckCargo":    fgModel.AllPackages,
				},
				FormCompilationTypes: []compilation.CompilationType{compilation.CompilationTypePolicy},
				ProgramTypes: []policyEnums.ProgramType{
					policyEnums.ProgramTypeFleet,
					policyEnums.ProgramTypeNonFleetAdmitted,
				},
			},
			ScheduleType: []compilation.ScheduleType{compilation.ScheduleTypeCore},
		}},
		Metadata: &model.FormScheduleMetadata{},
	}

	formSchedules := []model.FormSchedule{appPDF, quotePDF, affidavitPDF, dummyPolicyForm}
	for _, formSchedule := range formSchedules {
		err := h.FormsDeps.FormScheduleManager.CreateFormSchedule(h.Ctx, &formSchedule)
		if err != nil {
			return errors.Wrap(err, "unable to insert form schedule")
		}
	}
	return nil
}
