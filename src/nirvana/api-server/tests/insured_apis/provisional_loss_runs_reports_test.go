package insured_apis

import (
	"context"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"

	"nirvanatech.com/nirvana/api-server/quoting_jobber"
	"nirvanatech.com/nirvana/claims/reporting/loss_runs/db"
	"nirvanatech.com/nirvana/common-go/file_upload_lib/enums"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	policy_builder "nirvanatech.com/nirvana/common-go/test_utils/builders/policy"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	"nirvanatech.com/nirvana/db-api/db_wrappers/file_upload"
	policy_wrapper "nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/agency_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/api_server_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/fixture_utils"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/users_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	"nirvanatech.com/nirvana/jobber/job_utils"
	"nirvanatech.com/nirvana/jobber/jtypes"
	"nirvanatech.com/nirvana/jobber/registry"
	jobber_test_utils "nirvanatech.com/nirvana/jobber/test_utils"
	oapi_insured "nirvanatech.com/nirvana/openapi-specs/api_server_insured"
	oapi_components_insured "nirvanatech.com/nirvana/openapi-specs/components/insured"
	"nirvanatech.com/nirvana/policy_common/constants"
)

const dummyJobId = "DummyGenerateProvisionalLossRunsReport"

func TestProvisionalLossRunsReportsAPIsTestSuite(t *testing.T) {
	suite.Run(t, new(provisionalLossRunsReportsAPIsTestSuite))
}

type provisionalLossRunsReportsAPIsTestSuite struct {
	suite.Suite

	fxapp *fxtest.App

	ApiServer         api_server_fixture.ApiServer
	AuthWrapper       auth.DataWrapper
	FileUploadWrapper file_upload.DataWrapper
	InsuredWrapper    db.Wrapper
	PolicyWrapper     policy_wrapper.DataWrapper
	Registry          *quoting_jobber.Registry
	Jobber            quoting_jobber.Client

	seededLossRunsReports []db.LossRunsReport
	seededPolicies        map[string]policy_wrapper.Policy
	seededJobRunIds       map[string]jtypes.JobRunId
	usersFixture          *users_fixture.UsersFixture
	wantedStatus          map[string]oapi_components_insured.LossRunReportStatus

	testAgencyId uuid.UUID
}

// seedFileUpload creates a file upload object, inserts it into the database, and returns its
// handle ID.
func (p *provisionalLossRunsReportsAPIsTestSuite) seedFileUpload(ctx context.Context) uuid.UUID {
	fileUpload := file_upload.NewFile(
		uuid.New(),
		p.usersFixture.Underwriters[0].ID,
		"random-key",
		enums.FileDestinationGroupUnderwriting)

	err := p.FileUploadWrapper.InsertFile(ctx, fileUpload)
	p.Require().NoError(err)

	return fileUpload.Handle
}

func (p *provisionalLossRunsReportsAPIsTestSuite) seedPolicies(ctx context.Context) {
	policy := policy_builder.
		New().
		WithDefaultMockData().
		WithCompanyName("BOLA'S TRUCKING LLC").
		WithInsuranceCarrier(constants.InsuranceCarrierFalseLake).
		WithPolicyNumber("NISTK", "1339438", 2024).
		WithEffectiveDates(
			time_utils.NewDate(2024, 1, 1).ToTime(),
			time_utils.NewDate(2024, 12, 31).ToTime()).
		WithAgencyId(p.testAgencyId).
		Build()
	p.Require().NoError(p.PolicyWrapper.InsertPolicy(ctx, policy))
	p.seededPolicies["NISTK1339438-24"] = *policy

	policy = policy_builder.
		New().
		WithDefaultMockData().
		WithCompanyName("BOLA'S TRUCKING LLC").
		WithInsuranceCarrier(constants.InsuranceCarrierFalseLake).
		WithPolicyNumber("NISTK", "1339438", 2025).
		WithEffectiveDates(
			time_utils.NewDate(2025, 1, 1).ToTime(),
			time_utils.NewDate(2025, 12, 31).ToTime()).
		WithAgencyId(p.testAgencyId).
		Build()
	p.Require().NoError(p.PolicyWrapper.InsertPolicy(ctx, policy))
	p.seededPolicies["NISTK1339438-25"] = *policy

	policy = policy_builder.
		New().
		WithDefaultMockData().
		WithCompanyName("BOLA'S TRUCKING LLC").
		WithInsuranceCarrier(constants.InsuranceCarrierFalseLake).
		WithPolicyNumber("NISTK", "1334748", 2023).
		WithEffectiveDates(
			time_utils.NewDate(2023, 1, 1).ToTime(),
			time_utils.NewDate(2023, 12, 31).ToTime()).
		WithAgencyId(p.testAgencyId).
		Build()
	p.Require().NoError(p.PolicyWrapper.InsertPolicy(ctx, policy))
	p.seededPolicies["NISTK1334748-23"] = *policy

	policy = policy_builder.
		New().
		WithDefaultMockData().
		WithCompanyName("BOLA'S TRUCKING LLC").
		WithInsuranceCarrier(constants.InsuranceCarrierFalseLake).
		WithPolicyNumber("NISTK", "1334748", 2022).
		WithEffectiveDates(
			time_utils.NewDate(2022, 1, 1).ToTime(),
			time_utils.NewDate(2022, 12, 31).ToTime()).
		WithAgencyId(p.testAgencyId).
		Build()
	p.Require().NoError(p.PolicyWrapper.InsertPolicy(ctx, policy))
	p.seededPolicies["NISTK1334748-22"] = *policy
}

func (p *provisionalLossRunsReportsAPIsTestSuite) seedLossRunsReports(ctx context.Context) {
	p.seededLossRunsReports = []db.LossRunsReport{
		{
			Id:           uuid.New(),
			PolicyNumber: "NISTK1339438-24",
			GeneratedAt:  time.Now().Round(time.Millisecond).UTC(),
			RequestedBy:  p.usersFixture.Superuser.ID,
			FileHandleId: pointer_utils.ToPointer(p.seedFileUpload(ctx)),
		},
		{
			Id:           uuid.New(),
			PolicyNumber: "NISTK1339438-25",
			GeneratedAt:  time.Now().Round(time.Millisecond).UTC(),
			RequestedBy:  p.usersFixture.Underwriters[0].ID,
			FileHandleId: pointer_utils.ToPointer(p.seedFileUpload(ctx)),
			JobRunId:     pointer_utils.ToPointer(p.seededJobRunIds["NISTK1339438-25"]),
		},
		{
			Id:           uuid.New(),
			PolicyNumber: "NISTK1334748-23",
			GeneratedAt:  time.Now().Round(time.Millisecond).UTC(),
			RequestedBy:  p.usersFixture.Underwriters[1].ID,
			FileHandleId: pointer_utils.ToPointer(p.seedFileUpload(ctx)),
		},
		{
			Id:           uuid.New(),
			PolicyNumber: "NISTK1334748-22",
			GeneratedAt:  time.Now().Round(time.Millisecond).UTC(),
			RequestedBy:  p.usersFixture.Underwriters[1].ID,
			JobRunId:     pointer_utils.ToPointer(p.seededJobRunIds["NISTK1334748-22"]),
		},
	}

	// job-less report generation status expectations
	p.wantedStatus["NISTK1339438-24"] = oapi_components_insured.Succeeded
	p.wantedStatus["NISTK1334748-23"] = oapi_components_insured.Succeeded

	for _, report := range p.seededLossRunsReports {
		err := p.InsuredWrapper.InsertLossRunsReport(ctx, report)
		p.Require().NoError(err)
	}
}

func (p *provisionalLossRunsReportsAPIsTestSuite) seedJobRuns(ctx context.Context) {
	testTasker := jobber_test_utils.NewTestTasker("dummy", jobber_test_utils.TestTaskerConfig{
		RetryAttempts:       0,
		AutoSucceedTaskUndo: true,
	})

	dummyJob, err := jtypes.NewJob(
		dummyJobId,
		[]jtypes.TaskCreator[job_utils.EmptyMessageT]{
			testTasker.TaskCreator(),
		},
		job_utils.EmptyUnmarshalMessageFn,
	)
	p.Require().NoError(err)
	p.Require().NoError(registry.AddJob(p.Registry, dummyJob))

	jrId1, err := p.Jobber.AddJobRun(ctx, jtypes.NewAddJobRunParams(
		dummyJobId,
		job_utils.EmptyMessageT{},
		jtypes.NewMetadata(jtypes.Immediate),
	))
	p.Require().NoError(err)

	jrId2, err := p.Jobber.AddJobRun(ctx, jtypes.NewAddJobRunParams(
		dummyJobId,
		job_utils.EmptyMessageT{},
		jtypes.NewMetadata(jtypes.Immediate),
	))
	p.Require().NoError(err)

	testTasker.FailTaskRun(jrId2)
	p.Require().NoError(p.Jobber.WaitForJobRunCompletion(ctx, jrId2))

	testTasker.SucceedTaskRun(jrId1)
	p.Require().NoError(p.Jobber.WaitForJobRunCompletion(ctx, jrId1))

	p.seededJobRunIds["NISTK1339438-25"] = jrId1
	p.wantedStatus["NISTK1339438-25"] = oapi_components_insured.Succeeded

	p.seededJobRunIds["NISTK1334748-22"] = jrId2
	p.wantedStatus["NISTK1334748-22"] = oapi_components_insured.Failed
}

func (p *provisionalLossRunsReportsAPIsTestSuite) SetupTest() {
	var env struct {
		fx.In

		ApiServer         api_server_fixture.ApiServer
		AuthWrapper       auth.DataWrapper
		FileUploadWrapper file_upload.DataWrapper
		InsuredWrapper    db.Wrapper
		PolicyWrapper     policy_wrapper.DataWrapper
		Registry          *quoting_jobber.Registry
		Jobber            quoting_jobber.Client

		UsersFixture  *users_fixture.UsersFixture
		AgencyFixture *agency_fixture.AgencyFixture
	}

	ctx := context.Background()

	p.fxapp = testloader.RequireStart(p.T(), &env)
	p.ApiServer = env.ApiServer
	p.AuthWrapper = env.AuthWrapper
	p.FileUploadWrapper = env.FileUploadWrapper
	p.InsuredWrapper = env.InsuredWrapper
	p.PolicyWrapper = env.PolicyWrapper
	p.Registry = env.Registry
	p.Jobber = env.Jobber

	p.usersFixture = env.UsersFixture
	p.seededPolicies = make(map[string]policy_wrapper.Policy)
	p.seededJobRunIds = make(map[string]jtypes.JobRunId)
	p.wantedStatus = make(map[string]oapi_components_insured.LossRunReportStatus)

	p.testAgencyId = env.AgencyFixture.Agency.ID

	p.seedJobRuns(ctx)
	p.seedPolicies(ctx)
	p.seedLossRunsReports(ctx)
}

func (p *provisionalLossRunsReportsAPIsTestSuite) TearDownTest() {
	p.fxapp.RequireStop()
}

func (p *provisionalLossRunsReportsAPIsTestSuite) Test_GetAllProvisionalLossRunsReports() {
	ctx := context.Background()

	testCases := []struct {
		name       string
		user       users_fixture.User
		want       []db.LossRunsReport
		wantStatus int
	}{
		{
			name:       "get all provisional loss runs reports as underwriter",
			user:       p.usersFixture.Underwriters[0],
			want:       p.seededLossRunsReports,
			wantStatus: http.StatusOK,
		},
		{
			name:       "get all provisional loss runs reports as superuser",
			user:       p.usersFixture.Superuser,
			want:       p.seededLossRunsReports,
			wantStatus: http.StatusOK,
		},
		{
			name:       "get all provisional loss runs reports as claims admin",
			user:       p.usersFixture.ClaimsAdmin,
			want:       p.seededLossRunsReports,
			wantStatus: http.StatusOK,
		},
		{
			name:       "unauthorized",
			user:       p.usersFixture.FleetReader,
			wantStatus: http.StatusForbidden,
		},
	}

	for _, tc := range testCases {
		p.Run(tc.name, func() {
			insuredApiClient := p.ApiServer.InsuredClient(&tc.user)

			res, err := insuredApiClient.GetAllProvisionalLossRunsReports(ctx)
			p.Require().NoError(err)

			p.Require().Equal(tc.wantStatus, res.StatusCode)
			if res.StatusCode >= 400 {
				return
			}

			parsedResponse := fixture_utils.ReadResponse[[]oapi_components_insured.ProvisionalLossRunsReport](
				p.T(), res)
			p.Require().Len(*parsedResponse, len(tc.want))

			for _, wantReport := range tc.want {
				gotReport := slice_utils.Find(*parsedResponse, func(report oapi_components_insured.ProvisionalLossRunsReport) bool {
					return report.Id == wantReport.Id.String()
				})

				p.Require().NotNil(gotReport, "report not found in response")
				p.Equal(wantReport.PolicyNumber, gotReport.Policy.PolicyNumber)
				wantPolicy := p.seededPolicies[gotReport.Policy.PolicyNumber]
				p.Require().NotNil(wantPolicy)
				p.Equal(wantPolicy.InsuredName, gotReport.Policy.InsuredName)
				p.Equal(strconv.FormatInt(wantPolicy.CompanyInfo.DOTNumber, 10), gotReport.Policy.DotNumber)
				p.Equal(wantReport.GeneratedAt, gotReport.GeneratedAt)
				requester, err := p.AuthWrapper.FetchAuthzUser(ctx, wantReport.RequestedBy)
				p.Require().NoError(err)
				p.Equal(requester.FullName(), gotReport.RequestedBy)
				p.Equal(p.wantedStatus[wantReport.PolicyNumber], gotReport.Status)
			}
		})
	}
}

func (p *provisionalLossRunsReportsAPIsTestSuite) Test_GenerateProvisionalLossRunsReport() {
	testCases := []struct {
		name       string
		user       users_fixture.User
		body       oapi_insured.GenerateProvisionalLossRunsReportJSONRequestBody
		wantStatus int
	}{
		{
			name: "unauthorized",
			user: p.usersFixture.FleetReader,
			body: oapi_insured.GenerateProvisionalLossRunsReportJSONRequestBody{
				PolicyNumbers: []string{"NISTK1339438-24"},
			},
			wantStatus: http.StatusForbidden,
		},
		{
			name: "policy not found",
			user: p.usersFixture.Underwriters[0],
			body: oapi_insured.GenerateProvisionalLossRunsReportJSONRequestBody{
				PolicyNumbers: []string{"NISTK1339438-99", "NISTK1339438-24"},
			},
			wantStatus: http.StatusNotFound,
		},
		{
			name: "valid request",
			user: p.usersFixture.Underwriters[1],
			body: oapi_insured.GenerateProvisionalLossRunsReportJSONRequestBody{
				PolicyNumbers: []string{"NISTK1339438-24", "NISTK1339438-25"},
			},
			wantStatus: http.StatusNoContent,
		},
	}

	for _, tt := range testCases {
		p.Run(tt.name, func() {
			ctx := context.Background()
			insuredApiClient := p.ApiServer.InsuredClient(&tt.user)

			res, err := insuredApiClient.GenerateProvisionalLossRunsReport(ctx, tt.body)
			p.Require().NoError(err)
			p.Require().Equal(tt.wantStatus, res.StatusCode)
		})
	}
}
