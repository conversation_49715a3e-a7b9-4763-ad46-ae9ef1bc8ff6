package uw_apis

import (
	"context"
	"net/http"
	"testing"
	"time"

	"nirvanatech.com/nirvana/infra/fx/testfixtures/lni_fixture"

	"github.com/google/uuid"
	"github.com/jinzhu/copier"
	"github.com/launchdarkly/go-sdk-common/v3/ldvalue"
	openapi_types "github.com/oapi-codegen/runtime/types"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"

	"nirvanatech.com/nirvana/api-server/helpers"
	external_deps "nirvanatech.com/nirvana/api-server/interceptors/external/deps"
	"nirvanatech.com/nirvana/api-server/test_utils"
	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/str_utils"
	common_test_utils "nirvanatech.com/nirvana/common-go/test_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/external_data_management/data_processing/vin/testing/testfixtures"
	nhtsaEnums "nirvanatech.com/nirvana/external_data_management/nhtsa/enums"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/api_server_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/application_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/application_review_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/emailer_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/feature_store_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/fixture_utils"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/fmcsa_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/users_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
	oapi_uw "nirvanatech.com/nirvana/openapi-specs/components/underwriting"
	state_enums "nirvanatech.com/nirvana/quoting/app_state_machine/enums"
	"nirvanatech.com/nirvana/rating/rtypes"
	"nirvanatech.com/nirvana/telematics"
	test_utils2 "nirvanatech.com/nirvana/telematics/data_platform/test_utils"
	"nirvanatech.com/nirvana/underwriting/app_review/actions/reasons"
)

// Important Note!
// Tests in this package are taking close to 250s to run, we have a timeout of 300s per package in CI.
// So avoid adding new tests to this package and instead create separate package for new tests similar to what we have done for action_test and app_review_list_test.
// General guideline for creating new package is - tests for related APIs can reside in a single package and unrelated APIs can have separate test packages.

// TestUwApisNonIsolatedTestSuite runs all tests in the same suite to share a single fx testloader App.
// This approach improves test performance by reducing setup/teardown time.
// If isolated environment is required for a test, consider creating a separate suite.
func TestUwApisNonIsolatedTestSuite(t *testing.T) {
	suite.Run(t, new(uwApisTestSuite))
}

type uwApisTestSuite struct {
	common_test_utils.StatsHandler
	suite.Suite
	h                 *test_utils.ApiServerHarness
	deps              external_deps.Deps
	fxapp             *fxtest.App
	FeatureFlagClient feature_flag_lib.Client
	apiServer         api_server_fixture.ApiServer
	usersFixture      *users_fixture.UsersFixture
	appReviewFixture  *application_review_fixture.ApplicationReviewsFixture
	*feature_store_fixture.FeatureStoreFixture
}

func (s *uwApisTestSuite) TearDownSuite() {
	s.fxapp.RequireStop()
}

func (s *uwApisTestSuite) SetupSuite() {
	var env struct {
		fx.In
		Harness *test_utils.ApiServerHarness
		*emailer_fixture.NoopEmailerFixture
		*feature_store_fixture.FeatureStoreFixture
		*lni_fixture.ActiveOrPendingInsuranceFixture
		*fmcsa_fixture.FmcsaFixture
		*lni_fixture.VersionFixture
		*testfixtures.NhtsaToIsoMappingV1Fixture
		FeatureFlagClient feature_flag_lib.Client
		UsersFixture      *users_fixture.UsersFixture
		ApiServer         api_server_fixture.ApiServer
		ApplicationReview *application_review_fixture.ApplicationReviewsFixture
	}
	s.fxapp = testloader.RequireStart(s.T(), &env)
	s.h = env.Harness
	s.FeatureFlagClient = env.FeatureFlagClient
	s.Require().NoError(s.h.CreateDefaultLowestAuthorityUW())
	err := s.h.CreateDefaultAgency()
	s.Require().Nil(err)
	_, err = s.h.CreateDefaultHighestAuthorityUWWithName("Karleigh", "Schroeder", "<EMAIL>")
	s.Require().NoError(err)
	err = s.h.CreateDefaultAgent()
	s.Require().Nil(err)
	ffClient := s.FeatureFlagClient.(*feature_flag_lib.MockClient)
	ffClient.SetValue(feature_flag_lib.FeatureValidateDuplicateApplications, ldvalue.Bool(false))
	s.deps = s.h.ExternalDeps
	s.apiServer = env.ApiServer
	s.usersFixture = env.UsersFixture
	s.Require().NoError(s.h.CreateDefaultFormSchedules())
	s.appReviewFixture = env.ApplicationReview
	s.Require().NoError(s.h.LoginDefaultLowestAuthorityUW())
}

func (s *uwApisTestSuite) TestGetVinProblems() {
	session, err := s.h.CreateRandomUserAndSession()
	s.Require().Nil(err)
	s.Require().NotNil(session)

	appTestWrapper, err := s.h.TestWrappers.AppTestWrapperCreator()
	s.Require().NoError(err)
	app, err := appTestWrapper.CreateApplication()
	s.Require().NoError(err)

	// Login as UW to access GetVinProblems API
	err = s.h.LoginDefaultHighestAuthorityUW()
	s.Require().NoError(err)

	// No problems yet since VINs haven't been processed
	problems, err := s.h.Api.GetVinProblems(app.ID)
	s.Require().Nil(err)
	s.Require().Empty(*problems)

	_, err = appTestWrapper.GenerateAndSelectIndicationC(
		test_utils.GenerateAndSelectIndicationParams{Coverages: test_utils.CoveragesALAndAPD})
	s.Require().NoError(err)

	// After running indication, we should populate some problems
	problems, err = s.h.Api.GetVinProblems(app.ID)
	s.Require().Nil(err)
	s.Require().NotEmpty(*problems)
}

func (s *uwApisTestSuite) TestSkipVinProblem() {
	session, err := s.h.CreateRandomUserAndSession()
	s.Require().Nil(err)
	s.Require().NotNil(session)

	ffClient, ok := s.deps.FeatureFlagClient.(*feature_flag_lib.MockClient)
	s.Require().True(ok)
	ffClient.SetValue(feature_flag_lib.FeatureVehiclesServiceVINDecoder, ldvalue.Bool(false))

	appTestWrapper, err := s.h.TestWrappers.AppTestWrapperCreator()
	s.Require().NoError(err)
	app, err := appTestWrapper.CreateApplication()
	s.Require().NoError(err)

	app.ModelPinConfig.Application.Flags.UseVehiclesServiceForVINProblems = false
	s.Require().NoError(s.h.UnderwritingDeps.ApplicationWrapper.UpdateApp(context.Background(), app.ID, func(a application.Application) (application.Application, error) {
		a.ModelPinConfig.Application.Flags.UseVehiclesServiceForVINProblems = false
		return a, nil
	}))

	_, err = appTestWrapper.GenerateAndSelectIndicationC(
		test_utils.GenerateAndSelectIndicationParams{Coverages: test_utils.CoveragesALAndAPD})
	s.Require().NoError(err)

	// Login as UW to access Get & Put VinProblems API
	err = s.h.LoginDefaultHighestAuthorityUW()
	s.Require().NoError(err)

	// After running indication, we should populate some problems
	problems, err := s.h.Api.GetVinProblems(app.ID)
	s.Require().Nil(err)
	s.Require().NotEmpty(*problems)

	// Set problems to skip
	skipped := oapi_uw.ApplicationVinProblems{
		Problems: []oapi_uw.ApplicationVinProblemRecord{
			// All necessary fields manually fixed up
			{
				FixedBodyClass: helpers.VehicleBodyClassPtr("Truck-Tractor"),
				Vin:            "123",
				ShouldSkip:     pointer_utils.Bool(true),
			},
			// Not a complete fix
			{
				Vin:        "456",
				ShouldSkip: pointer_utils.Bool(true),
				IsReviewed: true,
			},
		},
	}

	err = s.h.Api.PutVinProblems(app.ID, skipped)
	s.Require().NoError(err)

	// After running indication, we should populate some problems
	problems, err = s.h.Api.GetVinProblems(app.ID)
	s.Require().NoError(err)
	vin123 := getForVin(problems, "123")
	vin456 := getForVin(problems, "456")
	s.Require().True(vin123.IsResolved && *vin123.ShouldSkip)
	s.Require().True(vin456.IsResolved && *vin456.ShouldSkip)
}

func getForVin(
	probs *oapi_uw.ApplicationVinProblems, vin string,
) *oapi_uw.ApplicationVinProblemRecord {
	for i := range probs.Problems {
		if (probs.Problems)[i].Vin == vin {
			return &(probs.Problems)[i]
		}
	}
	return nil
}

func (s *uwApisTestSuite) TestRollback() {
	appReviewTestWrapper, err := s.h.TestWrappers.AppReviewTestWrapperCreator()
	s.Require().Nil(err)
	s.Require().NotNil(appReviewTestWrapper)

	// Login As UW
	err = s.h.LoginDefaultHighestAuthorityUW()
	s.Require().NoError(err)

	reviewObj, err := s.h.UnderwritingDeps.ApplicationReviewWrapper.GetReview(
		s.h.Ctx, appReviewTestWrapper.AppReviewId)
	s.Require().Nil(err)
	appObj, err := s.h.UnderwritingDeps.ApplicationWrapper.GetAppById(s.h.Ctx, reviewObj.ApplicationID)
	s.Require().Nil(err)
	dummyComment := "Dummy Comment"

	// Case1:
	// Pending->Approved		ConfirmBindableQuote    NoError
	// Approved->Pending		Rollback                NoError

	// Valid Request as appReview currently in Pending State
	_, err = appReviewTestWrapper.CreateAndApproveBindableQuote()
	s.Require().Nil(err)
	appObj, err = s.h.UnderwritingDeps.ApplicationWrapper.GetAppById(s.h.Ctx, reviewObj.ApplicationID)
	s.Require().Nil(err)
	s.Require().Equal(state_enums.AppStateApproved, appObj.State)

	// Valid Request as appReview currently in Approved State
	err = s.h.Api.RollbackAppReview(appReviewTestWrapper.AppReviewId, dummyComment)
	s.Require().Nil(err)
	appObj, err = s.h.UnderwritingDeps.ApplicationWrapper.GetAppById(s.h.Ctx, reviewObj.ApplicationID)
	s.Require().Nil(err)
	s.Require().Equal(state_enums.AppStateUnderUWReview, appObj.State)

	// Case2:
	// Pending->Pending		Rollback		Error
	// Pending->Decline		Decline			NoError
	// Decline->Pending		Rollback		NoError
	// Pending->Pending		Rollback		Error
	// We also check if the ASM is rolling back or not by checking the appState

	// Invalid Request as appReview currently in Pending State
	err = s.h.Api.RollbackAppReview(appReviewTestWrapper.AppReviewId, dummyComment)
	// TODO check why this was not failing
	// s.Require().NoError(err)
	s.Require().Error(err)
	// Valid Request
	err = s.h.Api.DeclineAppReview(appReviewTestWrapper.AppReviewId)
	s.Require().Nil(err)
	appObj, err = s.h.UnderwritingDeps.ApplicationWrapper.GetAppById(s.h.Ctx, reviewObj.ApplicationID)
	s.Require().Nil(err)
	s.Require().Equal(state_enums.AppStateDeclined, appObj.State)

	// Valid Request as appReview currently in Declined State
	err = s.h.Api.RollbackAppReview(appReviewTestWrapper.AppReviewId, dummyComment)
	s.Require().Nil(err)
	appObj, err = s.h.UnderwritingDeps.ApplicationWrapper.GetAppById(s.h.Ctx, reviewObj.ApplicationID)
	s.Require().Nil(err)
	s.Require().Equal(state_enums.AppStateUnderUWReview, appObj.State)

	// Invalid Request as appReview currently in Pending State
	err = s.h.Api.RollbackAppReview(appReviewTestWrapper.AppReviewId, dummyComment)
	s.Require().Error(err)
	appObj, err = s.h.UnderwritingDeps.ApplicationWrapper.GetAppById(s.h.Ctx, reviewObj.ApplicationID)
	s.Require().Nil(err)
	s.Require().Equal(state_enums.AppStateUnderUWReview, appObj.State)

	// Case3: Checking if metadata gets updated.
	// New app review
	appReviewTestWrapper, err = s.h.TestWrappers.AppReviewTestWrapperCreator()
	s.Require().NoError(err)
	err = s.h.LoginDefaultHighestAuthorityUW()
	s.Require().Nil(err)

	reviewObj, err = s.h.UnderwritingDeps.ApplicationReviewWrapper.GetReview(
		s.h.Ctx, appReviewTestWrapper.AppReviewId)
	s.Require().Nil(err)

	err = s.h.Api.DeclineAppReview(appReviewTestWrapper.AppReviewId)
	s.Require().Nil(err)
	appObj, err = s.h.UnderwritingDeps.ApplicationWrapper.GetAppById(s.h.Ctx, reviewObj.ApplicationID)
	s.Require().Nil(err)
	s.Require().NotEqual(appObj.StateMetadata.Description, dummyComment)

	// Send a rollback comment and checks if it is updated or not
	err = s.h.Api.RollbackAppReview(appReviewTestWrapper.AppReviewId, dummyComment)
	s.Require().Nil(err)
	appObj, err = s.h.UnderwritingDeps.ApplicationWrapper.GetAppById(s.h.Ctx, reviewObj.ApplicationID)
	s.Require().Nil(err)
	s.Require().Equal(appObj.StateMetadata.Description, dummyComment)
	s.Require().Equal(state_enums.AppStateDeclined, appObj.StateMetadata.PreviousState)
}

// Test PostApplicationReviewDocument and GetApplicationReviewDocument
func (s *uwApisTestSuite) TestUwDocumentUploadDownload() {
	// Create an app till UW Review state
	appReviewTestWrapper, err := s.h.TestWrappers.AppReviewTestWrapperCreator()
	s.Require().Nil(err)
	s.Require().NotNil(appReviewTestWrapper)

	// Login As UW
	err = s.h.LoginDefaultHighestAuthorityUW()
	s.Require().NoError(err)

	// test all invalid upload request scenarios
	_, err = s.h.Api.PostApplicationReviewDocuments(
		test_utils.FileUploadScenarioExceededFileSize,
		appReviewTestWrapper.AppReviewId)
	s.Require().NotNil(err)
	s.Require().Contains(err.Error(),
		"Error: Exceeded file size limit")

	_, err = s.h.Api.PostApplicationReviewDocuments(
		test_utils.FileUploadScenarioUnrecognizedFileType,
		appReviewTestWrapper.AppReviewId)
	s.Require().NotNil(err)
	s.Require().Contains(err.Error(), "Error: unrecognized file type")

	_, err = s.h.Api.PostApplicationReviewDocuments(
		test_utils.FileUploadScenarioUnrecognizedFileDestinationGroup,
		appReviewTestWrapper.AppReviewId)
	s.Require().NotNil(err)
	s.Require().Contains(err.Error(),
		"Error: unrecognized file destination group")

	// Valid Upload Case
	fileResp, err := s.h.Api.PostApplicationReviewDocuments(
		test_utils.FileUploadScenarioValid,
		appReviewTestWrapper.AppReviewId)
	s.Require().Nil(err)

	// test valid file download
	// Check if the above posted document is fetched or not
	documents, err := s.h.Api.GetApplicationReviewDocument(appReviewTestWrapper.AppReviewId)
	s.Require().Nil(err)
	s.Require().Condition(
		func() bool {
			for _, f := range documents.Files {
				if *f.Handle == fileResp.Handle {
					return true
				}
			}
			return false
		},
	)
	// Test endpoint state checks
	validStates := map[uw.ApplicationReviewState]bool{
		uw.ApplicationReviewStatePending: true,
	}
	type testCase struct {
		state uw.ApplicationReviewState
	}
	var testCases []testCase
	for _, grade := range uw.ApplicationReviewStateValues() {
		testCases = append(testCases, testCase{
			state: grade,
		})
	}
	s.Require().NoError(err)
	// Test for all test cases and ensure that the action works only for valid states
	for _, tc := range testCases {
		s.T().Run(tc.state.String(), func(t *testing.T) {
			// update the state of the application review.
			err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, appReviewTestWrapper.AppReviewId,
				func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
					review.State = tc.state
					return review, nil
				})

			s.Require().NoError(err)
			// Try to update app review and ensure that it fails if the state is not valid
			if _, ok := validStates[tc.state]; !ok {
				_, err = s.h.Api.PostApplicationReviewDocuments(test_utils.FileUploadScenarioValid,
					appReviewTestWrapper.AppReviewId)
				s.Require().Error(err)
				return
			}
			// For valid states ensure that the action works
			_, err = s.h.Api.PostApplicationReviewDocuments(test_utils.FileUploadScenarioValid,
				appReviewTestWrapper.AppReviewId)
			s.Require().NoError(err)
		})
	}

	// test authz for file download
	_, err = s.h.CreateRandomUserAndSession()
	s.Require().Nil(err)
	_, err = s.h.Api.GetApplicationReviewDocument(appReviewTestWrapper.AppReviewId)
	s.Require().NotNil(err)
	s.Require().Contains(err.Error(), "unauthorized to access")
}

// TestQuoteGeneration basic test for a quote generation
func (s *uwApisTestSuite) TestBindableQuoteGeneration() {
	wrapper, err := s.h.TestWrappers.AppReviewTestWrapperCreator()
	s.Require().NoError(err)
	s.Require().NoError(s.h.LoginDefaultHighestAuthorityUW())
	// Set the pull mvr flag as true for the
	// update_submission_for_uw job
	err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateQuoteInfo(s.h.Ctx,
		wrapper.AppReviewId,
		func(input uw.QuoteInfo) (uw.QuoteInfo, error) {
			input.PullMvrs = pointer_utils.Bool(true)
			return input, nil
		},
	)
	s.Require().NoError(err)
	quote, err := wrapper.UpdateAndGetQuote()
	s.Require().NoError(err)
	bindableQuote, err := wrapper.CreateAndGetBindableQuote()
	s.Require().NoError(err)
	// TODO: replace for AppReviewValidator
	s.T().Skip("SKIPPING DB_TEST due to race condition with quote.TotalPremium not being updated here. This is a jobber race condition.")
	s.Require().Equal(quote.TotalPremium, bindableQuote.TotalPremium)
	s.Require().Equal(quote.PreDiscountTotalPremium, bindableQuote.PreDiscountTotalPremium)
	s.Require().Equal(quote.SubtotalPremium, bindableQuote.SubtotalPremium)
	s.Require().Equal(quote.SafetyDiscountPercentage, bindableQuote.SafetyDiscountPercentage)
	s.Require().Equal(quote.SafetyDiscount, bindableQuote.SafetyDiscount)
	s.Require().Equal(quote.AutoLiabilityPremium, bindableQuote.AutoLiabilityPremium)
	s.Require().Equal(quote.AutoPhysicalDamagePremium, bindableQuote.AutoPhysicalDamagePremium)
	s.Require().Equal(quote.FlatCharges, bindableQuote.FlatCharges)
}

func (s *uwApisTestSuite) setupPendingAppReviewWithFixture() (*uuid.UUID, error) {
	appReview := s.appReviewFixture.ApplicationReview
	id, err := uuid.Parse(appReview.Id)
	if err != nil {
		return nil, err
	}
	// always update the state to pending
	err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, appReview.Id,
		func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
			review.State = uw.ApplicationReviewStatePending
			return review, nil
		})
	if err != nil {
		return nil, err
	}
	s.Require().NoError(s.h.LoginDefaultLowestAuthorityUW())
	return &id, nil
}

// TODO: Add test to verify that quote model succeeds after manual problem fixing

func (s *uwApisTestSuite) TestUpdateAccountGrade() {
	validStates := map[uw.ApplicationReviewState]bool{
		uw.ApplicationReviewStatePending: true,
	}
	type testCase struct {
		grade string
		state uw.ApplicationReviewState
	}
	var testCases []testCase
	for _, grade := range uw.ApplicationReviewStateValues() {
		testCases = append(testCases, testCase{
			grade: "A",
			state: grade,
		})
	}
	reviewId, err := s.setupPendingAppReviewWithFixture()
	s.Require().NoError(err)
	// Test for all test cases and ensure that the action works only for valid states
	for _, tc := range testCases {
		s.T().Run(tc.state.String(), func(t *testing.T) {
			// update the state of the application review.
			err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, reviewId.String(),
				func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
					review.State = tc.state
					return review, nil
				})
			s.Require().NoError(err)
			// Try to update the account grade and ensure that it fails if the state is not valid
			if _, ok := validStates[tc.state]; !ok {
				err = s.h.Api.UpdateAccountGrade(reviewId.String(), tc.grade)
				s.Require().Error(err)
				return
			}
			// For valid states ensure that the action works
			err = s.h.Api.UpdateAccountGrade(reviewId.String(), tc.grade)
			s.Require().NoError(err)
			// Ensure that the account grade is updated
			appReview, err := s.h.UnderwritingDeps.ApplicationReviewWrapper.GetReview(s.h.Ctx, reviewId.String())
			s.Require().NoError(err)
			s.Require().Equal(appReview.AccountGrade, tc.grade)
		})
	}
}

func (s *uwApisTestSuite) TestMarkMVRPullAction() {
	validStates := map[uw.ApplicationReviewState]bool{
		uw.ApplicationReviewStatePending: true,
	}
	type testCase struct {
		state uw.ApplicationReviewState
	}
	var testCases []testCase
	for _, grade := range uw.ApplicationReviewStateValues() {
		testCases = append(testCases, testCase{
			state: grade,
		})
	}
	// Create app and app review
	appReviewId, err := s.setupPendingAppReviewWithFixture()
	s.Require().NoError(err)
	for _, tc := range testCases {
		s.T().Run(tc.state.String(), func(t *testing.T) {
			// update the state of the app review to the current state
			err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, appReviewId.String(), func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
				review.State = tc.state
				return review, nil
			})
			s.Require().NoError(err)
			// Try to mark MVR pull and ensure that it fails if the state is not valid
			if _, ok := validStates[tc.state]; !ok {
				err = s.h.Api.MarkMVRPull(appReviewId.String())
				s.Require().Error(err)
				return
			}
			// For valid states ensure that the action works
			err = s.h.Api.MarkMVRPull(appReviewId.String())
			s.Require().NoError(err)
			// Ensure that MVR Flag is set to true
			appReview, err := s.h.UnderwritingDeps.ApplicationReviewWrapper.GetReview(s.h.Ctx, appReviewId.String())
			s.Require().NoError(err)
			s.Require().NotNil(appReview.QuoteInfo.PullMvrs)
			s.Require().True(*appReview.QuoteInfo.PullMvrs)
		})
	}
}

func (s *uwApisTestSuite) TestBoardsInfo() {
	validStates := map[uw.ApplicationReviewState]bool{
		uw.ApplicationReviewStatePending: true,
	}
	type testCase struct {
		state uw.ApplicationReviewState
	}
	var testCases []testCase
	for _, grade := range uw.ApplicationReviewStateValues() {
		testCases = append(testCases, testCase{
			state: grade,
		})
	}
	reviewId, err := s.setupPendingAppReviewWithFixture()
	s.Require().NoError(err)
	timeNow := time.Now()
	form := oapi_uw.ApplicationReviewBoardsInfo{
		CommitTimestamp: pointer_utils.Time(timeNow),
		Version:         pointer_utils.String("1.0.0"),
	}
	// Test for all test cases and ensure that the action works only for valid states
	for _, tc := range testCases {
		s.T().Run(tc.state.String(), func(t *testing.T) {
			// update the state of the application review.
			err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, reviewId.String(),
				func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
					review.State = tc.state
					return review, nil
				})
			s.Require().NoError(err)
			// Try to update the account grade and ensure that it fails if the state is not valid
			if _, ok := validStates[tc.state]; !ok {
				err = s.h.Api.UpdateBoardsInfo(reviewId.String(), form)
				s.Require().Error(err)
				return
			}
			// For valid states ensure that the action works
			err = s.h.Api.UpdateBoardsInfo(reviewId.String(), form)
			s.Require().NoError(err)
			// Ensure that the account grade is updated
			appReview, err := s.h.UnderwritingDeps.ApplicationReviewWrapper.GetReview(s.h.Ctx, reviewId.String())
			s.Require().NoError(err)
			s.Require().Equal(appReview.BoardsInfo.CommitTimestamp.Format(time_utils.ISOLayout),
				timeNow.Format(time_utils.ISOLayout))
			s.Require().Equal(appReview.BoardsInfo.Version, form.Version)
		})
	}
}

func (s *uwApisTestSuite) TestUpdateCoverage() {
	validStates := map[uw.ApplicationReviewState]bool{
		uw.ApplicationReviewStatePending: true,
	}
	type testCase struct {
		state uw.ApplicationReviewState
	}
	var testCases []testCase
	for _, grade := range uw.ApplicationReviewStateValues() {
		testCases = append(testCases, testCase{
			state: grade,
		})
	}
	reviewId, err := s.setupPendingAppReviewWithFixture()
	s.Require().NoError(err)
	form := oapi_uw.ApplicationReviewCoverages{
		AutoLiability:      nil,
		AutoPhysicalDamage: nil,
		CombinedCoverages:  nil,
		GeneralLiability:   nil,
		Meta:               nil,
		MotorTruckCargo:    nil,
	}
	// Test for all test cases and ensure that the action works only for valid states
	for _, tc := range testCases {
		s.T().Run(tc.state.String(), func(t *testing.T) {
			// update the state of the application review.
			err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, reviewId.String(),
				func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
					review.State = tc.state
					return review, nil
				})
			s.Require().NoError(err)
			// Try to update the account grade and ensure that it fails if the state is not valid
			if _, ok := validStates[tc.state]; !ok {
				err = s.h.Api.UpdateCoverages(reviewId.String(), form)
				s.Require().Error(err)
				return
			}
			// For valid states ensure that the action works
			err = s.h.Api.UpdateCoverages(reviewId.String(), form)
			s.Require().NoError(err)
		})
	}
}

func (s *uwApisTestSuite) TestUpdateDriversList() {
	validStates := map[uw.ApplicationReviewState]bool{
		uw.ApplicationReviewStatePending: true,
	}
	type testCase struct {
		state uw.ApplicationReviewState
	}
	var testCases []testCase
	for _, grade := range uw.ApplicationReviewStateValues() {
		testCases = append(testCases, testCase{
			state: grade,
		})
	}
	reviewId, err := s.setupPendingAppReviewWithFixture()
	s.Require().NoError(err)
	form := oapi_uw.ApplicationReviewDriversListForm{
		Data: nil,
		Meta: nil,
	}
	// Test for all test cases and ensure that the action works only for valid states
	for _, tc := range testCases {
		s.T().Run(tc.state.String(), func(t *testing.T) {
			// update the state of the application review.
			err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, reviewId.String(),
				func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
					review.State = tc.state
					return review, nil
				})
			s.Require().NoError(err)
			// Try to update the account grade and ensure that it fails if the state is not valid
			if _, ok := validStates[tc.state]; !ok {
				err = s.h.Api.UpdateDriversList(reviewId.String(), form)
				s.Require().Error(err)
				return
			}
			// For valid states ensure that the action works
			err = s.h.Api.UpdateDriversList(reviewId.String(), form)
			s.Require().NoError(err)
		})
	}
}

func (s *uwApisTestSuite) TestUpdateEquipmentUnits() {
	validStates := map[uw.ApplicationReviewState]bool{
		uw.ApplicationReviewStatePending: true,
	}
	type testCase struct {
		state uw.ApplicationReviewState
	}
	var testCases []testCase
	for _, grade := range uw.ApplicationReviewStateValues() {
		testCases = append(testCases, testCase{
			state: grade,
		})
	}
	reviewId, err := s.setupPendingAppReviewWithFixture()
	s.Require().NoError(err)
	form := oapi_uw.ApplicationReviewEquipmentsUnitsForm{}
	// Test for all test cases and ensure that the action works only for valid states
	for _, tc := range testCases {
		s.T().Run(tc.state.String(), func(t *testing.T) {
			// update the state of the application review.
			err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, reviewId.String(),
				func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
					review.State = tc.state
					return review, nil
				})
			s.Require().NoError(err)
			// Try to update the account grade and ensure that it fails if the state is not valid
			if _, ok := validStates[tc.state]; !ok {
				err = s.h.Api.UpdateEquipmentUnits(reviewId.String(), form)
				s.Require().Error(err)
				return
			}
			// For valid states ensure that the action works
			err = s.h.Api.UpdateEquipmentUnits(reviewId.String(), form)
			s.Require().NoError(err)
		})
	}
}

func (s *uwApisTestSuite) TestUpdateFinancialsData() {
	validStates := map[uw.ApplicationReviewState]bool{
		uw.ApplicationReviewStatePending: true,
	}
	type testCase struct {
		state uw.ApplicationReviewState
	}
	var testCases []testCase
	for _, grade := range uw.ApplicationReviewStateValues() {
		testCases = append(testCases, testCase{
			state: grade,
		})
	}
	reviewId, err := s.setupPendingAppReviewWithFixture()
	s.Require().NoError(err)
	form := oapi_uw.ApplicationReviewFinancialsData{}
	// Test for all test cases and ensure that the action works only for valid states
	for _, tc := range testCases {
		s.T().Run(tc.state.String(), func(t *testing.T) {
			// update the state of the application review.
			err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, reviewId.String(),
				func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
					review.State = tc.state
					return review, nil
				})
			s.Require().NoError(err)
			// Try to update the account grade and ensure that it fails if the state is not valid
			if _, ok := validStates[tc.state]; !ok {
				err = s.h.Api.UpdateUpdateFinancialsData(reviewId.String(), form)
				s.Require().Error(err)
				return
			}
			// For valid states ensure that the action works
			err = s.h.Api.UpdateUpdateFinancialsData(reviewId.String(), form)
			s.Require().NoError(err)
		})
	}
}

func (s *uwApisTestSuite) TestUpdateLargeLosses() {
	validStates := map[uw.ApplicationReviewState]bool{
		uw.ApplicationReviewStatePending: true,
	}
	type testCase struct {
		state uw.ApplicationReviewState
	}
	var testCases []testCase
	for _, grade := range uw.ApplicationReviewStateValues() {
		testCases = append(testCases, testCase{
			state: grade,
		})
	}
	reviewId, err := s.setupPendingAppReviewWithFixture()
	s.Require().NoError(err)
	val := struct {
		Value []oapi_uw.ApplicationReviewLargeLossesItem
	}{}
	val.Value = []oapi_uw.ApplicationReviewLargeLossesItem{
		{
			CoverageType: "Auto Liability",
			Date:         openapi_types.Date{Time: time.Date(2021, time.December, 30, 0, 0, 0, 0, time.UTC)},
			Description:  pointer_utils.String("test"),
			LossIncurred: 0,
		},
	}
	form := oapi_uw.ApplicationReviewLargeLossesForm{
		Data: (*struct {
			Value []oapi_uw.ApplicationReviewLargeLossesItem `json:"value"`
		})(&val),
		Meta: &oapi_uw.ApplicationReviewWidgetMeta{},
	}
	lossSummaryRecords := []application.LossRunSummaryRecord{
		{
			LossIncurred:          11388,
			NumberOfClaims:        2,
			NumberOfPowerUnits:    15,
			PolicyPeriodStartDate: time.Date(2021, time.December, 31, 0, 0, 0, 0, time.UTC),
			PolicyPeriodEndDate:   time.Date(2022, time.December, 31, 0, 0, 0, 0, time.UTC),
		},
		{
			LossIncurred:          10667,
			NumberOfClaims:        2,
			NumberOfPowerUnits:    15,
			PolicyPeriodStartDate: time.Date(2020, time.December, 31, 0, 0, 0, 0, time.UTC),
			PolicyPeriodEndDate:   time.Date(2021, time.December, 31, 0, 0, 0, 0, time.UTC),
		},
		{
			LossIncurred:          15000,
			NumberOfClaims:        15,
			NumberOfPowerUnits:    15,
			PolicyPeriodStartDate: time.Date(2019, time.December, 31, 0, 0, 0, 0, time.UTC),
			PolicyPeriodEndDate:   time.Date(2020, time.December, 31, 0, 0, 0, 0, time.UTC),
		},
		{
			LossIncurred:          0,
			NumberOfClaims:        0,
			NumberOfPowerUnits:    0,
			PolicyPeriodStartDate: time.Date(2018, time.December, 31, 0, 0, 0, 0, time.UTC),
			PolicyPeriodEndDate:   time.Date(2019, time.December, 31, 0, 0, 0, 0, time.UTC),
		},
		{
			LossIncurred:          0,
			NumberOfClaims:        0,
			NumberOfPowerUnits:    0,
			PolicyPeriodStartDate: time.Date(2017, time.December, 31, 0, 0, 0, 0, time.UTC),
			PolicyPeriodEndDate:   time.Date(2018, time.December, 31, 0, 0, 0, 0, time.UTC),
		},
	}
	lossRunPerCoverage := []application.LossRunSummaryPerCoverage{
		{
			CoverageType: app_enums.CoverageAutoLiability,
			Summary:      lossSummaryRecords,
		},
		{
			CoverageType: app_enums.CoverageGeneralLiability,
			Summary:      lossSummaryRecords,
		},
		{
			CoverageType: app_enums.CoverageAutoPhysicalDamage,
			Summary:      lossSummaryRecords,
		},
		{
			CoverageType: app_enums.CoverageMotorTruckCargo,
			Summary:      lossSummaryRecords,
		},
		{
			CoverageType: app_enums.CoverageBlanketAdditional,
			Summary:      lossSummaryRecords,
		},
	}
	// Test for all test cases and ensure that the action works only for valid states
	for _, tc := range testCases {
		s.T().Run(tc.state.String(), func(t *testing.T) {
			// update the state of the application review.
			err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, reviewId.String(),
				func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
					review.State = tc.state
					return review, nil
				})
			rev, err := s.h.UnderwritingDeps.ApplicationReviewWrapper.GetReview(s.h.Ctx, reviewId.String())
			s.Require().NoError(err)
			err = s.h.UnderwritingDeps.ApplicationWrapper.UpdateSub(s.h.Ctx, rev.Submission.ID,
				func(object application.SubmissionObject) (application.SubmissionObject, error) {
					if object.LossInfo == nil {
						object.LossInfo = &application.LossInfo{}
					}
					object.LossInfo.LossRunSummary = lossRunPerCoverage
					return object, nil
				})
			s.Require().NoError(err)
			// Try to update the account grade and ensure that it fails if the state is not valid
			if _, ok := validStates[tc.state]; !ok {
				err = s.h.Api.UpdateLargeLosses(reviewId.String(), form)
				s.Require().Error(err)
				return
			}
			// For valid states ensure that the action works
			err = s.h.Api.UpdateLargeLosses(reviewId.String(), form)
			s.Require().NoError(err)
		})
	}
}

func (s *uwApisTestSuite) TestUpdateLossSummary() {
	validStates := map[uw.ApplicationReviewState]bool{
		uw.ApplicationReviewStatePending: true,
	}
	type testCase struct {
		state uw.ApplicationReviewState
	}
	var testCases []testCase
	for _, grade := range uw.ApplicationReviewStateValues() {
		testCases = append(testCases, testCase{
			state: grade,
		})
	}
	reviewId, err := s.setupPendingAppReviewWithFixture()
	s.Require().NoError(err)
	form := oapi_uw.ApplicationReviewLossSummaryForm{}
	// Test for all test cases and ensure that the action works only for valid states
	for _, tc := range testCases {
		s.T().Run(tc.state.String(), func(t *testing.T) {
			// update the state of the application review.
			err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, reviewId.String(),
				func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
					review.State = tc.state
					return review, nil
				})
			s.Require().NoError(err)
			// Try to update the account grade and ensure that it fails if the state is not valid
			if _, ok := validStates[tc.state]; !ok {
				err = s.h.Api.UpdateLossSummary(reviewId.String(), form)
				s.Require().Error(err)
				return
			}
			// For valid states ensure that the action works
			err = s.h.Api.UpdateLossSummary(reviewId.String(), form)
			s.Require().NoError(err)
		})
	}
}

func (s *uwApisTestSuite) TestUpdateNotes() {
	validStates := map[uw.ApplicationReviewState]bool{
		uw.ApplicationReviewStatePending: true,
	}
	type testCase struct {
		state uw.ApplicationReviewState
	}
	var testCases []testCase
	for _, grade := range uw.ApplicationReviewStateValues() {
		testCases = append(testCases, testCase{
			state: grade,
		})
	}
	reviewId, err := s.setupPendingAppReviewWithFixture()
	s.Require().NoError(err)
	form := oapi_uw.ApplicationReviewNotes{}
	// Test for all test cases and ensure that the action works only for valid states
	for _, tc := range testCases {
		s.T().Run(tc.state.String(), func(t *testing.T) {
			// update the state of the application review.
			err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, reviewId.String(),
				func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
					review.State = tc.state
					return review, nil
				})

			s.Require().NoError(err)
			// Try to update the account grade and ensure that it fails if the state is not valid
			if _, ok := validStates[tc.state]; !ok {
				err = s.h.Api.UpdateNotes(reviewId.String(), form)
				s.Require().Error(err)
				return
			}
			// For valid states ensure that the action works
			err = s.h.Api.UpdateNotes(reviewId.String(), form)
			s.Require().NoError(err)
		})
	}
}

func (s *uwApisTestSuite) TestUpdateCommodities() {
	validStates := map[uw.ApplicationReviewState]bool{
		uw.ApplicationReviewStatePending: true,
	}
	type testCase struct {
		state uw.ApplicationReviewState
	}
	var testCases []testCase
	for _, grade := range uw.ApplicationReviewStateValues() {
		testCases = append(testCases, testCase{
			state: grade,
		})
	}
	reviewId, err := s.setupPendingAppReviewWithFixture()
	s.Require().NoError(err)
	form := oapi_uw.ApplicationReviewOperationsCommoditiesForm{}
	// Test for all test cases and ensure that the action works only for valid states
	for _, tc := range testCases {
		s.T().Run(tc.state.String(), func(t *testing.T) {
			// update the state of the application review.
			err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, reviewId.String(),
				func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
					review.State = tc.state
					return review, nil
				})

			s.Require().NoError(err)
			// Try to update commodities and ensure that it fails if the state is not valid
			if _, ok := validStates[tc.state]; !ok {
				err = s.h.Api.UpdateCommodities(reviewId.String(), form)
				s.Require().Error(err)
				return
			}
			// For valid states ensure that the action works
			err = s.h.Api.UpdateCommodities(reviewId.String(), form)
			s.Require().NoError(err)
		})
	}
}

func (s *uwApisTestSuite) TestFleetHistory() {
	validStates := map[uw.ApplicationReviewState]bool{
		uw.ApplicationReviewStatePending: true,
	}
	type testCase struct {
		state uw.ApplicationReviewState
	}
	var testCases []testCase
	for _, grade := range uw.ApplicationReviewStateValues() {
		testCases = append(testCases, testCase{
			state: grade,
		})
	}
	reviewId, err := s.setupPendingAppReviewWithFixture()
	s.Require().NoError(err)
	form := oapi_uw.ApplicationReviewOperationsFleetHistoryForm{}
	// Test for all test cases and ensure that the action works only for valid states
	for _, tc := range testCases {
		s.T().Run(tc.state.String(), func(t *testing.T) {
			// update the state of the application review.
			err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, reviewId.String(),
				func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
					review.State = tc.state
					return review, nil
				})

			s.Require().NoError(err)
			// Try to update fleet history and ensure that it fails if the state is not valid
			if _, ok := validStates[tc.state]; !ok {
				err = s.h.Api.UpdateFleetHistory(reviewId.String(), form)
				s.Require().Error(err)
				return
			}
			// For valid states ensure that the action works
			err = s.h.Api.UpdateFleetHistory(reviewId.String(), form)
			s.Require().NoError(err)
		})
	}
}

func (s *uwApisTestSuite) TestGaragingLocation() {
	validStates := map[uw.ApplicationReviewState]bool{
		uw.ApplicationReviewStatePending: true,
	}
	type testCase struct {
		state uw.ApplicationReviewState
	}
	var testCases []testCase
	for _, grade := range uw.ApplicationReviewStateValues() {
		testCases = append(testCases, testCase{
			state: grade,
		})
	}
	reviewId, err := s.setupPendingAppReviewWithFixture()
	s.Require().NoError(err)
	form := oapi_uw.ApplicationReviewOperationsGaragingLocationForm{}
	// Test for all test cases and ensure that the action works only for valid states
	for _, tc := range testCases {
		s.T().Run(tc.state.String(), func(t *testing.T) {
			// update the state of the application review.
			err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, reviewId.String(),
				func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
					review.State = tc.state
					return review, nil
				})

			s.Require().NoError(err)
			// Try to update garaging location and ensure that it fails if the state is not valid
			if _, ok := validStates[tc.state]; !ok {
				err = s.h.Api.UpdateGaragingLocation(reviewId.String(), form)
				s.Require().Error(err)
				return
			}
			// For valid states ensure that the action works
			err = s.h.Api.UpdateGaragingLocation(reviewId.String(), form)
			s.Require().NoError(err)
		})
	}
}

func (s *uwApisTestSuite) TestOperatingClasses() {
	validStates := map[uw.ApplicationReviewState]bool{
		uw.ApplicationReviewStatePending: true,
	}
	type testCase struct {
		state uw.ApplicationReviewState
	}
	var testCases []testCase
	for _, grade := range uw.ApplicationReviewStateValues() {
		testCases = append(testCases, testCase{
			state: grade,
		})
	}
	reviewId, err := s.setupPendingAppReviewWithFixture()
	s.Require().NoError(err)
	form := oapi_uw.ApplicationReviewOperationsOperatingClassesForm{}
	// Test for all test cases and ensure that the action works only for valid states
	for _, tc := range testCases {
		s.T().Run(tc.state.String(), func(t *testing.T) {
			// update the state of the application review.
			err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, reviewId.String(),
				func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
					review.State = tc.state
					return review, nil
				})

			s.Require().NoError(err)
			// Try to update operating classes and ensure that it fails if the state is not valid
			if _, ok := validStates[tc.state]; !ok {
				err = s.h.Api.UpdateOperatingClasses(reviewId.String(), form)
				s.Require().Error(err)
				return
			}
			// For valid states ensure that the action works
			err = s.h.Api.UpdateOperatingClasses(reviewId.String(), form)
			s.Require().NoError(err)
		})
	}
}

func (s *uwApisTestSuite) TestRadiusOfOperation() {
	validStates := map[uw.ApplicationReviewState]bool{
		uw.ApplicationReviewStatePending: true,
	}
	type testCase struct {
		state uw.ApplicationReviewState
	}
	var testCases []testCase
	for _, grade := range uw.ApplicationReviewStateValues() {
		testCases = append(testCases, testCase{
			state: grade,
		})
	}
	reviewId, err := s.setupPendingAppReviewWithFixture()
	s.Require().NoError(err)
	form := oapi_uw.ApplicationReviewOperationsRadiusOfOperationForm{}
	// Test for all test cases and ensure that the action works only for valid states
	for _, tc := range testCases {
		s.T().Run(tc.state.String(), func(t *testing.T) {
			// update the state of the application review.
			err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, reviewId.String(),
				func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
					review.State = tc.state
					return review, nil
				})

			s.Require().NoError(err)
			// Try to update radius of operation and ensure that it fails if the state is not valid
			if _, ok := validStates[tc.state]; !ok {
				err = s.h.Api.UpdateRadiusOfOperation(reviewId.String(), form)
				s.Require().Error(err)
				return
			}
			// For valid states ensure that the action works
			err = s.h.Api.UpdateRadiusOfOperation(reviewId.String(), form)
			s.Require().NoError(err)
		})
	}
}

func (s *uwApisTestSuite) TestUpdateYearsInBuisness() {
	validStates := map[uw.ApplicationReviewState]bool{
		uw.ApplicationReviewStatePending: true,
	}
	type testCase struct {
		state uw.ApplicationReviewState
	}
	var testCases []testCase
	for _, grade := range uw.ApplicationReviewStateValues() {
		testCases = append(testCases, testCase{
			state: grade,
		})
	}
	reviewId, err := s.setupPendingAppReviewWithFixture()
	s.Require().NoError(err)
	form := oapi_uw.ApplicationReviewOperationsYearsInBusinessForm{}
	// Test for all test cases and ensure that the action works only for valid states
	for _, tc := range testCases {
		s.T().Run(tc.state.String(), func(t *testing.T) {
			// update the state of the application review.
			err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, reviewId.String(),
				func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
					review.State = tc.state
					return review, nil
				})

			s.Require().NoError(err)
			// Try to update years in business and ensure that it fails if the state is not valid
			if _, ok := validStates[tc.state]; !ok {
				err = s.h.Api.UpdateYearsInBusiness(reviewId.String(), form)
				s.Require().Error(err)
				return
			}
			// For valid states ensure that the action works
			err = s.h.Api.UpdateYearsInBusiness(reviewId.String(), form)
			s.Require().NoError(err)
		})
	}
}

func (s *uwApisTestSuite) TestNegotiatedRates() {
	validStates := map[uw.ApplicationReviewState]bool{
		uw.ApplicationReviewStatePending: true,
	}
	type testCase struct {
		state uw.ApplicationReviewState
	}
	var testCases []testCase
	for _, grade := range uw.ApplicationReviewStateValues() {
		testCases = append(testCases, testCase{
			state: grade,
		})
	}
	reviewId, err := s.setupPendingAppReviewWithFixture()
	s.Require().NoError(err)
	form := oapi_uw.ApplicationReviewNegotiatedRates{
		BaseLimitPremium: nil,
		Coverages:        nil,
		Details: oapi_uw.ApplicationReviewNegotiatedRatesDetails{
			AlNegotiatedRate:   1000,
			AlTraditionalRate:  nil,
			ApdNegotiatedRate:  pointer_utils.Int64(1000),
			ApdTraditionalRate: nil,
			CaseDescription:    "",
			Exemption:          "NegotiatedRatesExemptionRuleFifteen",
		},
		IsNegotiatedRatesApplicable: true,
		IsNegotiatedRatesApplied:    false,
		Rules:                       nil,
		ThresholdPremium:            nil,
	}
	// Test for all test cases and ensure that the action works only for valid states
	for _, tc := range testCases {
		s.T().Run(tc.state.String(), func(t *testing.T) {
			// update the state of the application review.
			err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, reviewId.String(),
				func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
					review.State = tc.state
					review.Overrides.NegotiatedRates = &application.NegotiatedRates{IsNegotiatedRatesApplicable: true}
					return review, nil
				})

			s.Require().NoError(err)
			// Try to update years in business and ensure that it fails if the state is not valid
			if _, ok := validStates[tc.state]; !ok {
				err = s.h.Api.UpdateNegotiatedRates(reviewId.String(), form)
				s.Require().Error(err)
				return
			}
			// For valid states ensure that the action works
			err = s.h.Api.UpdateNegotiatedRates(reviewId.String(), form)
			s.Require().NoError(err)
		})
	}
}

func (s *uwApisTestSuite) permissionExists(permissions *[]oapi_uw.ApplicationReviewUserPermission, permission oapi_uw.ApplicationReviewUserPermission) bool {
	for _, p := range *permissions {
		if p == permission {
			return true
		}
	}
	return false
}

//FIXME: test is flaky
//func (s *uwApisTestSuite) TestGetUnderwriterPermissions() {
//	// Create app and app review
//	appReviewWrapper, err := s.h.TestWrappers.AppReviewTestWrapperCreator()
//	s.Require().NoError(err)
//	// We add negotiated rates to check for authority permission
//	err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, appReviewWrapper.AppReviewId,
//		func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
//			review.Overrides.NegotiatedRates = &application.NegotiatedRates{
//				IsNegotiatedRatesApplied: true,
//			}
//			return review, nil
//		})
//	s.Require().NoError(err)
//
//	testCases := []struct {
//		name                string
//		user                func() error
//		expectedPermissions oapi_uw.ApplicationReviewUserPermissions
//	}{
//		{
//			name: "Lowest Authority UW",
//			user: s.h.LoginDefaultLowestAuthorityUW,
//			expectedPermissions: oapi_uw.ApplicationReviewUserPermissions{
//				Permissions: []oapi_uw.ApplicationReviewUserPermission{
//					{
//						Status: false,
//						Type:   oapi_uw.ApprovalPermission,
//					},
//				},
//				Status: false,
//			},
//		},
//		{
//			name: "Highest Authority UW",
//			user: s.h.LoginDefaultHighestAuthorityUW,
//			expectedPermissions: oapi_uw.ApplicationReviewUserPermissions{
//				Permissions: []oapi_uw.ApplicationReviewUserPermission{
//					{
//						Status: true,
//						Type:   oapi_uw.ApprovalPermission,
//					},
//				},
//				Status: true,
//			},
//		},
//	}
//
//	// Run through test cases
//	for _, tc := range testCases {
//		s.Run(tc.name, func() {
//			s.Require().NoError(tc.user())
//			permissions, err := s.h.Api.GetApplicationReviewPermissions(appReviewWrapper.AppReviewId)
//			s.Require().NoError(err)
//			s.Require().Equal(tc.expectedPermissions.Status, permissions.Status)
//		})
//	}
//}

func (s *uwApisTestSuite) TestUpdatePackageType() {
	validStates := map[uw.ApplicationReviewState]bool{
		uw.ApplicationReviewStatePending: true,
	}
	type testCase struct {
		state uw.ApplicationReviewState
	}
	var testCases []testCase
	for _, grade := range uw.ApplicationReviewStateValues() {
		testCases = append(testCases, testCase{
			state: grade,
		})
	}
	reviewId, err := s.setupPendingAppReviewWithFixture()
	s.Require().NoError(err)
	form := oapi_uw.ApplicationReviewPackageType{
		PackageType: oapi_uw.ApplicationReviewPackageTypeValue(str_utils.PrettyEnumString(
			app_enums.IndicationOptionTagBasic.String(), "IndicationOptionTag")),
	}
	// Test for all test cases and ensure that the action works only for valid states
	for _, tc := range testCases {
		s.T().Run(tc.state.String(), func(t *testing.T) {
			// update the state of the application review.
			err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, reviewId.String(),
				func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
					review.State = tc.state
					return review, nil
				})

			s.Require().NoError(err)
			// Try to update package type and ensure that it fails if the state is not valid
			if _, ok := validStates[tc.state]; !ok {
				err = s.h.Api.UpdatePackageType(reviewId.String(), form)
				s.Require().Error(err)
				return
			}
			// For valid states ensure that the action works
			err = s.h.Api.UpdatePackageType(reviewId.String(), form)
			s.Require().NoError(err)
		})
	}
}

func (s *uwApisTestSuite) TestUpdateSafetyBasicScoreThreshold() {
	validStates := map[uw.ApplicationReviewState]bool{
		uw.ApplicationReviewStatePending: true,
	}
	type testCase struct {
		state uw.ApplicationReviewState
	}
	var testCases []testCase
	for _, grade := range uw.ApplicationReviewStateValues() {
		testCases = append(testCases, testCase{
			state: grade,
		})
	}
	reviewId, err := s.setupPendingAppReviewWithFixture()
	s.Require().NoError(err)
	form := oapi_uw.ApplicationReviewSafetyBasicScoreThresholdForm{}
	// Test for all test cases and ensure that the action works only for valid states
	for _, tc := range testCases {
		s.T().Run(tc.state.String(), func(t *testing.T) {
			// update the state of the application review.
			err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, reviewId.String(),
				func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
					review.State = tc.state
					return review, nil
				})
			s.Require().NoError(err)
			// Try to update basic score threshold and ensure that it fails if the state is not valid
			if _, ok := validStates[tc.state]; !ok {
				err = s.h.Api.UpdateBasicScoreThreshold(reviewId.String(), form)
				s.Require().Error(err)
				return
			}
			// For valid states ensure that the action works
			err = s.h.Api.UpdateBasicScoreThreshold(reviewId.String(), form)
			s.Require().NoError(err)
		})
	}
}

func (s *uwApisTestSuite) TestUpdateSafetyBasicScoreTrend() {
	validStates := map[uw.ApplicationReviewState]bool{
		uw.ApplicationReviewStatePending: true,
	}
	type testCase struct {
		state uw.ApplicationReviewState
	}
	var testCases []testCase
	for _, grade := range uw.ApplicationReviewStateValues() {
		testCases = append(testCases, testCase{
			state: grade,
		})
	}
	reviewId, err := s.setupPendingAppReviewWithFixture()
	s.Require().NoError(err)
	form := oapi_uw.ApplicationReviewSafetyBasicScoreThresholdForm{}
	// Test for all test cases and ensure that the action works only for valid states
	for _, tc := range testCases {
		s.T().Run(tc.state.String(), func(t *testing.T) {
			// update the state of the application review.
			err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, reviewId.String(),
				func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
					review.State = tc.state
					return review, nil
				})

			s.Require().NoError(err)
			// Try to update basic score threshold and ensure that it fails if the state is not valid
			if _, ok := validStates[tc.state]; !ok {
				err = s.h.Api.UpdateBasicScoreTrend(reviewId.String(), form)
				s.Require().Error(err)
				return
			}
			// For valid states ensure that the action works
			err = s.h.Api.UpdateBasicScoreTrend(reviewId.String(), form)
			s.Require().NoError(err)
		})
	}
}

func (s *uwApisTestSuite) TestUpdateCrashRecord() {
	validStates := map[uw.ApplicationReviewState]bool{
		uw.ApplicationReviewStatePending: true,
	}
	type testCase struct {
		state uw.ApplicationReviewState
	}
	var testCases []testCase
	for _, grade := range uw.ApplicationReviewStateValues() {
		testCases = append(testCases, testCase{
			state: grade,
		})
	}
	reviewId, err := s.setupPendingAppReviewWithFixture()
	s.Require().NoError(err)
	form := oapi_uw.ApplicationReviewSafetyCrashRecordForm{}
	// Test for all test cases and ensure that the action works only for valid states
	for _, tc := range testCases {
		s.T().Run(tc.state.String(), func(t *testing.T) {
			// update the state of the application review.
			err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, reviewId.String(),
				func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
					review.State = tc.state
					return review, nil
				})

			s.Require().NoError(err)
			// Try to update crash record and ensure that it fails if the state is not valid
			if _, ok := validStates[tc.state]; !ok {
				err = s.h.Api.UpdateCrashRecord(reviewId.String(), form)
				s.Require().Error(err)
				return
			}
			// For valid states ensure that the action works
			err = s.h.Api.UpdateCrashRecord(reviewId.String(), form)
			s.Require().NoError(err)
		})
	}
}

func (s *uwApisTestSuite) TestUpdateDotRating() {
	validStates := map[uw.ApplicationReviewState]bool{
		uw.ApplicationReviewStatePending: true,
	}
	type testCase struct {
		state uw.ApplicationReviewState
	}
	var testCases []testCase
	for _, grade := range uw.ApplicationReviewStateValues() {
		testCases = append(testCases, testCase{
			state: grade,
		})
	}
	reviewId, err := s.setupPendingAppReviewWithFixture()
	s.Require().NoError(err)
	form := oapi_uw.ApplicationReviewSafetyDotRatingForm{}
	// Test for all test cases and ensure that the action works only for valid states
	for _, tc := range testCases {
		s.T().Run(tc.state.String(), func(t *testing.T) {
			// update the state of the application review.
			err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, reviewId.String(),
				func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
					review.State = tc.state
					return review, nil
				})

			s.Require().NoError(err)
			// Try to update basic score threshold and ensure that it fails if the state is not valid
			if _, ok := validStates[tc.state]; !ok {
				err = s.h.Api.UpdateBasicDotRating(reviewId.String(), form)
				s.Require().Error(err)
				return
			}
			// For valid states ensure that the action works
			err = s.h.Api.UpdateBasicDotRating(reviewId.String(), form)
			s.Require().NoError(err)
		})
	}
}

func (s *uwApisTestSuite) TestUpdateOOSViolations() {
	validStates := map[uw.ApplicationReviewState]bool{
		uw.ApplicationReviewStatePending: true,
	}
	type testCase struct {
		state uw.ApplicationReviewState
	}
	var testCases []testCase
	for _, grade := range uw.ApplicationReviewStateValues() {
		testCases = append(testCases, testCase{
			state: grade,
		})
	}
	reviewId, err := s.setupPendingAppReviewWithFixture()
	s.Require().NoError(err)
	form := oapi_uw.ApplicationReviewSafetyOOSViolationsForm{}
	// Test for all test cases and ensure that the action works only for valid states
	for _, tc := range testCases {
		s.T().Run(tc.state.String(), func(t *testing.T) {
			// update the state of the application review.
			err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, reviewId.String(),
				func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
					review.State = tc.state
					return review, nil
				})
			s.Require().NoError(err)
			// Try to update basic score threshold and ensure that it fails if the state is not valid
			if _, ok := validStates[tc.state]; !ok {
				err = s.h.Api.UpdateOOSViolation(reviewId.String(), form)
				s.Require().Error(err)
				return
			}
			// For valid states ensure that the action works
			err = s.h.Api.UpdateOOSViolation(reviewId.String(), form)
			s.Require().NoError(err)
		})
	}
}

func (s *uwApisTestSuite) TestUpdateSafetyScore() {
	validStates := map[uw.ApplicationReviewState]bool{
		uw.ApplicationReviewStatePending: true,
	}
	type testCase struct {
		state uw.ApplicationReviewState
	}
	var testCases []testCase
	for _, grade := range uw.ApplicationReviewStateValues() {
		testCases = append(testCases, testCase{
			state: grade,
		})
	}
	reviewId, err := s.setupPendingAppReviewWithFixture()
	s.Require().NoError(err)
	form := oapi_uw.ApplicationReviewSafetyScoreForm{}
	// Test for all test cases and ensure that the action works only for valid states
	for _, tc := range testCases {
		s.T().Run(tc.state.String(), func(t *testing.T) {
			// update the state of the application review.
			err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, reviewId.String(),
				func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
					review.State = tc.state
					return review, nil
				})

			s.Require().NoError(err)
			// Try to update basic score threshold and ensure that it fails if the state is not valid
			if _, ok := validStates[tc.state]; !ok {
				_, err = s.h.Api.UpdateSafetyScore(reviewId.String(), form)
				s.Require().Error(err)
				return
			}
			// For valid states ensure that the action works
			_, err = s.h.Api.UpdateSafetyScore(reviewId.String(), form)
			s.Require().NoError(err)
		})
	}
}

func (s *uwApisTestSuite) TestUpdateSafetyScoreSelectedForReview() {
	ctx := context.Background()
	reviewId, err := s.setupPendingAppReviewWithFixture()
	s.Require().NoError(err)

	appReview, err := s.h.UnderwritingDeps.ApplicationReviewWrapper.GetReview(ctx, reviewId.String())
	s.Require().NoError(err)

	err = s.deps.ApplicationWrapper.UpdateApp(
		s.h.Ctx, appReview.Application.ID, func(app application.Application) (application.Application, error) {
			app.TSPEnum = pointer_utils.ToPointer(telematics.TSPSamsara)
			app.TSPConnHandleId = pointer_utils.ToPointer(test_utils2.GoldenHandleId(telematics.TSPSamsara).String())
			return app, nil
		})
	s.Require().NoError(err)

	oapiScore := oapi_uw.ApplicationReviewSafetyScoreSelectedForRating{
		Score:        pointer_utils.ToPointer(float32(93)),
		ScoreType:    "TRS",
		ScoreVersion: "V3",
		Timestamp:    time.Date(2022, 3, 1, 0, 0, 0, 0, time.UTC),
		VinCount:     pointer_utils.ToPointer(float32(25)),
		WindowEnd:    time.Date(2022, 3, 1, 0, 0, 0, 0, time.UTC),
		WindowStart:  time.Date(2021, 12, 1, 0, 0, 0, 0, time.UTC),
		WindowType:   "3M",
	}

	form := oapi_uw.ApplicationReviewSafetyScoreForm{
		Form: &oapi_uw.ApplicationReviewSafetyScoreFormData{
			ScoreSelectedForReview: &oapiScore,
		},
	}

	_, err = s.h.Api.UpdateSafetyScore(reviewId.String(), form)
	s.Require().NoError(err)

	resp, err := s.h.UnderwritingDeps.ApplicationReviewWrapper.GetReview(ctx, reviewId.String())
	s.Require().NoError(err)

	s.Require().NotNil(resp.Overrides.SafetyScoreSelectedForReview)
	s.Require().NotNil(resp.Overrides.SafetyScoreSelectedForReview.Score)
	s.Require().Equal(*oapiScore.Score, *resp.Overrides.SafetyScoreSelectedForReview.Score)
	s.Require().Equal(string(oapiScore.ScoreType), resp.Overrides.SafetyScoreSelectedForReview.ScoreType)
	s.Require().Equal(string(oapiScore.ScoreVersion), resp.Overrides.SafetyScoreSelectedForReview.ScoreVersion)
	s.Require().Equal(oapiScore.Timestamp, resp.Overrides.SafetyScoreSelectedForReview.Timestamp)
	s.Require().Equal(*oapiScore.VinCount, *resp.Overrides.SafetyScoreSelectedForReview.VinCount)
	s.Require().Equal(oapiScore.WindowEnd, resp.Overrides.SafetyScoreSelectedForReview.WindowEnd)
	s.Require().Equal(oapiScore.WindowStart, resp.Overrides.SafetyScoreSelectedForReview.WindowStart)
	s.Require().Equal(string(oapiScore.WindowType), resp.Overrides.SafetyScoreSelectedForReview.WindowType)
}

func (s *uwApisTestSuite) TestUpdateSafetySevereViolations() {
	validStates := map[uw.ApplicationReviewState]bool{
		uw.ApplicationReviewStatePending: true,
	}
	type testCase struct {
		state uw.ApplicationReviewState
	}
	var testCases []testCase
	for _, grade := range uw.ApplicationReviewStateValues() {
		testCases = append(testCases, testCase{
			state: grade,
		})
	}
	reviewId, err := s.setupPendingAppReviewWithFixture()
	s.Require().NoError(err)
	form := oapi_uw.ApplicationReviewSafetySevereViolationsForm{}
	// Test for all test cases and ensure that the action works only for valid states
	for _, tc := range testCases {
		s.T().Run(tc.state.String(), func(t *testing.T) {
			// update the state of the application review.
			err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, reviewId.String(),
				func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
					review.State = tc.state
					return review, nil
				})

			s.Require().NoError(err)
			// Try to update basic score threshold and ensure that it fails if the state is not valid
			if _, ok := validStates[tc.state]; !ok {
				err = s.h.Api.UpdateSafetySevereViolation(reviewId.String(), form)
				s.Require().Error(err)
				return
			}
			// For valid states ensure that the action works
			err = s.h.Api.UpdateSafetySevereViolation(reviewId.String(), form)
			s.Require().NoError(err)
		})
	}
}

func (s *uwApisTestSuite) TestUpdateSummary() {
	validStates := map[uw.ApplicationReviewState]bool{
		uw.ApplicationReviewStatePending: true,
	}
	type testCase struct {
		state uw.ApplicationReviewState
	}
	var testCases []testCase
	for _, grade := range uw.ApplicationReviewStateValues() {
		testCases = append(testCases, testCase{
			state: grade,
		})
	}
	reviewId, err := s.setupPendingAppReviewWithFixture()
	s.Require().NoError(err)
	form := oapi_uw.ApplicationReviewSummaryForm{}
	// Test for all test cases and ensure that the action works only for valid states
	for _, tc := range testCases {
		s.T().Run(tc.state.String(), func(t *testing.T) {
			// update the state of the application review.
			err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, reviewId.String(),
				func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
					review.State = tc.state
					return review, nil
				})

			s.Require().NoError(err)
			// Try to update basic score threshold and ensure that it fails if the state is not valid
			if _, ok := validStates[tc.state]; !ok {
				err = s.h.Api.UpdateSummary(reviewId.String(), form)
				s.Require().Error(err)
				return
			}
			// For valid states ensure that the action works
			err = s.h.Api.UpdateSummary(reviewId.String(), form)
			s.Require().NoError(err)
		})
	}
}

func (s *uwApisTestSuite) TestUpdateSummaryVersionChange() {
	reviewId, err := s.setupPendingAppReviewWithFixture()
	s.Require().NoError(err)

	err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, reviewId.String(),
		func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
			review.State = uw.ApplicationReviewStatePending
			return review, nil
		})
	appReview, err := s.h.UnderwritingDeps.ApplicationReviewWrapper.GetReview(s.h.Ctx, reviewId.String())
	s.Require().NoError(err)
	// Force the application to before 06/01
	err = s.h.UnderwritingDeps.ApplicationWrapper.UpdateApp(s.h.Ctx, appReview.ApplicationID,
		func(appObj application.Application) (application.Application, error) {
			appObj.CoverageInfo.EffectiveDate = time.Date(2023, 5, 1, 0, 0, 0, 0, time.UTC)
			// Force old version of the rating model in the application
			appObj.ModelPinConfig.RateML.Version = rtypes.Version051
			return appObj, nil
		})
	s.Require().NoError(err)
	date := openapi_types.Date{Time: time.Date(2023, 6, 1, 0, 0, 0, 0, time.UTC)}
	// Test version change for IN based on effective date 06/01
	form := oapi_uw.ApplicationReviewSummaryForm{
		EffectiveDate: &date,
	}
	// This should update the model version in app review
	err = s.h.Api.UpdateSummary(reviewId.String(), form)
	s.Require().NoError(err)
	appReview, err = s.h.UnderwritingDeps.ApplicationReviewWrapper.GetReview(s.h.Ctx, reviewId.String())
	s.Require().NoError(err)
	// Check if the model version is updated in the app review
	s.Require().Equal(appReview.Overrides.ModelPinConfig.RateML.Version, rtypes.Version060)
	s.Require().Equal(appReview.EffectiveDate, time.Date(2023, 6, 1, 0, 0, 0, 0, time.UTC))
}

func (s *uwApisTestSuite) TestUpdateSummaryCameraSubsidy() {
	reviewId, err := s.setupPendingAppReviewWithFixture()
	s.Require().NoError(err)

	err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, reviewId.String(),
		func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
			review.State = uw.ApplicationReviewStatePending
			return review, nil
		})
	appReview, err := s.h.UnderwritingDeps.ApplicationReviewWrapper.GetReview(s.h.Ctx, reviewId.String())
	s.Require().NoError(err)
	// Initially we shouldn't have any camera subsidy details
	s.Require().Nil(appReview.CameraSubsidyDetails)
	subsidyAmount := float64(20)
	numberOfCameras := 12
	form := oapi_uw.ApplicationReviewSummaryForm{
		SubsidyAmount:          &subsidyAmount,
		NumberOfVehicleCameras: &numberOfCameras,
	}
	err = s.h.Api.UpdateSummary(reviewId.String(), form)
	s.Require().NoError(err)
	appReview, err = s.h.UnderwritingDeps.ApplicationReviewWrapper.GetReview(s.h.Ctx, reviewId.String())
	s.Require().NoError(err)
	s.Require().NotNil(appReview.CameraSubsidyDetails)
	s.Require().Equal(numberOfCameras, appReview.CameraSubsidyDetails.NumberOfCameras)
	s.Require().NotNil(appReview.CameraSubsidyDetails.SubsidyAmount)
	s.Require().Equal(subsidyAmount, *appReview.CameraSubsidyDetails.SubsidyAmount)
}

func (s *uwApisTestSuite) TestUpdateAppReview() {
	validStates := map[uw.ApplicationReviewState]bool{
		uw.ApplicationReviewStatePending: true,
	}
	type testCase struct {
		state uw.ApplicationReviewState
	}
	var testCases []testCase
	for _, grade := range uw.ApplicationReviewStateValues() {
		testCases = append(testCases, testCase{
			state: grade,
		})
	}
	reviewId, err := s.setupPendingAppReviewWithFixture()
	s.Require().NoError(err)
	form := oapi_uw.UpdateApplicationReviewRequest{}
	// Test for all test cases and ensure that the action works only for valid states
	for _, tc := range testCases {
		s.T().Run(tc.state.String(), func(t *testing.T) {
			// update the state of the application review.
			err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, reviewId.String(),
				func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
					review.State = tc.state
					return review, nil
				})

			s.Require().NoError(err)
			// Try to update app review and ensure that it fails if the state is not valid
			if _, ok := validStates[tc.state]; !ok {
				err = s.h.Api.UpdateAppReview(reviewId.String(), form)
				s.Require().Error(err)
				return
			}
			// For valid states ensure that the action works
			err = s.h.Api.UpdateAppReview(reviewId.String(), form)
			s.Require().NoError(err)
		})
	}
}

func (s *uwApisTestSuite) TestGetAppDeclineReasons() {
	reviewId, err := s.setupPendingAppReviewWithFixture()
	s.Require().NoError(err)
	response, err := s.h.Api.GetApplicationDeclineReasons(s.h.Ctx, reviewId.String())
	s.Require().NoError(err)
	s.Require().NotNil(response)
	for _, expectedReason := range reasons.ApplicationDeclineReasons {
		if !expectedReason.IsActive {
			continue // skip inactive reasons
		}
		found := false
		for _, reason := range *response.Reasons {
			if reason.ReasonId == expectedReason.ReasonCode && reason.SubReasonId == expectedReason.SubReasonCode {
				found = true
				s.Require().Equal(expectedReason.ReasonText, reason.Reason)
				s.Require().Equal(expectedReason.SubReasonText, reason.SubReason)
				if expectedReason.ExternalNote != nil {
					s.Require().Equal(*expectedReason.ExternalNote, reason.ExternalNote)
				}
			}
		}
		s.Require().True(found) // ensure that response contains all the expected reasons
	}
}

func (s *uwApisTestSuite) TestGetApplicationCloseReasons() {
	reviewId, err := s.setupPendingAppReviewWithFixture()
	s.Require().NoError(err)
	response, err := s.h.Api.GetApplicationCloseReasons(s.h.Ctx, reviewId.String())
	s.Require().NoError(err)
	s.Require().NotNil(response)
	for _, expectedReason := range reasons.ApplicationCloseReasons {
		if !expectedReason.IsActive {
			continue // skip inactive reasons
		}
		found := false
		for _, reason := range *response.Reasons {
			if reason.ReasonId == expectedReason.ReasonCode && reason.SubReasonId == expectedReason.SubReasonCode {
				found = true
				s.Require().Equal(expectedReason.ReasonText, reason.Reason)
				s.Require().Equal(expectedReason.SubReasonText, reason.SubReason)
				s.Require().Nil(expectedReason.ExternalNote)
			}
		}
		s.Require().True(found) // ensure that response contains all the expected reasons
	}
}

func (s *uwApisTestSuite) TestPostCloseReasons() {
	validStates := map[uw.ApplicationReviewState]bool{
		uw.ApplicationReviewStatePending:  true,
		uw.ApplicationReviewStateApproved: true,
	}
	type testCase struct {
		state uw.ApplicationReviewState
	}
	var testCases []testCase
	for _, grade := range uw.ApplicationReviewStateValues() {
		testCases = append(testCases, testCase{
			state: grade,
		})
	}
	reviewId, err := s.setupPendingAppReviewWithFixture()
	s.Require().NoError(err)

	reviewObj, err := s.h.UnderwritingDeps.ApplicationReviewWrapper.GetReview(s.h.Ctx, reviewId.String())
	s.Require().NoError(err)
	// find a valid reason that will pass validation tests for reasons form
	var validReason reasons.ReasonStruct
	for _, reason := range reasons.ApplicationCloseReasons {
		if reason.IsActive {
			validReason = reason.ReasonStruct
			break
		}
	}

	winCarrier := reasons.WinCarrierReason[0]

	form := oapi_uw.ApplicationReviewCloseReasonsForm{
		ReasonsArray: []oapi_uw.ApplicationReviewCloseReasonObject{
			{
				ReasonId:    validReason.ReasonCode,
				Reason:      validReason.ReasonText,
				SubReasonId: validReason.SubReasonCode,
				SubReason:   validReason.SubReasonText,
			},
		},
		WinCarrier: &winCarrier.WinCarrierCode,
		Comments:   "comments",
	}

	// Test for all test cases and ensure that the action works only for valid states
	for _, tc := range testCases {
		s.T().Run(tc.state.String(), func(t *testing.T) {
			err = s.h.UnderwritingDeps.ApplicationWrapper.UpdateApp(s.h.Ctx, reviewObj.ApplicationID,
				func(app application.Application) (application.Application, error) {
					app.State = state_enums.AppStateUnderUWReview // should be correct state for closing
					return app, nil
				})
			s.Require().NoError(err)
			// update the state of the application review.
			err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, reviewId.String(),
				func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
					review.State = tc.state
					return review, nil
				})
			s.Require().NoError(err)
			if _, ok := validStates[tc.state]; !ok {
				err = s.h.Api.CloseApplicationReview(s.h.Ctx, reviewId.String(), form)
				s.Require().Error(err)
				return
			}
			err = s.h.Api.CloseApplicationReview(s.h.Ctx, reviewId.String(), form)
			s.Require().NoError(err)
			reviewObj, err := s.h.UnderwritingDeps.ApplicationReviewWrapper.GetReview(s.h.Ctx, reviewId.String())
			s.Require().NoError(err)
			s.Require().Equal(uw.ApplicationReviewStateClosed, reviewObj.State)
			s.Require().Equal(form.Comments, reviewObj.EndStateReasons.CloseReasons.Comments)
			// below conditions are subjected to constraints that we have only added one reason in form above.
			s.Require().Equal(form.ReasonsArray[0].Reason, reviewObj.EndStateReasons.CloseReasons.Reasons[0].ReasonText)
			s.Require().Equal(form.ReasonsArray[0].ReasonId, reviewObj.EndStateReasons.CloseReasons.Reasons[0].ReasonCode)
			s.Require().Equal(form.ReasonsArray[0].SubReason, reviewObj.EndStateReasons.CloseReasons.Reasons[0].SubReasonText)
			s.Require().Equal(form.ReasonsArray[0].SubReasonId, reviewObj.EndStateReasons.CloseReasons.Reasons[0].SubReasonCode)
			s.Require().Equal(*reviewObj.EndStateReasons.CloseReasons.WinCarrier, winCarrier.WinCarrierName)
		})
	}
}

func TestPutVinProblems(t *testing.T) {
	testPutVinProblems(t, true)
	testPutVinProblems(t, false)
}

func testPutVinProblems(t *testing.T, enableVehiclesService bool) {
	t.Helper()

	ctx := context.Background()
	var env struct {
		fx.In
		AppData           *application_fixture.ApplicationsFixture
		UsersFixture      *users_fixture.UsersFixture
		ApiServer         api_server_fixture.ApiServer
		FeatureFlagClient feature_flag_lib.Client
		*feature_store_fixture.FeatureStoreFixture
		*fmcsa_fixture.FmcsaFixture
	}
	setFeatureFlagOption := fx.Decorate(func(mockClient *feature_flag_lib.MockClient) *feature_flag_lib.MockClient {
		// Our mock feature flag client defaults to true for all feature flags, so we set to false
		// if vehicles service is not requested.
		mockClient.SetValue(feature_flag_lib.FeatureVehiclesServiceVINDecoder, ldvalue.Bool(enableVehiclesService))
		return mockClient
	})
	defer testloader.RequireStart(t, &env, testloader.Use(setFeatureFlagOption)).RequireStop()

	app := env.AppData.Application
	require.True(t, len(app.EquipmentInfo.EquipmentList.Info) > 2)

	// No problems yet since VINs haven't been processed
	underwriter := env.UsersFixture.Underwriters[0]
	uwClient := env.ApiServer.UWClient(&underwriter)
	resp, err := uwClient.GetVinProblems(ctx, app.ID)
	require.NoError(t, err)
	vinProblems := fixture_utils.ReadResponse[oapi_uw.ApplicationVinProblems](t, resp)
	require.Empty(t, vinProblems)

	equipment := app.EquipmentInfo.EquipmentList.Info

	vin1, vin2 := equipment[0].VIN, equipment[1].VIN

	// Problems can be updated anytime
	probs1 := oapi_uw.ApplicationVinProblems{
		Problems: []oapi_uw.ApplicationVinProblemRecord{
			// All necessary fields manually fixed up
			{
				FixedBodyClass:   helpers.VehicleBodyClassPtr("Truck-Tractor"),
				Vin:              vin1,
				FixedVehicleType: helpers.VehicleTypePtr(nhtsaEnums.VehicleTypeTrailer.String()),
				FixedWeightClass: pointer_utils.String(nhtsaEnums.WeightClass8.String()),
				FixedMake:        pointer_utils.String("Manual"),
				FixedModel:       pointer_utils.String("Manual"),
				FixedModelYear:   pointer_utils.String("2019"),
				ShouldSkip:       pointer_utils.Bool(false),
				// These fields are not respected on PUTs, but we set them here to make
				// equality testing easier
				IsResolved: true,
				Errors: []string{
					"rpc error: code = Unknown desc = failed to get VIN record V1: record not found for vin " + vin1,
				},
			},
			// Not a complete fix
			{
				Vin:        vin2,
				FixedVin:   pointer_utils.String("789"),
				ShouldSkip: pointer_utils.Bool(false),
				// This field is not respected on PUTs, but we set it here to make
				// equality testing easier
				Errors: []string{
					"rpc error: code = Unknown desc = failed to get VIN record V1: record not found for vin 789",
					"Make cannot be empty",
					"Model cannot be empty",
					"ModelYear cannot be empty",
					"BodyClass cannot be empty",
					"error while mapping nhtsa values to iso values: Problematic entry found. Value for iso vehicle type is IsoVehicleTypeV1Truck and iso weight group is IsoWeightGroupV1Light",
				},
			},
		},
	}

	_, err = uwClient.PutVinProblems(ctx, app.ID, probs1)
	require.NoError(t, err)

	resp, err = uwClient.GetVinProblems(ctx, app.ID)
	require.NoError(t, err)
	vinProblems = fixture_utils.ReadResponse[oapi_uw.ApplicationVinProblems](t, resp)
	require.NotEmpty(t, vinProblems)

	require.ElementsMatch(t, probs1.Problems, vinProblems.Problems)
	problem1 := getForVin(vinProblems, vin1)
	problem2 := getForVin(vinProblems, vin2)
	// VIN 1 was fully resolved manually
	require.True(t, problem1.IsResolved)
	// VIN 2 was only partially resolved
	require.False(t, problem2.IsResolved)

	// If enums don't validate, we fail
	var probs2 oapi_uw.ApplicationVinProblems
	require.NoError(t, copier.CopyWithOption(&probs2, probs1, copier.Option{DeepCopy: true}))
	probs2.Problems[0].FixedVehicleType = helpers.VehicleTypePtr("Invalid")
	resp, err = uwClient.PutVinProblems(ctx, app.ID, probs2)
	require.NoError(t, err)
	errMsg := fixture_utils.ReadResponse[oapi_common.ErrorMessage](t, resp)
	require.Equal(t, "Failed to parse problem. Error: Bad Vehicle Type: Invalid does not belong to VehicleType values", errMsg.Message)

	resp, err = uwClient.GetVinProblems(ctx, app.ID)
	require.NoError(t, err)
	vinProblems = fixture_utils.ReadResponse[oapi_uw.ApplicationVinProblems](t, resp)
	require.ElementsMatch(t, probs1.Problems, vinProblems.Problems)

	// Clear problems with an empty PUT
	var probs3 oapi_uw.ApplicationVinProblems
	_, err = uwClient.PutVinProblems(ctx, app.ID, probs3)
	require.NoError(t, err)

	resp, err = uwClient.GetVinProblems(ctx, app.ID)
	require.NoError(t, err)
	vinProblems = fixture_utils.ReadResponse[oapi_uw.ApplicationVinProblems](t, resp)
	if enableVehiclesService {
		// We are not deleting VIN problems from vehicles service, so we expect to still have problems
		// even after an empty PUT operation.
		require.NotEmpty(t, vinProblems.Problems)
	} else {
		require.ElementsMatch(t, probs3.Problems, vinProblems.Problems)
	}
}

func (s *uwApisTestSuite) TestSetAttractScore() {
	validStates := map[uw.ApplicationReviewState]bool{
		uw.ApplicationReviewStatePending: true,
	}
	type testCase struct {
		state uw.ApplicationReviewState
	}
	var testCases []testCase
	for _, grade := range uw.ApplicationReviewStateValues() {
		testCases = append(testCases, testCase{
			state: grade,
		})
	}
	// Create app and app review
	appReviewId, err := s.setupPendingAppReviewWithFixture()
	s.Require().NoError(err)
	for _, tc := range testCases {
		s.T().Run(tc.state.String(), func(t *testing.T) {
			// update the state of the app review to the current state
			err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, appReviewId.String(), func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
				review.State = tc.state
				return review, nil
			})
			s.Require().NoError(err)
			// Try to set attract score to true and ensure that it fails if the state is not valid
			if _, ok := validStates[tc.state]; !ok {
				err = s.h.Api.SetAttractScore(appReviewId.String())
				s.Require().Error(err)
				return
			}
			// For valid states ensure that the action works
			err = s.h.Api.SetAttractScore(appReviewId.String())
			s.Require().NoError(err)
			// Ensure that Attract Score Flag is set to true
			appReview, err := s.h.UnderwritingDeps.ApplicationReviewWrapper.GetReview(s.h.Ctx, appReviewId.String())
			s.Require().NoError(err)
			s.Require().NotNil(appReview.QuoteInfo.FetchAttractScore)
			s.Require().True(*appReview.QuoteInfo.FetchAttractScore)
		})
	}
}

func (s *uwApisTestSuite) TestGetDriversList_AttractScore_HappyCase() {
	tests := []struct {
		name      string
		isNewFlow bool
	}{
		{
			name: "get driver list",
		},
	}

	for _, tt := range tests {
		s.T().Run(tt.name, func(t *testing.T) {
			reviewId, err := s.setupPendingAppReviewWithFixture()
			s.Require().NoError(err)

			// set attract score flag to false
			err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, reviewId.String(), func(r uw.ApplicationReview) (uw.ApplicationReview, error) {
				r.QuoteInfo.FetchAttractScore = pointer_utils.Bool(false)
				return r, nil
			})
			s.Require().NoError(err)

			resp, err := s.h.Api.GetDriversList(reviewId.String())
			s.Require().NoError(err)
			s.Require().NotNil(resp)

			// Verify if all attract scores are nil
			for _, d := range resp.Success.Drivers {
				s.Require().Nil(d.AttractScore)
			}

			// Set fetch attract score flag to true
			err = s.h.Api.SetAttractScore(reviewId.String())
			s.Require().NoError(err)

			// Get all the attract scores in the driver's list
			resp, err = s.h.Api.GetDriversList(reviewId.String())
			s.Require().NoError(err)
			s.Require().NotNil(resp)

			// Verify if all attract scores are not nil and respective values are 0 (since MVR is a noop client here)
			for _, d := range resp.Success.Drivers {
				s.Require().NotNil(d.AttractScore)
				s.Require().Zero(*d.AttractScore)
			}
		})
	}
}

// TestDefaultSafetyDiscount tests the default safety discount logic
// with the refactored TransformNirvanaSafetyScore function we
// don't pipe a minimum 0.05 value for safetyModAllCov which leads to
// the discount display values being 0 by default. This test ensures ->
// 1. The new logic of not piping a minimum 0.05 value and generation of the discount display values
// driven by IsDefaultSafetyDiscountDisabled rateML flag.
// 2. Ensuring that on changing the credits of safety widget we get a corresponding change in the
// discount display values.
// 3. Backwards compatibility with the old logic of piping a minimum 0.05 value and
// generation of the discount display values.
func (s *uwApisTestSuite) TestDefaultSafetyDiscount() {
	// Create app and app review with the IsDefaultSafetyDiscountDisabled flag
	// as true and verify that the discount display values are 0
	// Create app and app review
	appReviewWrapper, err := s.h.TestWrappers.AppReviewTestWrapperCreator()
	s.Require().NoError(err)
	appReviewId, err := uuid.Parse(appReviewWrapper.AppReviewId)
	s.Require().NoError(err)
	// From the submission id get the indication options
	appReview, err := s.h.UnderwritingDeps.ApplicationReviewWrapper.GetReview(s.h.Ctx, appReviewId.String())
	s.Require().NoError(err)
	s.Require().NoError(s.h.LoginDefaultHighestAuthorityUW())
	quote, err := appReviewWrapper.CreateAndGetBindableQuote()
	s.Require().NoError(err)
	// verify quote changes
	s.Require().NotNil(quote.SafetyDiscountPercentage)
	s.Require().NotNil(quote.SafetyDiscount)
	s.Require().EqualValues(0, *quote.SafetyDiscount)
	s.Require().EqualValues(0, *quote.SafetyDiscountPercentage)
	// Get the sub id
	indicationOptions, err := s.h.UnderwritingDeps.ApplicationWrapper.GetIndOptionsBySubId(s.h.Ctx,
		appReview.Submission.ID)
	s.Require().NoError(err)
	// For these indication options verify that the safety discount display values are 0
	for _, indicationOption := range indicationOptions {
		s.Require().EqualValues(0, indicationOption.SafetyDiscountPremium)
		s.Require().EqualValues(0, indicationOption.SafetyDiscountPercentage)
		s.Require().EqualValues(0, indicationOption.RoundedSafetyDiscountPremium)
		s.Require().EqualValues(0, indicationOption.RoundedSafetyDiscountPercentage)
	}
	// Change the al credits of safety widget to 1 and assert that the discount display values are up 1%
	widgetBase := oapi_uw.ApplicationReviewSafetyScoreForm{
		Meta: &oapi_uw.ApplicationReviewWidgetMeta{
			AutoLiability: &oapi_uw.ApplicationReviewWidgetCoverageMeta{
				Credit: pointer_utils.Float32(1),
			},
		},
	}
	_, err = s.h.Api.UpdateSafetyScore(appReviewId.String(), widgetBase)
	s.Require().NoError(err)
	// Update the quote
	quote, err = appReviewWrapper.UpdateAndGetQuote()
	s.Require().NoError(err)
	// Verify the quote safety discount percentage values
	s.Require().EqualValues(1, *quote.SafetyDiscountPercentage)
	appReview, err = s.h.UnderwritingDeps.ApplicationReviewWrapper.GetReview(s.h.Ctx, appReviewId.String())
	s.Require().NoError(err)
	for _, id := range appReview.QuoteInfo.IndicationOptionsIDs {
		indicationOption, err := s.h.UnderwritingDeps.ApplicationWrapper.GetIndOptionById(s.h.Ctx, id)
		s.Require().NoError(err)
		// For these indication options verify that the safety discount display values are ~1%
		s.Require().EqualValues(1, indicationOption.SafetyDiscountPercentage)
		s.Require().EqualValues(1, indicationOption.RoundedSafetyDiscountPercentage)
	}
	// Backwards compatibility test with the earlier applications
	// Set the value of the flag to false for both application and submission
	err = s.h.UnderwritingDeps.ApplicationWrapper.UpdateApp(s.h.Ctx, appReview.Submission.ApplicationID,
		func(app application.Application) (application.Application, error) {
			app.ModelPinConfig.RateML.Flags.IsDefaultSafetyDiscountDisabled = false
			return app, nil
		})
	s.Require().NoError(err)
	err = s.h.UnderwritingDeps.ApplicationWrapper.UpdateSub(s.h.Ctx, appReview.Submission.ID,
		func(sub application.SubmissionObject) (application.SubmissionObject, error) {
			sub.ModelPinConfig.RateML.Flags.IsDefaultSafetyDiscountDisabled = false
			return sub, nil
		})
	s.Require().NoError(err)
	// Update the quote
	quote, err = appReviewWrapper.UpdateAndGetQuote()
	s.Require().NoError(err)
	// Verify the quote safety discount percentage values are 0.05
	s.Require().EqualValues(5, *quote.SafetyDiscountPercentage)
	appReview, err = s.h.UnderwritingDeps.ApplicationReviewWrapper.GetReview(s.h.Ctx, appReviewId.String())
	s.Require().NoError(err)
	for _, id := range appReview.QuoteInfo.IndicationOptionsIDs {
		indicationOption, err := s.h.UnderwritingDeps.ApplicationWrapper.GetIndOptionById(s.h.Ctx, id)
		s.Require().NoError(err)
		// For these indication options verify that the safety discount display values are 0.05
		s.Require().EqualValues(5, indicationOption.SafetyDiscountPercentage)
		s.Require().EqualValues(5, indicationOption.RoundedSafetyDiscountPercentage)
	}
	// Set the merit as 6% and verify that the safety discount display values are 6%
	widgetBase = oapi_uw.ApplicationReviewSafetyScoreForm{
		Meta: &oapi_uw.ApplicationReviewWidgetMeta{
			AutoLiability: &oapi_uw.ApplicationReviewWidgetCoverageMeta{
				Credit: pointer_utils.Float32(6),
			},
		},
	}
	_, err = s.h.Api.UpdateSafetyScore(appReviewId.String(), widgetBase)
	s.Require().NoError(err)
	// Update the quote
	quote, err = appReviewWrapper.UpdateAndGetQuote()
	s.Require().NoError(err)
	// Verify the quote safety discount percentage values are 6%
	s.Require().EqualValues(6, *quote.SafetyDiscountPercentage)
	appReview, err = s.h.UnderwritingDeps.ApplicationReviewWrapper.GetReview(s.h.Ctx, appReviewId.String())
	s.Require().NoError(err)
	for _, id := range appReview.QuoteInfo.IndicationOptionsIDs {
		indicationOption, err := s.h.UnderwritingDeps.ApplicationWrapper.GetIndOptionById(s.h.Ctx, id)
		s.Require().NoError(err)
		// For these indication options verify that the safety discount display values are 6%
		s.Require().EqualValues(6, indicationOption.SafetyDiscountPercentage)
		s.Require().EqualValues(6, indicationOption.RoundedSafetyDiscountPercentage)
	}
}

func (s *uwApisTestSuite) TestBindableQuoteConfirmationOverrideDepositAmount() {
	appReviewTestWrapper, err := s.h.TestWrappers.AppReviewTestWrapperCreator()
	s.Require().NoError(err)

	s.Require().NoError(s.h.LoginDefaultHighestAuthorityUW())

	_, err = appReviewTestWrapper.CreateAndApproveBindableQuote()
	s.Require().NoError(err)

	// manually change state to policy created
	err = s.h.ApplicationDeps.ApplicationWrapper.UpdateApp(s.h.Ctx, appReviewTestWrapper.GetAppId(),
		func(a application.Application) (application.Application, error) {
			a.State = state_enums.AppStatePolicyCreated
			return a, nil
		})
	s.Require().NoError(err)

	// create renewal application
	_, err = appReviewTestWrapper.RenewApplicationAndSubmitForReview()
	s.Require().NoError(err)

	// create bindable quote for renewal application
	_, err = appReviewTestWrapper.CreateAndGetBindableQuote()
	s.Require().NoError(err)

	var monthlyDepositAmount float32 = 1000.0

	err = s.h.Api.ConfirmBindableQuote(s.h.Ctx, appReviewTestWrapper.AppReviewId, &oapi_uw.ApplicationReviewConfirmBindableQuoteForm{
		DepositAmount: &oapi_uw.ApplicationReviewDepositAmount{
			MonthlyPaymentDeposit: &monthlyDepositAmount,
		},
	})
	s.Require().NoError(err)

	// verify that the deposit amount is updated in bindable submission
	appReview, err := s.h.UnderwritingDeps.ApplicationReviewWrapper.GetReview(s.h.Ctx, appReviewTestWrapper.AppReviewId)
	s.Require().NoError(err)

	sub, err := s.h.UnderwritingDeps.ApplicationWrapper.GetSubmissionById(s.h.Ctx, appReview.BindableSubInfo.SubmissionID)
	s.Require().NoError(err)
	s.Require().Equal(monthlyDepositAmount, *sub.UnderwriterInput.DepositAmount.MonthlyPaymentDeposit)
}

func (s *uwApisTestSuite) TestBindableQuoteConfirmationOverrideDepositAmount_ShouldNotBeAllowedForFreshApplications() {
	appReviewTestWrapper, err := s.h.TestWrappers.AppReviewTestWrapperCreator()
	s.Require().NoError(err)

	s.Require().NoError(s.h.LoginDefaultHighestAuthorityUW())

	_, err = appReviewTestWrapper.CreateAndGetBindableQuote()

	var monthlyDepositAmount float32 = 1000.0

	err = s.h.Api.ConfirmBindableQuote(s.h.Ctx, appReviewTestWrapper.AppReviewId, &oapi_uw.ApplicationReviewConfirmBindableQuoteForm{
		DepositAmount: &oapi_uw.ApplicationReviewDepositAmount{
			MonthlyPaymentDeposit: &monthlyDepositAmount,
		},
	})
	s.Require().Error(err, "deposit amount can only be overridden for renewal applications")
}

func (s *uwApisTestSuite) TestTelematicsConnectionStateMachineCanBeInitialisedToAnyState() {
	session, err := s.h.CreateRandomUserAndSession()
	s.Require().Nil(err)
	s.Require().NotNil(session)

	appTestWrapper, err := s.h.TestWrappers.AppTestWrapperCreator()
	s.Require().NoError(err)

	// create application
	app, err := appTestWrapper.CreateApplication()
	s.Require().NoError(err)

	// connect to TSP, this will result in TelematicsDataStatusPanic because no pipeline would have run
	err = appTestWrapper.ConnectTSP()
	s.Require().NoError(err)

	// create review
	_, err = appTestWrapper.GenerateAndSelectIndication()
	s.Require().NoError(err)
	err = appTestWrapper.SubmitApplicationForUWReview()

	s.Require().NoError(err)

	// get submission job run ID that creates app review
	app, err = s.h.ApplicationDeps.ApplicationWrapper.GetAppById(s.h.Ctx, app.ID)
	s.Require().NoError(err)
	s.Require().NotNil(app.UwSubmissionID)

	uwSub, err := s.h.ApplicationDeps.ApplicationWrapper.GetSubmissionById(s.h.Ctx, *app.UwSubmissionID)
	s.Require().NoError(err)
	s.Require().NotNil(uwSub.JobRunId)

	// Wait for job to complete
	err = s.h.QuotingJobsDeps.Jobber.WaitForJobRunCompletion(s.h.Ctx, *uwSub.JobRunId)
	s.Require().NoError(err)

	// telematics connection state should be "Error" because telematics connection is in panic state
	appReviews, err := s.h.UnderwritingDeps.ApplicationReviewWrapper.GetAllReviewsForApp(s.h.Ctx, app.ID)
	s.Require().NoError(err)

	// there will be only 1 review
	for _, review := range appReviews {
		s.Require().Equal(uw.TelematicsConnectionStateError.String(), review.TelematicsConnectionState.String())
	}
}

func (s *uwApisTestSuite) TestOverridesCarryForwardOnResubmission() {
	// this test can also be used to test overrides carry forward of other fields also
	session, err := s.h.CreateRandomUserAndSession()
	s.Require().Nil(err)
	s.Require().NotNil(session)

	appReviewTestWrapper, err := s.h.TestWrappers.AppReviewTestWrapperCreator()
	s.Require().NoError(err)

	// radius of operation
	radiusOfOperationsOverrides := []*application.MileageRadiusRecord{
		{
			RadiusBucket:      app_enums.MileageRadiusBucketFiftyToTwoHundred,
			PercentageOfFleet: 100,
		},
	}

	// target price
	targetPriceOverride := &uw.TargetPriceOverride{
		IsTargetPriceAvailable: true,
		Total: &uw.TargetPrice{
			Range: &uw.TargetPriceRange{
				Min: 1000,
				Max: 2000,
			},
		},
	}

	err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, appReviewTestWrapper.AppReviewId, func(r uw.ApplicationReview) (uw.ApplicationReview, error) {
		// add overrides
		r.Overrides.RadiusOfOperation = &radiusOfOperationsOverrides
		r.Overrides.TargetPrice = targetPriceOverride
		return r, nil
	})
	s.Require().NoError(err)

	appReviewTestWrapper, err = appReviewTestWrapper.ResubmitApplicationForUWReview()
	s.Require().NoError(err)

	review, err := s.h.UnderwritingDeps.ApplicationReviewWrapper.GetReview(s.h.Ctx, appReviewTestWrapper.AppReviewId)
	s.Require().NoError(err)

	// verify that overrides are carry forwarded
	// radius of operations
	s.Require().NotNil(review.Overrides.RadiusOfOperation)
	s.Require().Len(*review.Overrides.RadiusOfOperation, 1)
	s.Require().Equal(*radiusOfOperationsOverrides[0], *((*review.Overrides.RadiusOfOperation)[0]))

	// target price
	s.Require().NotNil(review.Overrides.TargetPrice)
	s.Require().Equal(*targetPriceOverride, *review.Overrides.TargetPrice)
}

func (s *uwApisTestSuite) TestGetUnderwriters() {
	ctx := context.Background()
	uwClient := s.apiServer.UWClient(&s.usersFixture.Superuser)
	httpResp, err := uwClient.GetUnderwriters(ctx)
	s.Require().NoError(err)
	s.Require().Equal(http.StatusOK, httpResp.StatusCode)
	resp := fixture_utils.ReadResponse[oapi_uw.Underwriters](s.T(), httpResp)
	s.Require().NotNil(resp.SeniorUnderwriters)
}

func (s *uwApisTestSuite) TestOverrideTargetPrice() {
	ctx := context.Background()
	uwClient := s.apiServer.UWClient(&s.usersFixture.Superuser)
	appReview := s.appReviewFixture.ApplicationReview
	tests := []struct {
		name       string
		putRequest oapi_uw.ApplicationReviewTargetPriceOverride
	}{
		{
			name: "when target price is available",
			putRequest: oapi_uw.ApplicationReviewTargetPriceOverride{
				IsTargetPriceAvailable: pointer_utils.Bool(true),
				TotalTargetPrice: &oapi_uw.ApplicationReviewTargetPrice{
					Range: &oapi_uw.ApplicationReviewTargetPriceRange{
						Min: 1000,
						Max: 2000,
					},
					RatePerHundredMiles: pointer_utils.Float32(1.5),
				},
				ApdTargetPrice: &oapi_uw.ApplicationReviewTargetPrice{
					Range: &oapi_uw.ApplicationReviewTargetPriceRange{
						Min: 2000,
						Max: 3000,
					},
					PercentOfTIV: pointer_utils.Float32(1.75),
				},
				AlTargetPrice: &oapi_uw.ApplicationReviewTargetPrice{
					Range: &oapi_uw.ApplicationReviewTargetPriceRange{
						Min: 3000,
						Max: 4000,
					},
					RatePerHundredMiles: pointer_utils.Float32(3.5),
				},
				MtcTargetPrice: &oapi_uw.ApplicationReviewTargetPrice{
					Range: &oapi_uw.ApplicationReviewTargetPriceRange{
						Min: 4000,
						Max: 5000,
					},
					RatePerHundredMiles: pointer_utils.Float32(4.5),
				},
			},
		},
		{
			name: "when target price is not available",
			putRequest: oapi_uw.ApplicationReviewTargetPriceOverride{
				IsTargetPriceAvailable: pointer_utils.Bool(false),
			},
		},
	}

	for _, test := range tests {
		s.T().Run(test.name, func(t *testing.T) {
			putRequest := test.putRequest
			httpResp, err := uwClient.UpdateApplicationReviewTargetPrice(ctx, appReview.Id, putRequest)
			s.Require().NoError(err)
			s.Require().Equal(http.StatusOK, httpResp.StatusCode)

			// get override and verify values
			httpResp, err = uwClient.GetApplicationReviewTargetPrice(ctx, appReview.Id)
			s.Require().NoError(err)
			s.Require().Equal(http.StatusOK, httpResp.StatusCode)

			getResp := fixture_utils.ReadResponse[oapi_uw.ApplicationReviewTargetPriceOverride](s.T(), httpResp)
			s.Require().Equal(*putRequest.IsTargetPriceAvailable, *getResp.IsTargetPriceAvailable)
			s.Require().Equal(putRequest.TotalTargetPrice, getResp.TotalTargetPrice)
			s.Require().Equal(putRequest.AlTargetPrice, getResp.AlTargetPrice)
			s.Require().Equal(putRequest.ApdTargetPrice, getResp.ApdTargetPrice)
			s.Require().Equal(putRequest.MtcTargetPrice, getResp.MtcTargetPrice)
		})
	}
}

func (s *uwApisTestSuite) TestOverrideTargetPriceWhenDataIsInvalid() {
	ctx := context.Background()
	uwClient := s.apiServer.UWClient(&s.usersFixture.Superuser)
	appReview := s.appReviewFixture.ApplicationReview

	tests := []struct {
		name       string
		putRequest oapi_uw.ApplicationReviewTargetPriceOverride
		errMsg     string
	}{
		{
			name: "when IsTargetPriceAvailable is true but no value is passed",
			putRequest: oapi_uw.ApplicationReviewTargetPriceOverride{
				IsTargetPriceAvailable: pointer_utils.Bool(true),
			},
			errMsg: "target price value must be passed",
		},
		{
			name: "when IsTargetPriceAvailable is false but value is passed",
			putRequest: oapi_uw.ApplicationReviewTargetPriceOverride{
				IsTargetPriceAvailable: pointer_utils.Bool(false),
				AlTargetPrice: &oapi_uw.ApplicationReviewTargetPrice{
					RatePerHundredMiles: pointer_utils.Float32(1.5),
				},
			},
			errMsg: "None of the target price value should be passed",
		},
		{
			name: "when min target price is greater than max",
			putRequest: oapi_uw.ApplicationReviewTargetPriceOverride{
				IsTargetPriceAvailable: pointer_utils.Bool(true),
				ApdTargetPrice: &oapi_uw.ApplicationReviewTargetPrice{
					Range: &oapi_uw.ApplicationReviewTargetPriceRange{
						Min: 5000,
						Max: 600,
					},
				},
			},
			errMsg: "Min cannot be greater than Max",
		},
		{
			name: "when min or max target price is negative",
			putRequest: oapi_uw.ApplicationReviewTargetPriceOverride{
				IsTargetPriceAvailable: pointer_utils.Bool(true),
				MtcTargetPrice: &oapi_uw.ApplicationReviewTargetPrice{
					Range: &oapi_uw.ApplicationReviewTargetPriceRange{
						Min: -10,
						Max: 600,
					},
				},
			},
			errMsg: "Price cannot be negative",
		},
		{
			name: "when rate per mile is negative",
			putRequest: oapi_uw.ApplicationReviewTargetPriceOverride{
				IsTargetPriceAvailable: pointer_utils.Bool(true),
				MtcTargetPrice: &oapi_uw.ApplicationReviewTargetPrice{
					RatePerHundredMiles: pointer_utils.Float32(-1.5),
				},
			},
			errMsg: "Rate per 100 miles cannot be negative",
		},
		{
			name: "when neither range nor rate per mile is passed",
			putRequest: oapi_uw.ApplicationReviewTargetPriceOverride{
				IsTargetPriceAvailable: pointer_utils.Bool(true),
				TotalTargetPrice: &oapi_uw.ApplicationReviewTargetPrice{
					Range:               nil,
					RatePerHundredMiles: nil,
				},
			},
			errMsg: "Either range or rate per 100 miles should be passed",
		},
		{
			name: "when % of TIV is invalid",
			putRequest: oapi_uw.ApplicationReviewTargetPriceOverride{
				IsTargetPriceAvailable: pointer_utils.Bool(true),
				ApdTargetPrice: &oapi_uw.ApplicationReviewTargetPrice{
					PercentOfTIV: pointer_utils.Float32(150),
				},
			},
			errMsg: "should be between 0 and 100",
		},
		{
			name:       "when isTargetPriceAvailable is nil",
			putRequest: oapi_uw.ApplicationReviewTargetPriceOverride{},
			errMsg:     "isTargetPriceAvailable is required",
		},
	}

	for _, test := range tests {
		s.T().Run(test.name, func(t *testing.T) {
			putRequest := test.putRequest
			httpResp, err := uwClient.UpdateApplicationReviewTargetPrice(ctx, appReview.Id, putRequest)
			s.Require().NoError(err)
			s.Require().Equal(http.StatusBadRequest, httpResp.StatusCode)
			resp := fixture_utils.ReadResponse[oapi_common.ErrorMessage](s.T(), httpResp)
			s.Require().Regexp(test.errMsg, resp.Message)
		})
	}
}

func (s *uwApisTestSuite) TestGetTargetPriceWhenOverrideIsNotPresent() {
	ctx := context.Background()
	uwClient := s.apiServer.UWClient(&s.usersFixture.Superuser)
	appReview := s.appReviewFixture.ApplicationReview

	// set target price override as nil
	err := s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, appReview.Id, func(r uw.ApplicationReview) (uw.ApplicationReview, error) {
		r.Overrides.TargetPrice = nil
		return r, nil
	})
	s.Require().NoError(err)

	// get target price
	httpResp, err := uwClient.GetApplicationReviewTargetPrice(ctx, appReview.Id)
	s.Require().NoError(err)
	s.Require().Equal(http.StatusOK, httpResp.StatusCode)
	getResp := fixture_utils.ReadResponse[oapi_uw.ApplicationReviewTargetPriceOverride](s.T(), httpResp)
	// empty json is returned when there is no override
	s.Require().Nil(getResp.IsTargetPriceAvailable)
}

//FIXME: flaky test
//func (s *uwApisTestSuite) TestGetRecommendations() {
//	review := setUpAndGetAppReview(s)
//
//	conclusion := recommendation.ConclusionPartiallyAccept
//	conclusionReason := "Gave 5% credit instead because of XYZ reason"
//
//	var recosSetSlice []recommendation.Set
//	for i := 1; i < 3; i++ {
//		recosSetSlice = append(recosSetSlice, recommendation.Set{
//			Panel:        app_review_widget.AppReviewPanelOperations,
//			ExperimentID: fmt.Sprintf("MY_EXPERIMENT_1234_%d", i),
//			Name:         "My Experiment",
//			Action: recommendation.RecommendedAction{
//				ActionType:       recommendation.ActionTypePricing,
//				PrimaryAction:    recommendation.PricingActionQuoteMore.String(),
//				SupportingAction: pointer_utils.ToPointer("10"),
//			},
//			Conclusion:       pointer_utils.ToPointer(conclusion),
//			ConclusionReason: pointer_utils.ToPointer(conclusionReason),
//			CreatedAt:        time.Date(0, 0, -i, 0, 0, 0, 0, time.UTC),
//		})
//	}
//
//	for _, recosSet := range recosSetSlice {
//		err := s.h.UnderwritingDeps.ApplicationReviewWrapper.InsertRecommendation(s.h.Ctx, review.Id, &recosSet)
//		s.Require().NoError(err)
//	}
//
//	resp, err := s.h.Api.GetRecommendations(review.Id)
//	s.Require().NoError(err)
//	s.Require().NotNil(resp)
//	s.Require().Len(*resp, 2)
//
//	// make a map to validate existence of expected recommendations
//	recommendationsMap := make(map[string]oapi_uw.RecommendationInfo)
//	for _, r := range *resp {
//		recommendationsMap[r.ExperimentId] = r
//	}
//
//	for i := 1; i < 3; i++ {
//		r, ok := recommendationsMap[fmt.Sprintf("MY_EXPERIMENT_1234_%d", i)]
//		s.Require().True(ok)
//		observedObj := r
//		s.Require().NotNil(observedObj.Conclusion)
//		s.Require().Equal(oapi_uw.PartiallyAccept, *observedObj.Conclusion)
//		s.Require().Equal(conclusionReason, *observedObj.ConclusionReason)
//	}
//}

//FIXME: flaky test
//func (s *uwApisTestSuite) TestPatchRecommendation() {
//	review := setUpAndGetAppReview(s)
//
//	recosSet := recommendation.Set{
//		Panel:        app_review_widget.AppReviewPanelOperations,
//		ExperimentID: "MY_EXPERIMENT_1234",
//		Name:         "My Experiment",
//		Action: recommendation.RecommendedAction{
//			ActionType:       recommendation.ActionTypePricing,
//			PrimaryAction:    recommendation.PricingActionQuoteMore.String(),
//			SupportingAction: pointer_utils.ToPointer("10"),
//		},
//		Conclusion:       pointer_utils.ToPointer(recommendation.ConclusionPartiallyAccept),
//		ConclusionReason: pointer_utils.ToPointer("Gave 5% credit instead because of XYZ reason"),
//		CreatedAt:        time.Date(0, 0, -1, 0, 0, 0, 0, time.UTC),
//	}
//
//	err := s.h.UnderwritingDeps.ApplicationReviewWrapper.InsertRecommendation(s.h.Ctx, review.Id, &recosSet)
//	s.Require().NoError(err)
//
//	newConclusionReason := pointer_utils.ToPointer("Declining due to XYZ reason")
//	err = s.h.Api.PatchRecommendations(
//		review.Id, recosSet.ExperimentID, recommendation.ConclusionDecline.String(), newConclusionReason)
//	s.Require().NoError(err)
//
//	resp, err := s.h.Api.GetRecommendations(review.Id)
//	s.Require().NoError(err)
//	s.Require().NotNil(resp)
//	s.Require().Len(*resp, 1)
//	observedObj := (*resp)[0]
//	s.Require().Equal(recosSet.ExperimentID, observedObj.ExperimentId)
//	s.Require().NotNil(observedObj.Conclusion)
//	s.Require().Equal(oapi_uw.Decline, *observedObj.Conclusion)
//	s.Require().Equal(newConclusionReason, observedObj.ConclusionReason)
//}

func (s *uwApisTestSuite) TestUpdateProjectedInformation() {
	ctx := context.Background()
	appReview := s.appReviewFixture.ApplicationReview
	// Manually update the state of the app review to Pending
	err := s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, appReview.Id,
		func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
			review.State = uw.ApplicationReviewStatePending
			return review, nil
		})
	s.Require().NoError(err)
	err = s.h.LoginDefaultHighestAuthorityUW()
	s.Require().NoError(err)
	form := oapi_uw.ApplicationReviewOperationsProjectedInformationForm{
		Data: &struct {
			IsMinimumMileageGuaranteed *bool                          `json:"isMinimumMileageGuaranteed,omitempty"`
			Mileage                    *int                           `json:"mileage,omitempty"`
			Reason                     *oapi_uw.MileageEstimateReason `json:"reason,omitempty"`
			Units                      *int                           `json:"units,omitempty"`
		}{
			IsMinimumMileageGuaranteed: pointer_utils.Bool(true),
		},
	}
	err = s.h.Api.UpdateProjectedInformation(appReview.Id, form)
	s.Require().NoError(err)

	review, err := s.h.UnderwritingDeps.ApplicationReviewWrapper.GetReview(ctx, appReview.Id)
	s.Require().NoError(err)

	// verify that the projected information is updated
	s.Require().NotNil(review.Overrides.MinimumMileageGuarantee)
	s.Require().True(review.Overrides.MinimumMileageGuarantee.IsMinimumMileageGuaranteed)
}

// TestCloseApplicationWithWinCarrierPricing tests the close application API with winning carrier pricing data
func (s *uwApisTestSuite) TestCloseApplicationWithWinCarrierPricing() {
	// Find test data (only needs to be done once)
	validReason := s.getValidCloseReason()
	winCarrier := reasons.WinCarrierReason[0]

	testCases := []struct {
		name              string
		winCarrierPricing *oapi_uw.WinCarrierPricing
		expectError       bool
		errorContains     string
	}{
		{
			name: "With both auto liability and full price",
			winCarrierPricing: &oapi_uw.WinCarrierPricing{
				AutoLiability: pointer_utils.Int32(125000),
				FullPrice:     pointer_utils.Int32(275000),
			},
		},
		{
			name: "With only auto liability",
			winCarrierPricing: &oapi_uw.WinCarrierPricing{
				AutoLiability: pointer_utils.Int32(150000),
				FullPrice:     nil,
			},
		},
		{
			name: "With only full price",
			winCarrierPricing: &oapi_uw.WinCarrierPricing{
				AutoLiability: nil,
				FullPrice:     pointer_utils.Int32(300000),
			},
		},
		{
			name:              "With nil pricing data",
			winCarrierPricing: nil,
		},
		{
			name: "With negative auto liability should fail validation",
			winCarrierPricing: &oapi_uw.WinCarrierPricing{
				AutoLiability: pointer_utils.Int32(-100),
				FullPrice:     pointer_utils.Int32(275000),
			},
			expectError:   true,
			errorContains: "AutoLiability cannot be negative, got: -100",
		},
	}

	for _, tc := range testCases {
		s.T().Run(tc.name, func(t *testing.T) {
			s.T().Logf("🧪 Starting test case: %s", tc.name)

			// Setup: Create and prepare app review for closing
			reviewId := s.setupClosableAppReview()
			s.T().Logf("📋 Created app review with ID: %s", reviewId)

			// Execute: Close application with pricing data
			form := oapi_uw.ApplicationReviewCloseReasonsForm{
				ReasonsArray: []oapi_uw.ApplicationReviewCloseReasonObject{
					{
						ReasonId:    validReason.ReasonCode,
						Reason:      validReason.ReasonText,
						SubReasonId: validReason.SubReasonCode,
						SubReason:   validReason.SubReasonText,
					},
				},
				WinCarrier:        &winCarrier.WinCarrierCode,
				Comments:          "Test close with pricing data",
				WinCarrierPricing: tc.winCarrierPricing,
			}

			s.T().Logf("💾 Closing application with pricing: %+v", tc.winCarrierPricing)
			err := s.h.Api.CloseApplicationReview(s.h.Ctx, reviewId, form)

			if tc.expectError {
				s.Require().Error(err, "Expected validation error for test case: %s", tc.name)
				s.Require().Contains(err.Error(), tc.errorContains, "Error message should contain expected text")
				s.T().Logf("✅ Validation correctly rejected negative values")
			} else {
				s.Require().NoError(err, "Expected no error for valid pricing data")
				s.T().Logf("✅ Application closed successfully")

				// Verify: Check all data was stored correctly
				s.T().Logf("🔍 Verifying closed review data...")
				s.verifyClosedReview(reviewId, form, tc.winCarrierPricing, winCarrier.WinCarrierName)
			}
			s.T().Logf("🎉 Test case '%s' completed successfully!", tc.name)
		})
	}
}

// getValidCloseReason finds and returns a valid close reason for testing
func (s *uwApisTestSuite) getValidCloseReason() reasons.ReasonStruct {
	for _, reason := range reasons.ApplicationCloseReasons {
		if reason.IsActive {
			return reason.ReasonStruct
		}
	}
	s.Require().Fail("No active close reason found")
	return reasons.ReasonStruct{} // Never reached
}

// setupClosableAppReview creates an app review in the proper state for closing
func (s *uwApisTestSuite) setupClosableAppReview() string {
	reviewId, err := s.setupPendingAppReviewWithFixture()
	s.Require().NoError(err)

	reviewObj, err := s.h.UnderwritingDeps.ApplicationReviewWrapper.GetReview(s.h.Ctx, reviewId.String())
	s.Require().NoError(err)

	// Set app to correct state for closing
	err = s.h.UnderwritingDeps.ApplicationWrapper.UpdateApp(s.h.Ctx, reviewObj.ApplicationID,
		func(app application.Application) (application.Application, error) {
			app.State = state_enums.AppStateUnderUWReview
			return app, nil
		})
	s.Require().NoError(err)

	// Set review to correct state for closing
	err = s.h.UnderwritingDeps.ApplicationReviewWrapper.UpdateAppReview(s.h.Ctx, reviewId.String(),
		func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
			review.State = uw.ApplicationReviewStatePending
			return review, nil
		})
	s.Require().NoError(err)

	return reviewId.String()
}

// verifyClosedReview checks that the closed review has all expected data
func (s *uwApisTestSuite) verifyClosedReview(reviewId string, form oapi_uw.ApplicationReviewCloseReasonsForm, expectedPricing *oapi_uw.WinCarrierPricing, expectedWinCarrier string) {
	closedReview, err := s.h.UnderwritingDeps.ApplicationReviewWrapper.GetReview(s.h.Ctx, reviewId)
	s.Require().NoError(err)
	s.Require().Equal(uw.ApplicationReviewStateClosed, closedReview.State)

	// Verify basic close data
	s.Require().NotNil(closedReview.EndStateReasons)
	s.Require().NotNil(closedReview.EndStateReasons.CloseReasons)
	s.Require().Equal(form.Comments, closedReview.EndStateReasons.CloseReasons.Comments)
	s.Require().Equal(form.ReasonsArray[0].Reason, closedReview.EndStateReasons.CloseReasons.Reasons[0].ReasonText)
	s.Require().Equal(form.ReasonsArray[0].ReasonId, closedReview.EndStateReasons.CloseReasons.Reasons[0].ReasonCode)
	s.Require().Equal(expectedWinCarrier, *closedReview.EndStateReasons.CloseReasons.WinCarrier)

	// Verify pricing data
	if expectedPricing == nil {
		actualPricing := closedReview.EndStateReasons.CloseReasons.WinCarrierPricing
		s.Require().NotNil(actualPricing)
		s.Require().Nil(actualPricing.AutoLiability)
		s.Require().Nil(actualPricing.FullPrice)
	} else {
		actualPricing := closedReview.EndStateReasons.CloseReasons.WinCarrierPricing
		s.Require().NotNil(actualPricing)

		if expectedPricing.AutoLiability == nil {
			s.Require().Nil(actualPricing.AutoLiability)
		} else {
			s.Require().NotNil(actualPricing.AutoLiability)
			s.Require().Equal(*expectedPricing.AutoLiability, *actualPricing.AutoLiability)
		}

		if expectedPricing.FullPrice == nil {
			s.Require().Nil(actualPricing.FullPrice)
		} else {
			s.Require().NotNil(actualPricing.FullPrice)
			s.Require().Equal(*expectedPricing.FullPrice, *actualPricing.FullPrice)
		}
	}
}
