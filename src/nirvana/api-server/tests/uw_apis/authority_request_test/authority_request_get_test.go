package authority_request_test

import (
	"encoding/json"
	"net/http"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/oapi-codegen/runtime/types"
	"github.com/stretchr/testify/suite"
	"github.com/volatiletech/null/v8"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"

	external_deps "nirvanatech.com/nirvana/api-server/interceptors/external/deps"
	"nirvanatech.com/nirvana/api-server/test_utils"
	authorities_wrapper "nirvanatech.com/nirvana/db-api/db_wrappers/authorities"
	"nirvanatech.com/nirvana/db-api/db_wrappers/authorities/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/api_server_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/application_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/application_review_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/fixture_utils"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/users_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
	oapi_uw "nirvanatech.com/nirvana/openapi-specs/components/underwriting"
)

func TestAuthorityRequestGetSuite(t *testing.T) {
	suite.Run(t, new(AuthorityRequestGetTestSuite))
}

type AuthorityRequestGetTestSuite struct {
	suite.Suite
	h                   *test_utils.ApiServerHarness
	deps                external_deps.Deps
	fxapp               *fxtest.App
	apiServer           *api_server_fixture.ApiServer
	appFixture          *application_fixture.ApplicationsFixture
	appReviewFixture    *application_review_fixture.ApplicationReviewsFixture
	usersFixture        *users_fixture.UsersFixture
}

func (s *AuthorityRequestGetTestSuite) SetupTest() {
	var env struct {
		fx.In
		Harness           *test_utils.ApiServerHarness
		ApiServer         api_server_fixture.ApiServer
		AppFixture        *application_fixture.ApplicationsFixture
		AppReviewFixture  *application_review_fixture.ApplicationReviewsFixture
		UsersFixture      *users_fixture.UsersFixture
	}
	
	s.fxapp = testloader.RequireStart(s.T(), &env)
	s.h = env.Harness
	s.deps = s.h.ExternalDeps
	s.apiServer = &env.ApiServer
	s.appFixture = env.AppFixture
	s.appReviewFixture = env.AppReviewFixture
	s.usersFixture = env.UsersFixture
	
	// Create default test data
	s.Require().NoError(s.h.CreateDefaultLowestAuthorityUW())
	err := s.h.CreateDefaultAgency()
	s.Require().Nil(err)
	err = s.h.CreateDefaultAgent()
	s.Require().Nil(err)
	
	// Create an underwriting manager for testing
	s.Require().NoError(s.h.CreateUnderwritingManager())
}

func (s *AuthorityRequestGetTestSuite) TearDownTest() {
	defer s.fxapp.RequireStop()
}

func (s *AuthorityRequestGetTestSuite) TestGetAuthorityRequest() {
	testCases := []struct {
		name                string
		applicationReviewID uuid.UUID
		expectedStatus      int
		expectedError       string
	}{
		{
			name:                "InvalidUUIDFormat",
			applicationReviewID: uuid.Nil,
			expectedStatus:      http.StatusNotFound,
			expectedError:       "not found",
		},
		{
			name:                "NonExistentApplicationReview",
			applicationReviewID: uuid.New(),
			expectedStatus:      http.StatusNotFound,
			expectedError:       "not found",
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			// Execute - GetAuthorityRequest now takes applicationReviewId
			uwClient := s.apiServer.UWClient(&s.usersFixture.Superuser)
			httpResp, err := uwClient.GetAuthorityRequest(s.h.Ctx, types.UUID(tc.applicationReviewID))
			s.Require().NoError(err)

			// Assert
			s.Equal(tc.expectedStatus, httpResp.StatusCode)
			if tc.expectedStatus >= 400 {
				errorResp := fixture_utils.ReadResponse[oapi_common.ErrorMessage](s.T(), httpResp)
				s.Contains(errorResp.Message, tc.expectedError)
			}
		})
	}
}

func (s *AuthorityRequestGetTestSuite) TestGetLatestAuthorityRequestForApplicationReview() {
	// Create an authority request first
	appReviewID := s.appReviewFixture.ApplicationReview.Id
	
	// Create decline request payload
	declinePayload := oapi_uw.AuthorityDeclineRequestPayload{
		HasHardRuleViolations: false,
		DeclineReasons: &[]oapi_uw.ApplicationReviewDeclineReasonObject{
			{
				Reason:       "Test Reason",
				ReasonId:     "T001",
				SubReason:    "Test Sub Reason",
				SubReasonId:  "T001-01",
				ExternalNote: "Test decline reason",
			},
		},
		TargetQuotePrice:   func() *float64 { p := 10000.0; return &p }(),
		NoQuoteExplanation: func() *string { s := "Test explanation"; return &s }(),
	}
	
	requestData := &oapi_uw.AuthorityRequest_RequestData{}
	err := requestData.FromAuthorityDeclineRequestPayload(declinePayload)
	s.Require().NoError(err)
	
	authorityRequest := oapi_uw.AuthorityRequest{
		ApplicationReviewId: types.UUID(uuid.MustParse(appReviewID)),
		RequestType:         oapi_uw.RequestTypeDecline,
		RequestData:         *requestData,
	}

	// Create the authority request
	uwClient := s.apiServer.UWClient(&s.usersFixture.Superuser)
	httpResp, err := uwClient.CreateAuthorityRequest(s.h.Ctx, authorityRequest)
	s.Require().NoError(err)
	
	if httpResp.StatusCode == http.StatusCreated {
		createResp := fixture_utils.ReadResponse[oapi_uw.AuthorityRequestResponse](s.T(), httpResp)
		
		// Now get the latest request for this application review
		httpResp, err = uwClient.GetAuthorityRequest(s.h.Ctx, types.UUID(uuid.MustParse(appReviewID)))
		s.Require().NoError(err)
		s.Equal(http.StatusOK, httpResp.StatusCode)
		
		// Verify we got the correct request
		getResp := fixture_utils.ReadResponse[oapi_uw.AuthorityRequestDetails](s.T(), httpResp)
		s.Equal(createResp.RequestId, getResp.RequestId)
		s.Equal(types.UUID(uuid.MustParse(appReviewID)), getResp.ApplicationReviewId)
		s.Equal(oapi_uw.RequestTypeDecline, getResp.RequestType)
		s.Equal(oapi_uw.AuthorityRequestStatePending, getResp.State)
		
		// Verify isUnderwritingManager field is present
		// Superuser without UnderwriterManagerRole should NOT be considered an underwriting manager
		s.NotNil(getResp.IsUnderwritingManager)
		s.False(*getResp.IsUnderwritingManager, "Superuser without UnderwriterManagerRole should NOT be considered an underwriting manager")
	}
}

func (s *AuthorityRequestGetTestSuite) TestGetAuthorityRequest_IsUnderwritingManagerFlag() {
	// Create an authority request first
	appReviewID := s.appReviewFixture.ApplicationReview.Id
	
	// Create decline request payload
	declinePayload := oapi_uw.AuthorityDeclineRequestPayload{
		HasHardRuleViolations: false,
		DeclineReasons: &[]oapi_uw.ApplicationReviewDeclineReasonObject{
			{
				Reason:       "Test Reason",
				ReasonId:     "T001",
				SubReason:    "Test Sub Reason",
				SubReasonId:  "T001-01",
				ExternalNote: "Test decline reason",
			},
		},
		TargetQuotePrice:   func() *float64 { p := 10000.0; return &p }(),
		NoQuoteExplanation: func() *string { s := "Test explanation"; return &s }(),
	}
	
	requestData := &oapi_uw.AuthorityRequest_RequestData{}
	err := requestData.FromAuthorityDeclineRequestPayload(declinePayload)
	s.Require().NoError(err)
	
	authorityRequest := oapi_uw.AuthorityRequest{
		ApplicationReviewId: types.UUID(uuid.MustParse(appReviewID)),
		RequestType:         oapi_uw.RequestTypeDecline,
		RequestData:         *requestData,
	}

	// Create the authority request
	uwClient := s.apiServer.UWClient(&s.usersFixture.Superuser)
	httpResp, err := uwClient.CreateAuthorityRequest(s.h.Ctx, authorityRequest)
	s.Require().NoError(err)
	s.Equal(http.StatusCreated, httpResp.StatusCode)
	
	// Test 1: Superuser should NOT be considered an underwriting manager (unless they have the specific role)
	httpResp, err = uwClient.GetAuthorityRequest(s.h.Ctx, types.UUID(uuid.MustParse(appReviewID)))
	s.Require().NoError(err)
	s.Equal(http.StatusOK, httpResp.StatusCode)
	
	getResp := fixture_utils.ReadResponse[oapi_uw.AuthorityRequestDetails](s.T(), httpResp)
	s.NotNil(getResp.IsUnderwritingManager)
	s.False(*getResp.IsUnderwritingManager, "Superuser without UnderwriterManagerRole should NOT be considered an underwriting manager")
	
	// Test 2: Get the underwriting manager user we created and log them in
	// First, we need to get the user and create a session for them
	managerAuthzUser, err := s.h.ApplicationDeps.AuthWrapper.FetchAuthzUserByEmail(s.h.Ctx, "<EMAIL>")
	s.Require().NoError(err)
	s.Require().NotNil(managerAuthzUser, "Should find the underwriting manager user")
	
	// Create a test user struct that matches what the API client expects
	// We need to convert authz.User to auth.User for the test fixture
	managerTestUser := users_fixture.User{
		User: &auth.User{
			UserInfo: managerAuthzUser.UserInfo,
		},
		Password: "FakePass123", // This is defaultUWPassword from the harness
		Roles: managerAuthzUser.Roles,
	}

	uwClientManager := s.apiServer.UWClient(&managerTestUser)
	httpResp, err = uwClientManager.GetAuthorityRequest(s.h.Ctx, types.UUID(uuid.MustParse(appReviewID)))
	s.Require().NoError(err)
	s.Equal(http.StatusOK, httpResp.StatusCode)

	getResp = fixture_utils.ReadResponse[oapi_uw.AuthorityRequestDetails](s.T(), httpResp)
	s.NotNil(getResp.IsUnderwritingManager)
	s.True(*getResp.IsUnderwritingManager, "Underwriter with manager role should be considered a manager")

	// Test 3: John Wayne (only has Level1UnderwriterRole) should NOT be considered a manager
	var regularUW *users_fixture.User
	for i := range s.usersFixture.Underwriters {
		uw := &s.usersFixture.Underwriters[i]
		if uw.Email == "<EMAIL>" {
			regularUW = uw
			break
		}
	}
	s.Require().NotNil(regularUW, "Should find John Wayne in underwriters fixture")

	uwClientRegular := s.apiServer.UWClient(regularUW)
	httpResp, err = uwClientRegular.GetAuthorityRequest(s.h.Ctx, types.UUID(uuid.MustParse(appReviewID)))
	s.Require().NoError(err)
	s.Equal(http.StatusOK, httpResp.StatusCode)

	getResp = fixture_utils.ReadResponse[oapi_uw.AuthorityRequestDetails](s.T(), httpResp)
	s.NotNil(getResp.IsUnderwritingManager)
	s.False(*getResp.IsUnderwritingManager, "Regular underwriter should NOT be considered a manager")
}

func (s *AuthorityRequestGetTestSuite) TestConvertToAuthorityRequestDetails() {
	// Common test data
	requestID := uuid.New()
	applicationReviewID := uuid.New()
	requesterID := uuid.New()
	reviewerID := uuid.New()
	
	now := time.Now()
	submittedAt := now.Add(-24 * time.Hour)
	reviewedAt := now.Add(-12 * time.Hour)
	executedAt := now.Add(-6 * time.Hour)
	
	requestDataMap := map[string]interface{}{
		"reason": "Test reason",
		"priority": "high",
	}
	requestDataJSON, _ := json.Marshal(requestDataMap)

	testCases := []struct {
		name     string
		input    *authorities_wrapper.AuthorityRequest
		validate func(result oapi_uw.AuthorityRequestDetails)
	}{
		{
			name: "ConvertWithAllFields",
			input: &authorities_wrapper.AuthorityRequest{
				ID:                  requestID,
				ApplicationReviewID: applicationReviewID,
				RequestType:         enums.RequestTypeDecline,
				State:               enums.RequestStateApproved,
				RequestData:         null.JSONFrom(requestDataJSON),
				RequesterID:         requesterID,
				LastReviewedBy:      null.StringFrom(reviewerID.String()),
				ReviewData:          null.JSONFrom([]byte(`{"decision": "approved"}`)),
				CreatedAt:           now,
				UpdatedAt:           now,
				SubmittedAt:         null.TimeFrom(submittedAt),
				LastReviewedAt:      null.TimeFrom(reviewedAt),
				ExecutedAt:          null.TimeFrom(executedAt),
			},
			validate: func(result oapi_uw.AuthorityRequestDetails) {
				s.Equal(requestID, result.RequestId)
				s.Equal(applicationReviewID, result.ApplicationReviewId)
				s.Equal(oapi_uw.RequestTypeDecline, result.RequestType)
				s.Equal(oapi_uw.AuthorityRequestStateApproved, result.State)
				s.Equal(requesterID, result.RequesterId)
				s.Equal(now, result.CreatedAt)
				s.Equal(now, result.UpdatedAt)
				s.NotNil(result.SubmittedAt)
				s.Equal(submittedAt, *result.SubmittedAt)
				s.NotNil(result.LastReviewedBy)
				s.Equal(reviewerID, *result.LastReviewedBy)
				s.NotNil(result.LastReviewedAt)
				s.Equal(reviewedAt, *result.LastReviewedAt)
				s.NotNil(result.ExecutedAt)
				s.Equal(executedAt, *result.ExecutedAt)
				// RequestData should be properly deserialized union type
				s.NotNil(result.RequestData)
				// Test that the JSON content is correct by marshalling back and comparing
				expectedJSON, _ := json.Marshal(requestDataMap)
				actualJSON, err := result.RequestData.MarshalJSON()
				s.NoError(err)
				s.JSONEq(string(expectedJSON), string(actualJSON))
			},
		},
		{
			name: "ConvertWithMinimalFields",
			input: &authorities_wrapper.AuthorityRequest{
				ID:                  requestID,
				ApplicationReviewID: applicationReviewID,
				RequestType:         enums.RequestTypeDecline,
				State:               enums.RequestStatePending,
				RequestData:         null.JSON{},
				RequesterID:         requesterID,
				LastReviewedBy:      null.String{},
				ReviewData:          null.JSON{},
				CreatedAt:           now,
				UpdatedAt:           now,
				SubmittedAt:         null.Time{},
				LastReviewedAt:      null.Time{},
				ExecutedAt:          null.Time{},
			},
			validate: func(result oapi_uw.AuthorityRequestDetails) {
				s.Equal(requestID, result.RequestId)
				s.Equal(applicationReviewID, result.ApplicationReviewId)
				s.Equal(oapi_uw.RequestTypeDecline, result.RequestType)
				s.Equal(oapi_uw.AuthorityRequestStatePending, result.State)
				s.Equal(requesterID, result.RequesterId)
				s.Equal(now, result.CreatedAt)
				s.Equal(now, result.UpdatedAt)
				s.Nil(result.SubmittedAt)
				s.Nil(result.LastReviewedBy)
				s.Nil(result.LastReviewedAt)
				s.Nil(result.ExecutedAt)
				// RequestData is not a pointer, so it should be zero value (empty union)
				// Test that it marshals to null/empty
				actualJSON, err := result.RequestData.MarshalJSON()
				s.NoError(err)
				s.Equal("null", string(actualJSON)) // empty/null union should marshal to "null"
			},
		},
		{
			name: "ConvertWithManagerNote",
			input: &authorities_wrapper.AuthorityRequest{
				ID:                  requestID,
				ApplicationReviewID: applicationReviewID,
				RequestType:         enums.RequestTypeDecline,
				State:               enums.RequestStateUnderUWManagerReview,
				RequesterID:         requesterID,
				ReviewData: null.JSONFrom([]byte(`{"manager_note":"This request needs additional documentation before approval"}`)),
				CreatedAt:           now,
				UpdatedAt:           now,
			},
			validate: func(result oapi_uw.AuthorityRequestDetails) {
				s.Equal(oapi_uw.AuthorityRequestStateUnderUwManagerReview, result.State)
				s.NotNil(result.ManagerNote)
				s.Equal("This request needs additional documentation before approval", *result.ManagerNote)
			},
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			result := convertToAuthorityRequestDetails(tc.input)
			tc.validate(result)
		})
	}
}

// convertToAuthorityRequestDetails converts a database authority request to API response format
func convertToAuthorityRequestDetails(request *authorities_wrapper.AuthorityRequest) oapi_uw.AuthorityRequestDetails {
	// Convert state using direct 1:1 mapping
	var state oapi_uw.AuthorityRequestState
	switch request.State {
	case enums.RequestStatePending:
		state = oapi_uw.AuthorityRequestStatePending
	case enums.RequestStateUnderUWManagerReview:
		state = oapi_uw.AuthorityRequestStateUnderUwManagerReview
	case enums.RequestStateApproved:
		state = oapi_uw.AuthorityRequestStateApproved
	default:
		// Default to pending for any unmapped states
		state = oapi_uw.AuthorityRequestStatePending
	}

	// Convert request type
	var requestType oapi_uw.AuthorityRequestType
	switch request.RequestType {
	case enums.RequestTypeDecline:
		requestType = oapi_uw.RequestTypeDecline
	case enums.RequestTypeInvalid:
		// Default to decline for invalid types
		requestType = oapi_uw.RequestTypeDecline
	default:
		// Default to decline for any unmapped types
		requestType = oapi_uw.RequestTypeDecline
	}

	result := oapi_uw.AuthorityRequestDetails{
		RequestId:           request.ID,
		ApplicationReviewId: request.ApplicationReviewID,
		RequestType:         requestType,
		State:               state,
		RequesterId:         request.RequesterID,
		CreatedAt:           request.CreatedAt,
		UpdatedAt:           request.UpdatedAt,
	}

	// Set optional fields if they exist
	if request.SubmittedAt.Valid {
		result.SubmittedAt = &request.SubmittedAt.Time
	}
	if request.LastReviewedBy.Valid {
		if reviewedByUUID, err := uuid.Parse(request.LastReviewedBy.String); err == nil {
			result.LastReviewedBy = &reviewedByUUID
		}
	}
	if request.LastReviewedAt.Valid {
		result.LastReviewedAt = &request.LastReviewedAt.Time
	}
	if request.ExecutedAt.Valid {
		result.ExecutedAt = &request.ExecutedAt.Time
	}
	
	// Handle request_data - convert from JSON to union type
	if request.RequestData.Valid {
		var requestData oapi_uw.AuthorityRequestDetails_RequestData
		if err := requestData.UnmarshalJSON(request.RequestData.JSON); err == nil {
			result.RequestData = requestData
		}
	}
	
	// Extract manager_note from review_data JSON if available
	if request.ReviewData.Valid {
		if managerNote := extractManagerNoteFromReviewData(request.ReviewData.JSON); managerNote != nil {
			result.ManagerNote = managerNote
		}
	}

	return result
}

// ReviewDataWithManagerNote represents the structure of review_data JSON with manager note
type ReviewDataWithManagerNote struct {
	ManagerNote *string `json:"manager_note,omitempty"`
}

// extractManagerNoteFromReviewData extracts manager_note from the review_data JSON field
func extractManagerNoteFromReviewData(reviewData []byte) *string {
	var data ReviewDataWithManagerNote
	if err := json.Unmarshal(reviewData, &data); err != nil {
		return nil
	}
	return data.ManagerNote
}