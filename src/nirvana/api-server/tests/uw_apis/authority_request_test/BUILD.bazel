load("@io_bazel_rules_go//go:def.bzl", "go_test")

go_test(
    name = "authority_request_test_test",
    srcs = [
        "authority_request_create_test.go",
        "authority_request_get_test.go",
        "authority_request_integration_test.go",
        "authority_request_list_test.go",
        "authority_request_process_test.go",
    ],
    data = [
        "testdata/authority_request_fixtures.sql",
    ],
    deps = [
        "//nirvana/api-server/interceptors/external/deps",
        "//nirvana/api-server/test_utils",
        "//nirvana/db-api/db_wrappers/auth",
        "//nirvana/db-api/db_wrappers/authorities",
        "//nirvana/db-api/db_wrappers/authorities/enums",
        "//nirvana/infra/fx/testfixtures/api_server_fixture",
        "//nirvana/infra/fx/testfixtures/application_fixture",
        "//nirvana/infra/fx/testfixtures/application_review_fixture",
        "//nirvana/infra/fx/testfixtures/fixture_utils",
        "//nirvana/infra/fx/testfixtures/users_fixture",
        "//nirvana/infra/fx/testloader",
        "//nirvana/openapi-specs/components/common",
        "//nirvana/openapi-specs/components/underwriting",
        "@com_github_google_uuid//:uuid",
        "@com_github_oapi_codegen_runtime//types",
        "@com_github_stretchr_testify//suite",
        "@com_github_volatiletech_null_v8//:null",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
    ],
)
