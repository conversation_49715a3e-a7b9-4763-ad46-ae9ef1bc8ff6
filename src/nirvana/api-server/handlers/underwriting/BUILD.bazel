load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "underwriting",
    srcs = [
        "add_equipment_unit.go",
        "ancillary_coverage.go",
        "app_review_helper.go",
        "authority_request_converters.go",
        "authority_request_create.go",
        "authority_request_get.go",
        "authority_request_list.go",
        "authority_request_process.go",
        "close_app_review.go",
        "confirm_bindable_quote.go",
        "create_bindable_quote.go",
        "create_driver.go",
        "current_status.go",
        "decline_app_review.go",
        "get_account_grade.go",
        "get_actions.go",
        "get_additional_info_units.go",
        "get_app_review.go",
        "get_app_review_list_count.go",
        "get_app_review_list_v2.go",
        "get_assignees.go",
        "get_bindable_quote.go",
        "get_boards_version.go",
        "get_claim_history.go",
        "get_claim_history_description.go",
        "get_cleared_application.go",
        "get_close_reasons.go",
        "get_coverages.go",
        "get_data_completion_app_review_list.go",
        "get_decline_reasons.go",
        "get_document_link.go",
        "get_documents.go",
        "get_drivers_list.go",
        "get_equip_owner_operators.go",
        "get_equip_safety_usage.go",
        "get_equip_units.go",
        "get_factor_ranking.go",
        "get_financials_data.go",
        "get_large_losses.go",
        "get_loss_averages.go",
        "get_loss_summary.go",
        "get_loss_summary_v2.go",
        "get_mileage_estimate_reasons.go",
        "get_mvr_problems.go",
        "get_notes.go",
        "get_ops_commodities.go",
        "get_ops_commodities_supported_operations.go",
        "get_ops_customers.go",
        "get_ops_fleet_history.go",
        "get_ops_garaging_location.go",
        "get_ops_hazard_zones.go",
        "get_ops_operating_classes.go",
        "get_ops_projected_information.go",
        "get_ops_radius_of_operation.go",
        "get_ops_terminal_locations.go",
        "get_ops_vehicle_zones.go",
        "get_ops_years_in_business.go",
        "get_overview_recommended_action.go",
        "get_package_negotiated_rates.go",
        "get_package_type.go",
        "get_permissions.go",
        "get_quote.go",
        "get_recommendations.go",
        "get_recommended_action_notification.go",
        "get_recommended_action_trail.go",
        "get_review_readiness_tasks.go",
        "get_safer_dot_info.go",
        "get_safety_basic_score_threshold.go",
        "get_safety_basic_score_trend.go",
        "get_safety_crash_records.go",
        "get_safety_dot_rating.go",
        "get_safety_iss_score_trend.go",
        "get_safety_oos_violations.go",
        "get_safety_score.go",
        "get_safety_score_v2.go",
        "get_safety_severe_violations.go",
        "get_target_price.go",
        "get_telematics_info.go",
        "get_underwriters.go",
        "get_vin_problems.go",
        "get_vin_visibility.go",
        "get_vin_visibility_checklist.go",
        "get_widget_summary.go",
        "log_uw_events.go",
        "mst_referral.go",
        "overrides.go",
        "panel_notes.go",
        "patch_ops_terminal_locations.go",
        "post_app_review_widget_backfill.go",
        "post_app_review_widget_repull.go",
        "post_clear_application.go",
        "post_document.go",
        "post_mvr_pull.go",
        "post_recalculate_telematics_connection_state.go",
        "post_regenerate_authority_info.go",
        "post_report.go",
        "post_set_attract_score.go",
        "put_mvr_problems.go",
        "put_target_price.go",
        "put_vin_problems.go",
        "put_vin_visibility_checklist.go",
        "ready_application_review.go",
        "refetch_mvr.go",
        "risk_factors.go",
        "risk_factors_mappers.go",
        "risk_factors_validators.go",
        "rollback_app_review.go",
        "select_rating_address.go",
        "set_recommended_action_notification_acked.go",
        "update_account_grade.go",
        "update_app_review.go",
        "update_assignees.go",
        "update_boards_info.go",
        "update_coverages.go",
        "update_driver.go",
        "update_drivers_list.go",
        "update_equip_units.go",
        "update_financials_data.go",
        "update_large_losses.go",
        "update_loss_averages.go",
        "update_loss_summary.go",
        "update_loss_summary_v2.go",
        "update_notes.go",
        "update_ops_commodities.go",
        "update_ops_fleet_history.go",
        "update_ops_garaging_location.go",
        "update_ops_hazard_zones.go",
        "update_ops_operating_classes.go",
        "update_ops_projected_information.go",
        "update_ops_radius_of_operation.go",
        "update_ops_vehicle_zones.go",
        "update_ops_years_in_business.go",
        "update_package_negotiated_rates.go",
        "update_package_type.go",
        "update_quote.go",
        "update_recommendation.go",
        "update_review_readiness_tasks.go",
        "update_safety_basic_score_threshold.go",
        "update_safety_basic_score_trend.go",
        "update_safety_crash_records.go",
        "update_safety_dot_rating.go",
        "update_safety_iss_score_trend.go",
        "update_safety_oos_violations.go",
        "update_safety_score.go",
        "update_safety_score_v2.go",
        "update_safety_severe_violations.go",
        "update_summary.go",
        "update_telematics_reminder_email_info.go",
        "utils.go",
        "validate_endpoint_state.go",
        "widget_utils.go",
    ],
    importpath = "nirvanatech.com/nirvana/api-server/handlers/underwriting",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/api-server/common",
        "//nirvana/api-server/handlers/application",
        "//nirvana/api-server/handlers/reports",
        "//nirvana/api-server/handlers/underwriting/utils",
        "//nirvana/api-server/handlers/utils",
        "//nirvana/api-server/helpers",
        "//nirvana/api-server/interceptors/reports/deps",
        "//nirvana/api-server/interceptors/underwriting/deps",
        "//nirvana/api-server/quoting_jobber",
        "//nirvana/common-go/application-util",
        "//nirvana/common-go/errgroup",
        "//nirvana/common-go/feature_flag_lib",
        "//nirvana/common-go/file_upload_lib/enums",
        "//nirvana/common-go/log",
        "//nirvana/common-go/map_utils",
        "//nirvana/common-go/math_utils",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/problem",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/common-go/tracing",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/app_review_widget",
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/authorities",
        "//nirvana/db-api/db_wrappers/authorities/enums",
        "//nirvana/db-api/db_wrappers/policy",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/db-api/db_wrappers/uw",
        "//nirvana/db-api/db_wrappers/uw/mst_referral",
        "//nirvana/events",
        "//nirvana/events/underwriter_events",
        "//nirvana/events/underwriter_events/assignment_events",
        "//nirvana/events/underwriter_events/panel_events",
        "//nirvana/external_client/salesforce/jobs/enums",
        "//nirvana/external_client/salesforce/wrapper",
        "//nirvana/external_data_management/interceptors_management",
        "//nirvana/features/radius_operation_telematics",
        "//nirvana/fmcsa/safety",
        "//nirvana/infra/authz",
        "//nirvana/infra/config",
        "//nirvana/infra/constants",
        "//nirvana/insurance-bundle/service",
        "//nirvana/insurance-core/proto",
        "//nirvana/jobber/job_utils",
        "//nirvana/jobber/jtypes",
        "//nirvana/openapi-specs/api_server_uw/fleet_uw",
        "//nirvana/openapi-specs/components/common",
        "//nirvana/openapi-specs/components/underwriting",
        "//nirvana/pricing/explainability/entities/raters/factor_utils",
        "//nirvana/quoting/app_state_machine",
        "//nirvana/quoting/app_state_machine/cerrors",
        "//nirvana/quoting/app_state_machine/enums",
        "//nirvana/quoting/clearance",
        "//nirvana/quoting/clearance/enums",
        "//nirvana/quoting/jobs",
        "//nirvana/quoting/quoting_logic",
        "//nirvana/quoting/utils",
        "//nirvana/rating/data_fetching/mvr_fetching",
        "//nirvana/rating/data_processing/vin_processing",
        "//nirvana/rating/data_processing/vin_processing/enums",
        "//nirvana/rating/data_processing/vin_processing/iso_utils",
        "//nirvana/rating/models/models_release",
        "//nirvana/reporting/enums",
        "//nirvana/servers/telematicsv2",
        "//nirvana/telematics",
        "//nirvana/underwriting/app_review/actions",
        "//nirvana/underwriting/app_review/actions/permission",
        "//nirvana/underwriting/app_review/actions/reasons",
        "//nirvana/underwriting/app_review/authority",
        "//nirvana/underwriting/app_review/problems",
        "//nirvana/underwriting/app_review/utils",
        "//nirvana/underwriting/app_review/vin_visibility",
        "//nirvana/underwriting/app_review/widgets",
        "//nirvana/underwriting/app_review/widgets/drivers",
        "//nirvana/underwriting/app_review/widgets/global",
        "//nirvana/underwriting/app_review/widgets/losses",
        "//nirvana/underwriting/app_review/widgets/lossesv2",
        "//nirvana/underwriting/app_review/widgets/safety/scorev2",
        "//nirvana/underwriting/app_review_widget_manager",
        "//nirvana/underwriting/authority",
        "//nirvana/underwriting/authority/enums",
        "//nirvana/underwriting/authority/types",
        "//nirvana/underwriting/common-utils",
        "//nirvana/underwriting/jobs",
        "//nirvana/underwriting/jobs/impl/widgets/panels/drivers/drivers_list",
        "//nirvana/underwriting/jobs/impl/widgets/panels/losses/large_loss",
        "//nirvana/underwriting/jobs/impl/widgets/panels/losses/loss_summary",
        "//nirvana/underwriting/jobs/impl/widgets/panels/operations/commodities",
        "//nirvana/underwriting/jobs/impl/widgets/panels/operations/projected_information",
        "//nirvana/underwriting/jobs/impl/widgets/panels/operations/radius_of_operation",
        "//nirvana/underwriting/jobs/impl/widgets/panels/operations/terminal_locations",
        "//nirvana/underwriting/modal",
        "//nirvana/underwriting/overview_panel_appetite_factors",
        "//nirvana/underwriting/platform/safety_score",
        "//nirvana/underwriting/recommendations",
        "//nirvana/underwriting/risk_factors",
        "//nirvana/underwriting/rule-engine/appetite_factors/appetite_factor",
        "//nirvana/underwriting/rule-engine/authorities",
        "//nirvana/underwriting/state_machine/review_readiness_state_machine",
        "//nirvana/underwriting/state_machine/telematics_connection_state_machine",
        "//nirvana/underwriting/task",
        "//nirvana/underwriting/task/taskmanager",
        "//nirvana/underwriting/utils",
        "@com_github_cactus_go_statsd_client_v5//statsd",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_grpc_ecosystem_grpc_gateway_v2//runtime",
        "@com_github_looplab_fsm//:fsm",
        "@com_github_oapi_codegen_runtime//types",
        "@io_opentelemetry_go_otel//attribute",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//types/known/emptypb",
    ],
)
