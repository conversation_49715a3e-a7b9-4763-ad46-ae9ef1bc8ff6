package underwriting

import (
	"context"
	"database/sql"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/interceptors/underwriting/deps"
	"nirvanatech.com/nirvana/infra/authz"
	oapi_uw "nirvanatech.com/nirvana/openapi-specs/components/underwriting"
)

type GetAuthorityRequestRequest struct {
	ApplicationReviewID string
}

func HandleGetAuthorityRequest(
	ctx context.Context,
	deps deps.Deps,
	req GetAuthorityRequestRequest,
) (*oapi_uw.AuthorityRequestDetails, error) {
	// Parse and validate application review ID
	applicationReviewID, err := uuid.Parse(req.ApplicationReviewID)
	if err != nil {
		return nil, common.NewNirvanaBadRequestErrorWithReason(err, "Invalid application review ID format")
	}

	// Get the latest authority request for this application review
	request, err := deps.AuthorityManager.GetLatestRequestForApplication(ctx, applicationReviewID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, common.NewNirvanaNotFoundErrorf(nil, "authority request for application review", applicationReviewID.String())
		}
		return nil, common.NewNirvanaInternalServerWithReason(err, "Failed to get latest authority request")
	}

	// Convert internal response to OpenAPI response
	oapiResponse, err := convertToAuthorityRequestDetails(request)
	if err != nil {
		return nil, common.NewNirvanaInternalServerWithReason(err, "Failed to convert authority request details")
	}

	// Check if the current user has underwriting manager role
	user := authz.UserFromContext(ctx)
	isUnderwritingManager := false
	for _, role := range user.Roles {
		if role.Group == authz.UnderwriterManagerRole {
			isUnderwritingManager = true
			break
		}
	}
	oapiResponse.IsUnderwritingManager = &isUnderwritingManager

	return &oapiResponse, nil
}
