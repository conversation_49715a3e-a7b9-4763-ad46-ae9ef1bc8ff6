package aggregation

import (
	"context"
	"sort"
	"strings"
	"time"

	"github.com/cockroachdb/errors"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/external/pibit"
)

type CoveragePeriodsWithGaps struct {
	Coverage app_enums.Coverage
	Periods  []PeriodsWithGaps
}

type PeriodsWithGaps struct {
	AggregationPeriod  pibit.Period
	MissingSubPeriods  []pibit.MissingSubPeriod
	NumberOfPowerUnits int32
}

func AddGapsToCoveragePeriods(ctx context.Context, resolution DeduplicationResult, aggregationPeriods []CoverageAggregationPeriods) ([]CoveragePeriodsWithGaps, error) {
	var coveragePeriodsWithGaps []CoveragePeriodsWithGaps

	for _, coveragePeriod := range aggregationPeriods {
		// Extract all covered periods for this specific coverage type.
		allCoveredForCoverage, err := extractCoveredPeriods(ctx, resolution, coveragePeriod.Coverage)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to extract covered periods for coverage %s", coveragePeriod.Coverage)
		}

		// Find gaps within each defined aggregation period for this coverage.
		periodGaps := findGapsInAggregationPeriods(coveragePeriod.Periods, allCoveredForCoverage)

		// Sort the results by the aggregation period start date for consistent output.
		sort.Slice(periodGaps, func(i, j int) bool {
			return periodGaps[i].AggregationPeriod.FromDate.Before(periodGaps[j].AggregationPeriod.FromDate)
		})

		coveragePeriodsWithGaps = append(coveragePeriodsWithGaps, CoveragePeriodsWithGaps{
			Coverage: coveragePeriod.Coverage,
			Periods:  periodGaps,
		})
	}

	return coveragePeriodsWithGaps, nil
}

func extractCoveredPeriods(ctx context.Context, resolution DeduplicationResult, coverage app_enums.Coverage) ([]pibit.Period, error) {
	var periods []pibit.Period
	seen := make(map[string]bool)

	for _, lossLine := range resolution.SelectedLosses {
		if !shouldPickUpLoss(lossLine, coverage) {
			continue
		}

		key := buildPolicyKey(lossLine.Policy)
		if seen[key] {
			continue
		}
		seen[key] = true

		periods = append(periods, pibit.Period{
			FromDate: *lossLine.Policy.EffDate,
			ToDate:   *lossLine.Policy.ExpDate,
		})
	}

	return periods, nil
}

func shouldPickUpLoss(lossLine LossLineWithPolicyAndClaim, coverage app_enums.Coverage) bool {
	coverageInferred := lossLine.Loss.CoverageInferred
	if coverageInferred == nil || lossLine.Policy.EffDate == nil || lossLine.Policy.ExpDate == nil {
		return false
	}

	mappedCoverage, err := getAppEnumCoverageFromCoverageInferred(*coverageInferred)
	if err != nil {
		return false
	}

	return coverage == *mappedCoverage
}

// findGapsInAggregationPeriods isolates relevant policies for each aggregation period and then computes gaps.
func findGapsInAggregationPeriods(aggPeriods []pibit.PeriodWithPUCount, allCoveredForCoverage []pibit.Period) []PeriodsWithGaps {
	var periodsWithGaps []PeriodsWithGaps

	for _, agg := range aggPeriods {
		// For each aggregation period, filter the master list of covered periods down to only those that are relevant.
		var relevantCoveredPeriods []pibit.Period
		for _, covered := range allCoveredForCoverage {
			// Skip if the covered period is completely outside the aggregation period.
			if covered.FromDate.After(agg.Period.ToDate) || covered.ToDate.Before(agg.Period.FromDate) {
				continue
			}

			// Clip the covered period to the bounds of the aggregation period.
			overlapStart := covered.FromDate
			if agg.Period.FromDate.After(overlapStart) {
				overlapStart = agg.Period.FromDate
			}

			overlapEnd := covered.ToDate
			if agg.Period.ToDate.Before(overlapEnd) {
				overlapEnd = agg.Period.ToDate
			}

			if !overlapStart.After(overlapEnd) {
				relevantCoveredPeriods = append(relevantCoveredPeriods, pibit.Period{
					FromDate: overlapStart,
					ToDate:   overlapEnd,
				})
			}
		}

		// Now, compute gaps using only the relevant, pre-filtered, and clipped periods.
		missing := computePeriodGaps(agg.Period, relevantCoveredPeriods)
		periodsWithGaps = append(periodsWithGaps, PeriodsWithGaps{
			AggregationPeriod:  agg.Period,
			MissingSubPeriods:  missing,
			NumberOfPowerUnits: agg.NumberOfPowerUnits,
		})
	}
	return periodsWithGaps
}

// computePeriodGaps takes a single period and a list of RELEVANT covered periods and finds the gaps.
// It assumes the coveredPeriods have already been clipped to the bounds of the entirePeriod.
func computePeriodGaps(entirePeriod pibit.Period, relevantCoveredPeriods []pibit.Period) []pibit.MissingSubPeriod {
	if len(relevantCoveredPeriods) == 0 {
		return []pibit.MissingSubPeriod{{
			Period:              entirePeriod,
			LargerThanThreshold: isPeriodLargerThanThreshold(entirePeriod),
		}}
	}

	merged := mergeCoveredPeriods(relevantCoveredPeriods)

	var gaps []pibit.MissingSubPeriod
	cursor := entirePeriod.FromDate

	for _, covered := range merged {
		// If there's space between the cursor and the start of the current covered period, it's a gap.
		if cursor.Before(covered.FromDate) {
			gapEnd := covered.FromDate.AddDate(0, 0, -1)
			period := pibit.Period{FromDate: cursor, ToDate: gapEnd}

			if !period.FromDate.After(period.ToDate) {
				gaps = append(gaps, pibit.MissingSubPeriod{
					Period:              period,
					LargerThanThreshold: isPeriodLargerThanThreshold(period),
				})
			}
		}

		// Advance the cursor past the current covered period. This is robust against overlapping merged periods.
		nextCursorStart := covered.ToDate.AddDate(0, 0, 1)
		if nextCursorStart.After(cursor) {
			cursor = nextCursorStart
		}
	}

	// Check for a final gap between the last covered period and the end of the entire period.
	if cursor.Before(entirePeriod.ToDate) || cursor.Equal(entirePeriod.ToDate) {
		finalGap := pibit.Period{FromDate: cursor, ToDate: entirePeriod.ToDate}
		if !finalGap.FromDate.After(finalGap.ToDate) {
			gaps = append(gaps, pibit.MissingSubPeriod{
				Period:              finalGap,
				LargerThanThreshold: isPeriodLargerThanThreshold(finalGap),
			})
		}
	}

	return gaps
}

func mergeCoveredPeriods(periods []pibit.Period) []pibit.Period {
	if len(periods) < 2 {
		return periods
	}

	sort.Slice(periods, func(i, j int) bool {
		return periods[i].FromDate.Before(periods[j].FromDate)
	})

	merged := []pibit.Period{periods[0]}

	for _, curr := range periods[1:] {
		last := &merged[len(merged)-1]
		if !curr.FromDate.After(last.ToDate.AddDate(0, 0, 1)) {
			if curr.ToDate.After(last.ToDate) {
				last.ToDate = curr.ToDate
			}
		} else {
			merged = append(merged, curr)
		}
	}

	return merged
}

func isPeriodLargerThanThreshold(period pibit.Period) bool {
	const threshold = 90 * 24 * time.Hour // 90 days
	duration := period.ToDate.Sub(period.FromDate)
	return duration > threshold
}

func buildPolicyKey(p pibit.PolicyData) string {
	return strings.Join([]string{
		toStr(p.PolicyNo),
		toStrTime(p.EffDate),
		toStrTime(p.ExpDate),
	}, "::")
}

func toStr(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}

func toStrTime(t *time.Time) string {
	if t == nil {
		return ""
	}
	return t.Format("2006-01-02")
}
