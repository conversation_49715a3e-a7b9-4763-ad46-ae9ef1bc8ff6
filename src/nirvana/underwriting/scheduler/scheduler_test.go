package scheduler_test

import (
	"context"
	"reflect"
	"testing"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"

	"nirvanatech.com/nirvana/api-server/test_utils"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	commontestutils "nirvanatech.com/nirvana/common-go/test_utils"
	app "nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/graphql-server/queryclient"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/feature_store_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/fmcsa_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	state_enums "nirvanatech.com/nirvana/quoting/app_state_machine/enums"
	state_machine_utils "nirvanatech.com/nirvana/quoting/app_state_machine/test_utils"
	"nirvanatech.com/nirvana/underwriting/scheduler"
)

func TestSchedulerTestSuite(t *testing.T) {
	suite.Run(t, new(schedulerTestSuite))
}

type schedulerTestSuite struct {
	suite.Suite
	h           *test_utils.ApiServerHarness
	a           *state_machine_utils.AppStateMachineHarness
	ctx         context.Context
	app         *fxtest.App
	queryClient *queryclient.QueryClient
}

func (s *schedulerTestSuite) TearDownTest() {
	s.app.RequireStop()
}

func (s *schedulerTestSuite) SetupTest() {
	s.ctx = context.Background()
	var env struct {
		fx.In

		H *test_utils.ApiServerHarness
		A *state_machine_utils.AppStateMachineHarness
		*feature_store_fixture.FeatureStoreFixture
		*fmcsa_fixture.FmcsaFixture
		QueryClient *queryclient.QueryClient
	}
	s.app = testloader.RequireStart(s.T(), &env)
	s.h = env.H
	s.a = env.A
	s.queryClient = env.QueryClient
}

func (s *schedulerTestSuite) TestGetAvailableUW() {
	uw, err := s.h.UnderwritingDeps.UWScheduler.GetFirstUW(s.ctx)
	s.Require().Nil(err)
	s.Require().NotNil(uw)
	s.Require().NotEqual(uw.ID, uuid.Nil)
	s.Require().Greater(len(uw.NirvanaRoles()), 0)
	var isUW bool
	for _, r := range uw.NirvanaRoles() {
		if r.Group == authz.SeniorUnderwriterRole {
			isUW = true
		}
	}
	s.Require().True(isUW)
}

var dotDetailsData = []int64{
	3704084,
	1366091,
	2984251,
	2596750,
	3736241,
	1249715,
	1040054,
	1306391,
	1102540,
	1721404,
	1465979,
	1653617,
}

func (s *schedulerTestSuite) TestGetAssignableUnderwriter() {
	// This test needs to be rewritten since amanda's capacity is now twice of mike hence the assignment order can be
	// random. The current approach of matching application id with uw id in a ordered fashion is not valid anymore.
	s.T().Skip("skip")
	var applications []app.Application
	var uwEmails []string

	wantArray := []string{
		"<EMAIL>",
		"<EMAIL>",
		"<EMAIL>",
		"<EMAIL>",
		"<EMAIL>",
		"<EMAIL>",
		"<EMAIL>",
		"<EMAIL>",
		"<EMAIL>",
		"<EMAIL>",
		"<EMAIL>",
		"<EMAIL>",
		"<EMAIL>",
		"<EMAIL>", // application 1 - og assignment
		"<EMAIL>", // application 1 - og assignment without capacity
		"<EMAIL>",   // application 2 - og assignment
		"<EMAIL>",   // application 2 - og assignment without capacity
		"<EMAIL>", // application 2 - new assignment because of og's deactivation
	}

	var dotIndex int

	// Create 2 users with senior uw role, amanda role is already created by default
	mikeUserID, err := s.h.CreateDefaultHighestAuthorityUWWithName("Michael", "Michael", "<EMAIL>")
	s.Require().NoError(err)
	_, err = s.h.CreateDefaultHighestAuthorityUWWithName("Corina", "Larson", "<EMAIL>")
	s.Require().NoError(err)

	// Create 6 new business (fleet) applications
	for i := 0; i < 6; i++ {
		DOTNum := dotDetailsData[dotIndex]
		dotIndex++
		uw, newApplication := s.createFleetApplicationAndShiftToPolicyCreated(DOTNum, 10)
		uwEmails = append(uwEmails, uw.Email)
		applications = append(applications, *newApplication)
	}

	// Create 2 applications with same DOT
	DOTNum := dotDetailsData[dotIndex]
	dotIndex++

	uw, newApplication := s.createFleetApplicationAndShiftToPolicyCreated(DOTNum, 10)
	uwEmails = append(uwEmails, uw.Email)
	applications = append(applications, *newApplication)

	uw, newApplication = s.createFleetApplicationAndShiftToPolicyCreated(DOTNum, 10)
	uwEmails = append(uwEmails, uw.Email)
	applications = append(applications, *newApplication)

	// Create 4 renewal applications
	for i := 0; i < 4; i++ {
		originalApplicationID := applications[i].ID
		dotNumberForRenewals := applications[i].CompanyInfo.DOTNumber
		uw, newApplication = s.createFleetRenewalApplication(dotNumberForRenewals, originalApplicationID)
		uwEmails = append(uwEmails, uw.Email)
		applications = append(applications, *newApplication)
	}

	// Create a renewal Clearance application
	originalApplicationID := applications[1].ID
	dotNumberForRenewals := applications[1].CompanyInfo.DOTNumber
	uw, newApplication = s.createFleetRenewalApplication(dotNumberForRenewals, originalApplicationID)
	uwEmails = append(uwEmails, uw.Email)
	applications = append(applications, *newApplication)

	uws := s.assignUwsToEndorsements(
		[]string{
			applications[0].ID,
			applications[0].ID,
			applications[1].ID,
			applications[1].ID,
			applications[1].ID, // Post Mike's deactivation.
		},
		mikeUserID,
	)
	uwEmails = appendUWEmails(uws, uwEmails)

	s.Require().Len(uwEmails, len(wantArray))
	for i, email := range uwEmails {
		s.Require().Equal(wantArray[i], email, "idx: %d", i)
	}
}

func (s *schedulerTestSuite) createFleetApplicationAndShiftToPolicyCreated(dotNumber int64, puCount int) (*authz.User, *app.Application) {
	basicAppData := s.a.MockBasicAppData()
	basicAppData.DotNumber = dotNumber
	basicAppData.PUCount = puCount

	applicationId, _, err := s.a.MoveApplicationToGenericState(&s.Suite, state_enums.AppStatePolicyCreated, basicAppData)
	s.Require().NoError(err)

	application, err := s.h.UnderwritingDeps.ApplicationWrapper.GetAppById(s.ctx, applicationId)
	s.Require().NoError(err)

	uw, err := s.h.UnderwritingDeps.UWScheduler.GetAssignableUnderwriter(s.ctx, scheduler.UWAssignmentParams{
		AppID:                 application.ID,
		AgencyID:              application.AgencyID,
		DotNumber:             application.CompanyInfo.DOTNumber,
		EffectiveDate:         application.CoverageInfo.EffectiveDate,
		ProgramType:           enums.ProgramTypeFleet,
		PUCount:               puCount,
		OriginalApplicationID: nil,
	})
	s.Require().NoError(err)
	s.Require().NotNil(uw)

	// Update Underwriter ID
	err = s.h.UnderwritingDeps.ApplicationWrapper.UpdateApp(s.ctx, application.ID, func(app app.Application) (app.Application, error) {
		app.UnderwriterID = uw.ID
		return app, nil
	})
	s.Require().NoError(err)

	return uw, application
}

func (s *schedulerTestSuite) createFleetApplication(dotNumber int64, puCount int) (*authz.User, *app.Application) {
	basicAppData := s.a.MockBasicAppData()
	basicAppData.DotNumber = dotNumber
	basicAppData.PUCount = puCount

	applicationId, err := s.a.CreateApplication(s.ctx, basicAppData)
	s.Require().NoError(err)

	application, err := s.h.UnderwritingDeps.ApplicationWrapper.GetAppById(s.ctx, applicationId)
	s.Require().NoError(err)

	uw, err := s.h.UnderwritingDeps.UWScheduler.GetAssignableUnderwriter(s.ctx, scheduler.UWAssignmentParams{
		AppID:                 application.ID,
		AgencyID:              application.AgencyID,
		DotNumber:             application.CompanyInfo.DOTNumber,
		EffectiveDate:         application.CoverageInfo.EffectiveDate,
		ProgramType:           enums.ProgramTypeFleet,
		PUCount:               puCount,
		OriginalApplicationID: nil,
	})
	s.Require().NoError(err)
	s.Require().NotNil(uw)

	// Update Underwriter ID
	err = s.h.UnderwritingDeps.ApplicationWrapper.UpdateApp(s.ctx, application.ID, func(app app.Application) (app.Application, error) {
		app.UnderwriterID = uw.ID
		return app, nil
	})
	s.Require().NoError(err)

	return uw, application
}

func (s *schedulerTestSuite) createFleetRenewalApplication(dotNumber int64, originalApplicationID string) (*authz.User, *app.Application) {
	basicAppData := s.a.MockBasicAppData()
	basicAppData.DotNumber = dotNumber

	originalApplication, err := s.h.UnderwritingDeps.ApplicationWrapper.GetAppById(s.ctx, originalApplicationID)
	s.Require().NoError(err)

	originalBindableSub, err := s.h.UnderwritingDeps.ApplicationWrapper.GetSubmissionById(s.ctx, *originalApplication.BindableSubmissionID)
	s.Require().NoError(err)

	applicationId, err := s.a.CreateApplication(s.ctx, basicAppData)
	// Update app to update effective date
	err = s.h.UnderwritingDeps.ApplicationWrapper.UpdateApp(s.ctx, applicationId, func(app app.Application) (app.Application, error) {
		app.CoverageInfo.EffectiveDate = originalBindableSub.CoverageInfo.EffectiveDate.AddDate(1, 0, 0)
		return app, nil
	})
	s.Require().NoError(err)

	application, err := s.h.UnderwritingDeps.ApplicationWrapper.GetAppById(s.ctx, applicationId)
	s.Require().NoError(err)

	uw, err := s.h.UnderwritingDeps.UWScheduler.GetAssignableUnderwriter(
		s.ctx,
		scheduler.UWAssignmentParams{
			AppID:                 application.ID,
			AgencyID:              uuid.MustParse(scheduler.TranstarId),
			DotNumber:             application.CompanyInfo.DOTNumber,
			EffectiveDate:         application.CoverageInfo.EffectiveDate,
			ProgramType:           enums.ProgramTypeFleet,
			OriginalApplicationID: &originalApplicationID,
		},
	)
	s.Require().NoError(err)
	s.Require().NotNil(uw)

	// Update Underwriter ID
	err = s.h.UnderwritingDeps.ApplicationWrapper.UpdateApp(
		s.ctx,
		application.ID,
		func(app app.Application) (app.Application, error) {
			app.UnderwriterID = uw.ID
			return app, nil
		},
	)
	s.Require().NoError(err)

	return uw, application
}

func (s *schedulerTestSuite) assignUwsToEndorsements(
	applicationIDs []string, uwToDeactivate uuid.UUID,
) (uws []*authz.User) {
	for i := 0; i < len(applicationIDs)-1; i++ {
		applicationID := applicationIDs[i]
		uws = append(uws, s.assignUWToEndorsement(applicationID, false))
	}

	// Deactivate an uw.
	successful, err := commontestutils.DeactivateUser(s.ctx, commontestutils.Superuser(), s.queryClient, uwToDeactivate)
	s.Require().NoError(err)
	s.Require().True(successful)

	uws = append(uws, s.assignUWToEndorsement(applicationIDs[len(applicationIDs)-1], true))

	return
}

func (s *schedulerTestSuite) assignUWToEndorsement(
	applicationID string, shouldResetCache bool,
) *authz.User {
	uw, err := s.h.UnderwritingDeps.UWScheduler.GetAssignableUnderwriter(
		s.ctx,
		scheduler.UWAssignmentParams{
			ProgramType:           enums.ProgramTypeFleet,
			OriginalApplicationID: &applicationID,
			IsEndorsement:         true,
			ResetCache:            shouldResetCache,
		},
	)
	s.Require().NoError(err)
	s.Require().NotNil(uw)
	return uw
}

func appendUWEmails(uws []*authz.User, uwEmails []string) []string {
	for _, uw := range uws {
		uwEmails = append(uwEmails, uw.Email)
	}
	return uwEmails
}

func (s *schedulerTestSuite) TestMaxPUCountUWAssignment() {
	// 1. Ensure that only selected underwriters are assigned to the application
	// Create 6 users with senior uw role
	mikeUserID, err := s.h.CreateDefaultHighestAuthorityUWWithName("Michael", "Socrates", "<EMAIL>")
	s.Require().NoError(err)
	_, err = s.h.CreateDefaultHighestAuthorityUWWithName("Corina", "Larson", "<EMAIL>")
	s.Require().NoError(err)
	ashUserID, err := s.h.CreateDefaultHighestAuthorityUWWithName("Ash", "Hensel", "<EMAIL>")
	s.Require().NoError(err)
	karleighUserID, err := s.h.CreateDefaultHighestAuthorityUWWithName("Karleigh", "Schroeder", "<EMAIL>")
	s.Require().NoError(err)
	markUserId, err := s.h.CreateDefaultHighestAuthorityUWWithName("Mark", "McDonalds", "<EMAIL>")
	s.Require().NoError(err)
	michaelGivensUserID, err := s.h.CreateDefaultHighestAuthorityUWWithName("Michael", "Givens", "<EMAIL>")
	s.Require().NoError(err)
	xandreaUserId, err := s.h.CreateDefaultHighestAuthorityUWWithName("Xandrea", "Powell", "<EMAIL>")
	s.Require().NoError(err)
	jordanUserId, err := s.h.CreateDefaultHighestAuthorityUWWithName("Jordan", "Cruz", "<EMAIL>")
	s.Require().NoError(err)
	siranUserId, err := s.h.CreateDefaultHighestAuthorityUWWithName("Siran", "Wixom", "<EMAIL>")
	s.Require().NoError(err)
	micheleUserId, err := s.h.CreateDefaultHighestAuthorityUWWithName("Michele", "Smith", "<EMAIL>")
	s.Require().NoError(err)
	ashleyId, err := s.h.CreateDefaultHighestAuthorityUWWithName("Ashley", "Laubscher", "<EMAIL>")
	s.Require().NoError(err)

	josephId, err := s.h.CreateDefaultHighestAuthorityUWWithName("Joseph", "Silvestro", "<EMAIL>")
	s.Require().NoError(err)
	billDahmId, err := s.h.CreateDefaultHighestAuthorityUWWithName("Bill", "Dahm", "<EMAIL>")
	s.Require().NoError(err)
	// taylor's id is created by default so we can't create a fresh one due to unique constraints on email, hence instead
	// we are fetching the user info by email
	taylorUserInfo, err := s.h.ApplicationDeps.AuthWrapper.FetchUserInfoByEmail(s.ctx, "<EMAIL>")
	s.Require().NoError(err)
	amandaUserInfo, err := s.h.ApplicationDeps.AuthWrapper.FetchUserInfoByEmail(s.ctx, "<EMAIL>")
	s.Require().NoError(err)
	amandaID := amandaUserInfo.ID
	taylorID := taylorUserInfo.ID
	// Create test app
	basicAppData := s.a.MockBasicAppData()
	basicAppData.DotNumber = dotDetailsData[4]
	basicAppData.PUCount = 75

	applicationId, err := s.a.CreateApplication(s.ctx, basicAppData)
	s.Require().NoError(err)

	application, err := s.h.UnderwritingDeps.ApplicationWrapper.GetAppById(s.ctx, applicationId)
	s.Require().NoError(err)
	// Test for verifying program type filtering
	uws, err := s.h.UnderwritingDeps.UWScheduler.GetAllAvailableUnderwriters(s.ctx, scheduler.UWAssignmentParams{
		AppID:                 application.ID,
		AgencyID:              application.AgencyID,
		DotNumber:             application.CompanyInfo.DOTNumber,
		EffectiveDate:         application.CoverageInfo.EffectiveDate,
		ProgramType:           enums.ProgramTypeFleet,
		PUCount:               75,
		OriginalApplicationID: nil,
	})
	s.Require().NoError(err)
	for _, uw := range uws {
		s.Require().Contains([]uuid.UUID{
			mikeUserID, amandaID, taylorID, karleighUserID,
			michaelGivensUserID, xandreaUserId, jordanUserId, siranUserId, micheleUserId, ashleyId, markUserId, josephId, billDahmId,
		}, uw.ID)
		s.Require().NotContains([]uuid.UUID{ashUserID}, uw.ID)
	}
	// Create new application with >75 PU
	// Create 6 new business (fleet) applications with >75 PU
	for i := 0; i < 3; i++ {
		DOTNum := dotDetailsData[i]
		uw, _ := s.createFleetApplication(DOTNum, 76)
		// No UWs have PU limit as of now so all UWs could be present (same as <=75 case)
		s.Require().Contains([]uuid.UUID{
			mikeUserID, amandaID, taylorID, michaelGivensUserID, xandreaUserId, karleighUserID,
			jordanUserId, siranUserId, micheleUserId, ashleyId, markUserId, josephId, billDahmId,
		}, uw.ID)
		s.Require().NotContains([]uuid.UUID{ashUserID}, uw.ID)
	}
	// 2. Create applications with <=75 PU
	for i := 0; i < 3; i++ {
		DOTNum := dotDetailsData[i]
		uw, _ := s.createFleetApplication(DOTNum, 75)
		// UW Id should be either mike ,taylor, amanda, michael
		s.Require().Contains([]uuid.UUID{
			mikeUserID, amandaID, taylorID, michaelGivensUserID, xandreaUserId, karleighUserID,
			jordanUserId, siranUserId, micheleUserId, ashleyId, markUserId, josephId, billDahmId,
		}, uw.ID)
		s.Require().NotContains([]uuid.UUID{ashUserID}, uw.ID)
	}
}

func getAssignmentParams(
	application *app.Application,
	agencyId string,
	programType enums.ProgramType,
	puCount int,
	originalAppId *string,
) scheduler.UWAssignmentParams {
	return scheduler.UWAssignmentParams{
		AppID:                 application.ID,
		AgencyID:              uuid.MustParse(agencyId),
		DotNumber:             application.CompanyInfo.DOTNumber,
		EffectiveDate:         application.CoverageInfo.EffectiveDate,
		ProgramType:           programType,
		PUCount:               puCount,
		OriginalApplicationID: originalAppId,
	}
}

func (s *schedulerTestSuite) TestGetAssignableUnderwriterV2() {
	_, karleighError := s.h.CreateDefaultHighestAuthorityUWWithName(
		"Karleigh",
		"Schroeder",
		scheduler.KarleighSchroederEmail,
	)
	s.Require().NoError(karleighError)
	_, socratesError := s.h.CreateDefaultHighestAuthorityUWWithName(
		"Michael",
		"Socrates",
		scheduler.MichaelSocratesEmail,
	)
	s.Require().NoError(socratesError)

	joniEmail := "<EMAIL>"
	_, joniError := s.h.CreateDefaultHighestAuthorityUWWithName(
		"Joni",
		"Linhart",
		joniEmail,
	)
	s.Require().NoError(joniError)
	barbaraEmail := "<EMAIL>"
	_, barbaraError := s.h.CreateDefaultHighestAuthorityUWWithName(
		"Barbara",
		"Bateman",
		barbaraEmail,
	)
	s.Require().NoError(barbaraError)
	micheleEmail := "<EMAIL>"
	_, micheleError := s.h.CreateDefaultHighestAuthorityUWWithName(
		"Michele",
		"Smith",
		micheleEmail,
	)
	s.Require().NoError(micheleError)
	jordanEmail := "<EMAIL>"
	_, jordanError := s.h.CreateDefaultHighestAuthorityUWWithName(
		"Jordan",
		"Cruz",
		jordanEmail,
	)
	s.Require().NoError(jordanError)
	siranEmail := "<EMAIL>"
	_, siranError := s.h.CreateDefaultHighestAuthorityUWWithName(
		"Siran",
		"Wixom",
		siranEmail,
	)
	s.Require().NoError(siranError)
	xandreaEmail := scheduler.XandreaPowellEmail
	_, xandreaError := s.h.CreateDefaultHighestAuthorityUWWithName(
		"Xandrea",
		"Powell",
		xandreaEmail,
	)
	s.Require().NoError(xandreaError)

	josephEmail := scheduler.JosephSilvestroEmail
	josephUser, josephError := s.h.CreateDefaultHighestAuthorityUWWithName(
		"Joseph",
		"Silvestro",
		josephEmail,
	)
	s.Require().NoError(josephError)

	// deactivate josephUser
	successful, err := commontestutils.DeactivateUser(
		s.ctx,
		commontestutils.Superuser(),
		s.queryClient,
		josephUser,
	)
	s.Require().NoError(err)
	s.Require().True(successful)

	// invalidate cache for deactivated user to reflect
	impl := s.h.UnderwritingDeps.UWScheduler.(*scheduler.Impl)
	impl.ResetUnderwritersCacheEntry()

	basicAppData := s.a.MockBasicAppData()
	basicAppData.DotNumber = dotDetailsData[4]
	basicAppData.PUCount = 75

	// Create test app with 75 PUs
	applicationId, err := s.a.CreateApplication(s.ctx, basicAppData)
	s.Require().NoError(err)
	application, err := s.h.UnderwritingDeps.ApplicationWrapper.GetAppById(s.ctx, applicationId)
	s.Require().NoError(err)

	ctx := context.Background()
	type args struct {
		ctx           context.Context
		agencyId      string
		programType   enums.ProgramType
		puCount       int
		originalAppId *string
	}
	tests := []struct {
		name               string
		args               args
		expectedUserEmails []string
		wantErr            error
	}{
		{
			name: "Test assignment with preferred underwriter for fleet with PU Count under uw limit",
			args: args{
				ctx:         ctx,
				agencyId:    scheduler.RiskPlacementServicesId,
				programType: enums.ProgramTypeFleet,
				puCount:     75,
			},
			expectedUserEmails: []string{
				scheduler.JordanCruzEmail,
			},
			wantErr: nil,
		},
		{
			name: "Test assignment with preferred underwriter with PU Count over uw limit",
			args: args{
				ctx:         ctx,
				agencyId:    scheduler.RiskPlacementServicesId,
				programType: enums.ProgramTypeFleet,
				puCount:     85,
			},
			expectedUserEmails: []string{
				scheduler.JordanCruzEmail,
			},
			wantErr: nil,
		},
		{
			name: "Test assignment with preferred underwriter but program type unaligned",
			args: args{
				ctx:         ctx,
				agencyId:    scheduler.CottinghamAndButlerId,
				programType: enums.ProgramTypeNonFleetAdmitted,
				puCount:     5,
			},
			// barbara is our only non fleet underwriter (for testing) and therefore, should
			// be assigned for non fleet applications even though the agency has preferred underwriter
			expectedUserEmails: []string{
				barbaraEmail,
			},
			wantErr: nil,
		},
		{
			name: "Test assignment with preferred underwriter deactivated",
			args: args{
				ctx:         ctx,
				agencyId:    scheduler.HubId,
				programType: enums.ProgramTypeFleet,
				puCount:     75,
			},
			expectedUserEmails: []string{},
			wantErr:            errors.Newf("user for preferred underwriter %s is deactivated", josephEmail),
		},
		{
			name: "Test assignment for tier 1 agency for renewal",
			args: args{
				ctx:           ctx,
				agencyId:      scheduler.TranstarId,
				programType:   enums.ProgramTypeFleet,
				puCount:       75,
				originalAppId: pointer_utils.ToPointer(uuid.New().String()),
			},
			expectedUserEmails: []string{
				scheduler.JoniLinhartEmail,
			},
			wantErr: nil,
		},
		{
			name: "Test assignment for tier 1 agency for renewal 2",
			args: args{
				ctx:           ctx,
				agencyId:      scheduler.RiskPlacementServicesId,
				programType:   enums.ProgramTypeFleet,
				puCount:       75,
				originalAppId: pointer_utils.ToPointer(uuid.New().String()),
			},
			expectedUserEmails: []string{
				scheduler.KarleighSchroederEmail,
			},
			wantErr: nil,
		},
		{
			name: "Test assignment for non tier 1 agency for renewal",
			args: args{
				ctx:           ctx,
				agencyId:      uuid.New().String(),
				programType:   enums.ProgramTypeFleet,
				puCount:       75,
				originalAppId: pointer_utils.ToPointer(uuid.New().String()),
			},
			expectedUserEmails: []string{
				scheduler.KarleighSchroederEmail,
			},
			wantErr: nil,
		},
		{
			name: "Test assignment for tier 11 agency for new business",
			args: args{
				ctx:         ctx,
				agencyId:    scheduler.AleraId,
				programType: enums.ProgramTypeFleet,
				puCount:     75,
			},
			expectedUserEmails: []string{
				scheduler.XandreaPowellEmail,
			},
			wantErr: nil,
		},
		{
			name: "Test assignment for tier 11 agency for new business 2",
			args: args{
				ctx:         ctx,
				agencyId:    scheduler.HaylorId,
				programType: enums.ProgramTypeFleet,
				puCount:     75,
			},
			expectedUserEmails: []string{
				scheduler.SiranWixomEmail,
			},
			wantErr: nil,
		},
		{
			name: "Test assignment for tier 11 agency for new business 3",
			args: args{
				ctx:         ctx,
				agencyId:    scheduler.TriumphId,
				programType: enums.ProgramTypeFleet,
				puCount:     75,
			},
			expectedUserEmails: []string{
				scheduler.XandreaPowellEmail,
			},
			wantErr: nil,
		},
		{
			name: "Test assignment for tier 11 agency for renewal",
			args: args{
				ctx:           ctx,
				agencyId:      scheduler.AleraId,
				programType:   enums.ProgramTypeFleet,
				puCount:       75,
				originalAppId: pointer_utils.ToPointer(uuid.New().String()),
			},
			expectedUserEmails: []string{
				scheduler.KarleighSchroederEmail,
			},
			wantErr: nil,
		},
		{
			name: "Test assignment for tier 11 agency for renewal 2",
			args: args{
				ctx:           ctx,
				agencyId:      scheduler.HaylorId,
				programType:   enums.ProgramTypeFleet,
				puCount:       75,
				originalAppId: pointer_utils.ToPointer(uuid.New().String()),
			},
			expectedUserEmails: []string{
				scheduler.KarleighSchroederEmail,
			},
			wantErr: nil,
		},
		{
			name: "Test assignment for tier 2 agency preferred uw",
			args: args{
				ctx:         ctx,
				agencyId:    scheduler.BlueRidgeSpecialtyId,
				programType: enums.ProgramTypeFleet,
				puCount:     75,
			},
			expectedUserEmails: []string{
				scheduler.SiranWixomEmail,
			},
			wantErr: nil,
		},
		{
			name: "Test assignment for tier 2 agency for renewal",
			args: args{
				ctx:           ctx,
				agencyId:      scheduler.BlueRidgeSpecialtyId,
				programType:   enums.ProgramTypeFleet,
				puCount:       75,
				originalAppId: pointer_utils.ToPointer(uuid.New().String()),
			},
			expectedUserEmails: []string{
				scheduler.KarleighSchroederEmail,
			},
			wantErr: nil,
		},
		{
			name: "Test assignment for tier 3 agency preferred uw",
			args: args{
				ctx:         ctx,
				agencyId:    scheduler.LouieParkerSmithId,
				programType: enums.ProgramTypeFleet,
				puCount:     75,
			},
			expectedUserEmails: []string{
				scheduler.XandreaPowellEmail,
			},
			wantErr: nil,
		},
		{
			name: "Test assignment for tier 3 agency for renewal",
			args: args{
				ctx:           ctx,
				agencyId:      scheduler.LouieParkerSmithId,
				programType:   enums.ProgramTypeFleet,
				puCount:       75,
				originalAppId: pointer_utils.ToPointer(uuid.New().String()),
			},
			expectedUserEmails: []string{
				scheduler.KarleighSchroederEmail,
			},
			wantErr: nil,
		},
	}

	for _, tt := range tests {
		s.T().Run(tt.name, func(t *testing.T) {
			gotUser, err := s.h.UnderwritingDeps.UWScheduler.GetAssignableUnderwriter(
				ctx,
				getAssignmentParams(application, tt.args.agencyId, tt.args.programType, tt.args.puCount, tt.args.originalAppId),
			)
			if !errors.Is(err, tt.wantErr) {
				t.Errorf("GetAllAvailableUnderwriters error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			expectedUserEmailSet := map[string]bool{}
			for _, expectedUserEmail := range tt.expectedUserEmails {
				expectedUserEmailSet[expectedUserEmail] = true
			}
			if gotUser == nil {
				if len(expectedUserEmailSet) > 0 {
					t.Errorf("got null but expected = %v", expectedUserEmailSet)
				}
			} else {
				_, gotAsExpected := expectedUserEmailSet[gotUser.Email]
				if !gotAsExpected {
					t.Errorf("() = %+v, want %+v", gotUser, expectedUserEmailSet)
				}
			}
		})
	}
}

func (s *schedulerTestSuite) TestGetAllAvailableUnderwriters() {
	_, karleighError := s.h.CreateDefaultHighestAuthorityUWWithName(
		"Karleigh",
		"Schroeder",
		scheduler.KarleighSchroederEmail,
	)
	s.Require().NoError(karleighError)
	_, socratesError := s.h.CreateDefaultHighestAuthorityUWWithName(
		"Michael",
		"Socrates",
		scheduler.MichaelSocratesEmail,
	)
	s.Require().NoError(socratesError)
	joniEmail := "<EMAIL>"
	joniId, joniError := s.h.CreateDefaultHighestAuthorityUWWithName(
		"Joni",
		"Linhart",
		joniEmail,
	)
	s.Require().NoError(joniError)
	barbaraEmail := "<EMAIL>"
	_, barbaraError := s.h.CreateDefaultHighestAuthorityUWWithName(
		"Barbara",
		"Bateman",
		barbaraEmail,
	)
	s.Require().NoError(barbaraError)
	// Create Bill Dahm for non-fleet testing
	billDahmEmail := "<EMAIL>"
	_, billDahmError := s.h.CreateDefaultHighestAuthorityUWWithName(
		"Bill",
		"Dahm",
		billDahmEmail,
	)
	s.Require().NoError(billDahmError)
	abbiEmail := "<EMAIL>"
	_, abbiError := s.h.CreateDefaultHighestAuthorityUWWithName(
		"Abbi",
		"Porter",
		abbiEmail,
	)
	s.Require().NoError(abbiError)
	amandaUserInfo, err := s.h.ApplicationDeps.AuthWrapper.FetchUserInfoByEmail(s.ctx, "<EMAIL>")
	s.Require().NoError(err)
	amandaEmail := amandaUserInfo.Email

	// deactivate joni
	successful, err := commontestutils.DeactivateUser(
		s.ctx,
		commontestutils.Superuser(),
		s.queryClient,
		joniId,
	)
	s.Require().NoError(err)
	s.Require().True(successful)

	// invalidate cache for deactivated user to reflect
	impl := s.h.UnderwritingDeps.UWScheduler.(*scheduler.Impl)
	impl.ResetUnderwritersCacheEntry()

	basicAppData := s.a.MockBasicAppData()
	basicAppData.DotNumber = dotDetailsData[4]
	basicAppData.PUCount = 75

	// Create test app with 75 PUs
	applicationId, err := s.a.CreateApplication(s.ctx, basicAppData)
	s.Require().NoError(err)
	application, err := s.h.UnderwritingDeps.ApplicationWrapper.GetAppById(s.ctx, applicationId)
	s.Require().NoError(err)

	ctx := context.Background()
	type args struct {
		ctx         context.Context
		agencyId    string
		programType enums.ProgramType
		puCount     int
	}
	tests := []struct {
		name               string
		args               args
		expectedUserEmails []string
		wantErr            error
	}{
		{
			name: "Test available underwriters for program = fleet and pu count below everyone's threshold",
			args: args{
				ctx:         ctx,
				agencyId:    application.AgencyID.String(),
				programType: enums.ProgramTypeFleet,
				puCount:     75,
			},
			expectedUserEmails: []string{
				scheduler.KarleighSchroederEmail,
				amandaEmail,
				scheduler.MichaelSocratesEmail,
				scheduler.AbbiPorterEmail,
			},
			wantErr: nil,
		},
		{
			name: "Test get underwriters with preferred underwriter but puCount > limit for certain underwriters",
			args: args{
				ctx:         ctx,
				agencyId:    scheduler.RiskPlacementServicesId,
				programType: enums.ProgramTypeFleet,
				puCount:     80,
			},
			// Underwriters with lower maxPuCount do not appear. Currently no such underwriters exist so all underwriters appear
			expectedUserEmails: []string{
				scheduler.KarleighSchroederEmail,
				amandaEmail,
				scheduler.MichaelSocratesEmail,
				scheduler.AbbiPorterEmail,
			},
			wantErr: nil,
		},
		{
			name: "Test get underwriters with program type non fleet",
			args: args{
				ctx:         ctx,
				agencyId:    scheduler.UsiId,
				programType: enums.ProgramTypeNonFleetAdmitted,
				puCount:     75,
			},
			// program filter is always applied. Since barbara and bill.dahm are the only non fleet underwriters available
			// and the preferred underwriter does not serve non fleet, we ignore preferred underwriter
			expectedUserEmails: []string{
				barbaraEmail,
				"<EMAIL>",
			},
			wantErr: nil,
		},
	}

	for _, tt := range tests {
		s.T().Run(tt.name, func(t *testing.T) {
			got, err := s.h.UnderwritingDeps.UWScheduler.GetAllAvailableUnderwriters(
				ctx,
				getAssignmentParams(application, tt.args.agencyId, tt.args.programType, tt.args.puCount, nil),
			)
			if !errors.Is(err, tt.wantErr) {
				t.Errorf("GetAllAvailableUnderwriters error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			gotUserEmailSet := map[string]bool{}
			for _, user := range got {
				gotUserEmailSet[user.Email] = true
			}
			expectedUserEmailSet := map[string]bool{}
			for _, expectedUserEmail := range tt.expectedUserEmails {
				expectedUserEmailSet[expectedUserEmail] = true
			}
			if !reflect.DeepEqual(gotUserEmailSet, expectedUserEmailSet) {
				t.Errorf("() = %+v, want %+v", gotUserEmailSet, expectedUserEmailSet)
			}
		})
	}
}
