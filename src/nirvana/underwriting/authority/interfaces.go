package authority

import (
	"context"

	"github.com/google/uuid"
	authorities_wrapper "nirvanatech.com/nirvana/db-api/db_wrappers/authorities"
	"nirvanatech.com/nirvana/db-api/db_wrappers/authorities/enums"
	authority_enums "nirvanatech.com/nirvana/underwriting/authority/enums"
)

// RequestPayload is a marker interface for all request payload types
// This ensures type safety while allowing different concrete payload types
type RequestPayload interface {
	// GetRequestType returns the request type this payload is for
	GetRequestType() enums.RequestType
}

// RequestHandler defines the interface for handling different request types
// Following the non-generic pattern for simplicity and maintainability
type RequestHandler interface {
	// ValidatePayload validates the request data and returns the parsed payload
	// The data parameter should be the concrete payload struct for the request type
	ValidatePayload(data interface{}) (interface{}, error)

	// Execute performs the approved request action
	Execute(ctx context.Context, request *authorities_wrapper.AuthorityRequest) error

	// GetType returns the request type this handler manages
	GetType() enums.RequestType

	// CanCreate checks if the user can create this type of request
	// The user is extracted from the context using authz.UserFromContext
	// Returns (true, nil) if user CAN create, (false, nil) if user CANNOT create,
	// or (false, error) if an error occurred while checking permissions
	CanCreate(ctx context.Context, applicationReviewID uuid.UUID) (bool, error)

	// CanReview checks if the user can review this type of request
	// The user is extracted from the context using authz.UserFromContext
	// Returns (true, nil) if user CAN review, (false, nil) if user CANNOT review,
	// or (false, error) if an error occurred while checking permissions
	CanReview(ctx context.Context) (bool, error)
}

// Manager defines the interface for the authorities request manager
type Manager interface {
	// CreateRequest creates a new authority request
	// The requester is extracted from the context using authz.UserFromContext
	// The requestData must implement RequestPayload interface for type safety
	CreateRequest(
		ctx context.Context,
		applicationReviewID uuid.UUID,
		requestData RequestPayload,
	) (*uuid.UUID, error)

	// ProcessRequest reviews and executes a request in a single atomic operation
	// This combines the review and execution steps for better consistency
	// - For approved requests: executes the action and marks as executed
	//   If execution fails, the entire operation is rolled back and request remains pending
	// - For rejected requests: marks as rejected without execution
	// The reviewer is extracted from the context using authz.UserFromContext
	ProcessRequest(
		ctx context.Context,
		requestID uuid.UUID,
		action authority_enums.ReviewAction,
		notes string,
	) error

	// GetRequestsForApplication retrieves all requests for an application
	GetRequestsForApplication(
		ctx context.Context,
		applicationReviewID uuid.UUID,
	) ([]*authorities_wrapper.AuthorityRequest, error)

	// GetLatestRequestForApplication retrieves the most recent request for an application
	GetLatestRequestForApplication(
		ctx context.Context,
		applicationReviewID uuid.UUID,
	) (*authorities_wrapper.AuthorityRequest, error)

	// GetRequest retrieves a specific request by ID
	GetRequest(ctx context.Context, requestID uuid.UUID) (*authorities_wrapper.AuthorityRequest, error)

	// GetRequestsByState retrieves requests by state
	GetRequestsByState(ctx context.Context, state enums.RequestState) ([]*authorities_wrapper.AuthorityRequest, error)

	// RegisterHandler registers a handler for a specific request type
	RegisterHandler(reqType enums.RequestType, handler RequestHandler)
}
