openapi: 3.0.0
info:
  version: 1.0.0
  title: Nirvana Underwriting API
  description: Nirvana Underwriting APIs

servers:
  - url: https://api.prod.nirvanatech.com/underwriting

tags:
  - name: 'underwriting'
    description: 'Underwriting related APIs'

security:
  - sessionIdAuth: []

paths:
  /v2/underwriting/application_review/list:
    get:
      tags:
        - 'underwriting'
      description: Get all application reviews with pagination
      parameters:
        - in: query
          name: size
          schema:
            type: integer
        - in: query
          name: cursor
          schema:
            type: string
        - in: query
          name: q
          schema:
            type: string
        - in: query
          name: effectiveDateBefore
          schema:
            type: string
            format: date
        - in: query
          name: effectiveDateAfter
          schema:
            type: string
            format: date
        - in: query
          name: underWriterID
          schema:
            type: string
        - in: query
          name: tab
          schema:
            $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewTab'
        - in: query
          name: recommendedActions
          schema:
            type: array
            items:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/RecommendedAction'
        - in: query
          name: sortBy
          schema:
            $ref: '../../components/underwriting/spec.yaml#/components/schemas/PaginatedListSortBy'
        - in: query
          name: sortDirection
          schema:
            $ref: '../../components/underwriting/spec.yaml#/components/schemas/PaginatedListSortDirection'

      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewListResponseV2'
        '422':
          description: Unable to load Applications Reviews
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'

  /underwriting/application_review/count:
    get:
      tags:
        - 'underwriting'
      description: Get all application reviews counts
      parameters:
        - in: query
          name: q
          schema:
            type: string
        - in: query
          name: effectiveDateBefore
          schema:
            type: string
            format: date
        - in: query
          name: effectiveDateAfter
          schema:
            type: string
            format: date
        - in: query
          name: underWriterID
          schema:
            type: string
        - in: query
          name: recommendedActions
          schema:
            type: array
            items:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/RecommendedAction'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewListCountResponse'
        '422':
          description: Unable to load Applications Reviews
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'


  /underwriting/application_review/data_completion/list:
    get:
      tags:
        - 'underwriting'
      description: Get all application reviews for data completion view with pagination
      parameters:
        - in: query
          name: size
          schema:
            type: integer
        - in: query
          name: cursor
          schema:
            type: string
        - in: query
          name: q
          schema:
            type: string
        - in: query
          name: tab
          schema:
            $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewDataCompletionTab'
      responses:
        '200':
          description: Get successful
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewListForDataCompletionResponse'
        '422':
          description: Unable to load Applications Reviews
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'

  /underwriting/underwriters:
    get:
      operationId: GetUnderwriters
      tags:
        - 'underwriting'
      description: Get all underwriters
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                type: object
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/Underwriters'
        '422':
          description: Unable to load Application Reviews
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}:
    get:
      operationId: GetApplicationReviewById
      tags:
        - 'underwriting'
      description: Return application review's information
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewDetail'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        '422':
          description: Unable to load Application Review
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReview
      tags:
        - 'underwriting'
      description: Update the current information of the application review
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/UpdateApplicationReviewRequest'
      responses:
        '200':
          description: Updated successfully
        '422':
          description: Unprocessable data
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/permissions:
    get:
      operationId: GetApplicationReviewPermissions
      tags:
        - 'underwriting'
      description: Return application review's UW permissions
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewUserPermissions'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'

  /underwriting/application_reviews/{applicationReviewID}/assignees:
    get:
      operationId: GetApplicationReviewAssignees
      tags:
        - 'underwriting'
      description: Return application review's assignees
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewAssignees'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewAssignees
      tags:
        - 'underwriting'
      description: Update the application review's assignees
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewAssigneesForm'
      responses:
        '200':
          description: Assigned successfully
        '422':
          description: Unprocessable data
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/summary:
    get:
      operationId: GetApplicationReviewWidgetSummary
      tags:
        - 'underwriting'
      description: Return application review's summary widget data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewWidgetSummary'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewSummary
      tags:
        - 'underwriting'
      description: Update application review's summary data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewSummaryForm'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/account_grade:
    get:
      operationId: GetApplicationReviewAccountGrade
      tags:
        - 'underwriting'
      description: Return application review's account grade
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewAccountGrade'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewAccountGrade
      tags:
        - 'underwriting'
      description: Update application review's account grade
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewAccountGrade'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/notes:
    get:
      operationId: GetApplicationReviewNotes
      tags:
        - 'underwriting'
      description: Return application review's notes
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewNotes'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewNotes
      tags:
        - 'underwriting'
      description: Update application review's notes
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewNotes'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/documents:
    get:
      operationId: GetApplicationReviewDocuments
      tags:
        - 'underwriting'
      description: Return application review's documents
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewDocuments'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    post:
      operationId: PostApplicationReviewDocuments
      tags:
        - 'underwriting'
      description: Upload documents for an application_review
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '../../components/common/spec.yaml#/components/schemas/UploadFileRequest'
      responses:
        '201':
          description: Upload successful
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/FileHandle'
        default:
          description: Unprocessable Document
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'

  /underwriting/application_reviews/{applicationReviewID}/documents/{fileHandle}/link:
    get:
      operationId: GetApplicationReviewDocumentLink
      tags:
        - 'underwriting'
      description: Downloads a document for application review
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
        - in: path
          name: fileHandle
          required: true
          schema:
            type: string
            example: d754a727-80fd-468d-b2d0-8c8659eac8f3
      responses:
        '200':
          description: Download successful
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/DownloadFileLinkResponse'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/operations/years_in_business:
    get:
      operationId: GetApplicationReviewOperationsYearsInBusiness
      tags:
        - 'underwriting'
      description: Return application review's years in business (operations) widget data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewOperationsYearsInBusiness'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewOperationsYearsInBusiness
      tags:
        - 'underwriting'
      description: Update application review's years in business information
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewOperationsYearsInBusinessForm'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/operations/projected_information:
    get:
      operationId: GetApplicationReviewOperationsProjectedInformation
      tags:
        - 'underwriting'
      description: Return application review's projected information (operations) widget data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewOperationsProjectedInformation'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewOperationsProjectedInformation
      tags:
        - 'underwriting'
      description: Update application review's projected information
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewOperationsProjectedInformationForm'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/operations/garaging_location:
    get:
      operationId: GetApplicationReviewOperationsGaragingLocation
      tags:
        - 'underwriting'
      description: Return application review's garaging location (operations) widget data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewOperationsGaragingLocation'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewOperationsGaragingLocation
      tags:
        - 'underwriting'
      description: Update application review's garaging location
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewOperationsGaragingLocationForm'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/operations/terminal_locations:
    get:
      operationId: GetApplicationReviewOperationsTerminalLocations
      tags:
        - 'underwriting'
      description: Return application review's terminal locations (operations) widget data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewOperationsTerminalLocations'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: PatchApplicationReviewOperationsTerminalLocations
      tags:
        - 'underwriting'
      description: Update application review's terminal locations (operations) widget data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/UpdateApplicationReviewOperationsTerminalLocations'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewOperationsTerminalLocations'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'

  /underwriting/application_reviews/{applicationReviewID}/operations/terminal_locations/select:
    post:
      operationId: PostApplicationReviewOperationsTerminalLocationSelect
      tags:
        - 'underwriting'
      description: Posts application review's chosen terminal location
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/SelectApplicationReviewOperationsTerminalLocationForm'
      responses:
        '200':
          description: Post successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/operations/commodities:
    get:
      operationId: GetApplicationReviewOperationsCommodities
      tags:
        - 'underwriting'
      description: Return application review's commodities (operations) widget data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewOperationsCommodities'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewOperationsCommodities
      tags:
        - 'underwriting'
      description: Update application review's commodities
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewOperationsCommoditiesForm'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/operations/commodities/supported_operations:
    get:
      operationId: GetApplicationReviewOperationsCommoditiesSupportedOperations
      tags:
        - 'underwriting'
      description: Return application review's supported operations for commodities (operations) widget data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewOperationsCommoditiesSupportedOperations'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/operations/fleet_history:
    get:
      operationId: GetApplicationReviewOperationsFleetHistory
      tags:
        - 'underwriting'
      description: Return application review's fleet history (operations) widget data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewOperationsFleetHistory'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewOperationsFleetHistory
      tags:
        - 'underwriting'
      description: Update application review's fleet history
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewOperationsFleetHistoryForm'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/operations/radius_of_operation:
    get:
      operationId: GetApplicationReviewOperationsRadiusOfOperation
      tags:
        - 'underwriting'
      description: Return application review's radius of operation (operations) widget data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewOperationsRadiusOfOperation'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewOperationsRadiusOfOperation
      tags:
        - 'underwriting'
      description: Update application review's radius of operation
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewOperationsRadiusOfOperationForm'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/operations/vehicle_zones:
    get:
      operationId: GetApplicationReviewOperationsVehicleZones
      tags:
        - 'underwriting'
      description: Return vehicle start & end zones for (operations) widget data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewOperationsVehicleZones'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewOperationsVehicleZones
      tags:
        - 'underwriting'
      description: Update application review's vehicle start & end zones
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewOperationsVehicleZonesForm'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/operations/operating_classes:
    get:
      operationId: GetApplicationReviewOperationsOperatingClasses
      tags:
        - 'underwriting'
      description: Return application review's operating classes (operations) widget data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewOperationsOperatingClasses'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewOperationsOperatingClasses
      tags:
        - 'underwriting'
      description: Update application review's operating classes
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewOperationsOperatingClassesForm'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/operations/customers:
    get:
      operationId: GetApplicationReviewOperationsCustomers
      tags:
        - 'underwriting'
      description: Return application review's customers (operations) widget data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewOperationsCustomers'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/operations/hazard_zones:
    get:
      operationId: GetApplicationReviewOperationsHazardZones
      tags:
        - 'underwriting'
      description: Return application review's hazard zones (operations) widget data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewOperationsHazardZones'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewOperationsHazardZones
      tags:
        - 'underwriting'
      description: Update application review's hazard zones
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewOperationsHazardZonesForm'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/equipment:
    post:
      operationId: AddEquipmentUnitToAppReview
      tags:
        - 'underwriting'
      description: Add a new equipment unit to the app review
      parameters:
        - $ref: "../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/AppReviewEquipmentAddRequest'
      responses:
        '201':
          description: Equipment unit created successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/equipments/units:
    get:
      operationId: GetApplicationReviewEquipmentsUnits
      tags:
        - 'underwriting'
      description: Return application review's units (equipments) widget data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewEquipmentsUnits'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewEquipmentsUnits
      tags:
        - 'underwriting'
      description: Update application review's units
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewEquipmentsUnitsForm'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/equipments/owner_operators:
    get:
      operationId: GetApplicationReviewEquipmentsOwnerOperators
      tags:
        - 'underwriting'
      description: Return application review's units (equipments) widget data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewEquipmentsOwnerOperators'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/equipments/additional_info_units:
    get:
      operationId: GetApplicationReviewEquipmentsAdditionalInfoUnits
      tags:
        - 'underwriting'
      description: Return application review's additional units (equipments) widget data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewEquipmentsAdditionalInfoUnits'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/equipments/safety_usage:
    get:
      operationId: GetApplicationReviewEquipmentsSafetyUsage
      tags:
        - 'underwriting'
      description: Return application review's safety usage (equipments) widget data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewEquipmentsSafetyUsage'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/drivers/list:
    get:
      operationId: GetApplicationReviewDriversList
      tags:
        - 'underwriting'
      description: Return application review's list (drivers) widget data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewDriversList'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: "../../components/common/spec.yaml#/components/schemas/ErrorMessage"
    put:
      operationId: UpdateApplicationReviewDriversList
      tags:
        - 'underwriting'
      description: Update application review's drivers list
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewDriversListForm'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'

  /underwriting/application_reviews/{applicationReviewID}/driver:
    patch:
      operationId: UpdateApplicationReviewDriver
      tags:
        - 'underwriting'
      description: Update application review's driver
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewDriverUpdateRequest'
      responses:
        '200':
          description: Driver updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    post:
      tags:
        - 'underwriting'
      operationId: CreateApplicationReviewDriver
      description: Create new driver in application review
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewDriverCreateRequest'
      responses:
        '201':
          description: Driver created successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/safety/safety_score/v2:
    get:
      operationId: GetApplicationReviewSafetyScoreV2
      tags:
        - 'underwriting'
      description: Return application review's safety score (safety) widget data (V2)
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewSafetyScoreV2'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewSafetyScoreV2
      tags:
        - 'underwriting'
      description: Update application review's safety score
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewSafetyScoreFormV2'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  # todo deprecate this once we move to the /v2 endpoint
  /underwriting/application_reviews/{applicationReviewID}/safety/safety_score:
    get:
      operationId: GetApplicationReviewSafetyScore
      tags:
        - 'underwriting'
      description: Return application review's safety score (safety) widget data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewSafetyScore'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewSafetyScore
      tags:
        - 'underwriting'
      description: Update application review's safety score
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewSafetyScoreForm'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/safety/basic_score_threshold:
    get:
      operationId: GetApplicationReviewSafetyBasicScoreThreshold
      tags:
        - 'underwriting'
      description: Return application review's basic score threshold (safety) widget data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewSafetyBasicScoreThreshold'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewSafetyBasicScoreThreshold
      tags:
        - 'underwriting'
      description: Update application review's basic score threshold
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewSafetyBasicScoreThresholdForm'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/safety/basic_score_trend:
    get:
      operationId: GetApplicationReviewSafetyBasicScoreTrend
      tags:
        - 'underwriting'
      description: Return application review's basic score trend (safety) widget data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewSafetyBasicScoreTrend'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewSafetyBasicScoreTrend
      tags:
        - 'underwriting'
      description: Update application review's basic score trend
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewSafetyBasicScoreTrendForm'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/safety/iss_score_trend:
    get:
      operationId: GetApplicationReviewSafetyISSScoreTrend
      tags:
        - 'underwriting'
      description: Return application review's iss score trend (safety) widget data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewSafetyISSScoreTrend'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewSafetyISSScoreTrend
      tags:
        - 'underwriting'
      description: Update application review's iss score trend
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewSafetyISSScoreTrendForm'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/safety/oos_violations:
    get:
      operationId: GetApplicationReviewSafetyOOSViolations
      tags:
        - 'underwriting'
      description: Return application review's OOS violations (safety) widget data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewSafetyOOSViolations'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewSafetyOOSViolations
      tags:
        - 'underwriting'
      description: Update application review's OOS violations
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewSafetyOOSViolationsForm'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/safety/severe_violations:
    get:
      operationId: GetApplicationReviewSafetySevereViolations
      tags:
        - 'underwriting'
      description: Return application review's severe violations (safety) widget data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewSafetySevereViolations'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewSafetySevereViolations
      tags:
        - 'underwriting'
      description: Update application review's  severe violations
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewSafetySevereViolationsForm'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/safety/dot_rating:
    get:
      operationId: GetApplicationReviewSafetyDotRating
      tags:
        - 'underwriting'
      description: Return application review's DOT rating (safety) widget data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewSafetyDotRating'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewSafetyDotRating
      tags:
        - 'underwriting'
      description: Update application review's DOT Rating
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewSafetyDotRatingForm'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/safety/crash_record:
    get:
      operationId: GetApplicationReviewSafetyCrashRecord
      tags:
        - 'underwriting'
      description: Return application review's crash record (safety) widget data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewSafetyCrashRecord'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewSafetyCrashRecord
      tags:
        - 'underwriting'
      description: Update application review's crash record
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewSafetyCrashRecordForm'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/financials/data:
    get:
      operationId: GetApplicationReviewFinancialsData
      tags:
        - 'underwriting'
      description: Return application review's data (financials) widget data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewFinancialsData'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewFinancialsData
      tags:
        - 'underwriting'
      description: Update application review's financials data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewFinancialsData'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/losses/loss_summary:
    get:
      operationId: GetApplicationReviewLossSummary
      tags:
        - 'underwriting'
      description: Return application review's loss summary widget data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewLossSummary'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewLossSummary
      tags:
        - 'underwriting'
      description: Update application review's loss summary
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewLossSummaryForm'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/losses/large_losses:
    get:
      operationId: GetApplicationReviewLargeLosses
      tags:
        - 'underwriting'
      description: Return application review's large losses widget data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewLargeLosses'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewLargeLosses
      tags:
        - 'underwriting'
      description: Update application review's large losses
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewLargeLossesForm'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/losses/loss_averages:
    get:
      operationId: GetApplicationReviewLossAverages
      tags:
        - 'underwriting'
      description: Return application review's loss averages widget data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewLossAverages'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewLossAverages
      tags:
        - 'underwriting'
      description: Update application review's loss summary
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewLossAveragesForm'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/package_type:
    get:
      operationId: GetApplicationReviewPackageType
      tags:
        - 'underwriting'
      description: Return application review's package type
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewPackageType'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewPackageType
      tags:
        - 'underwriting'
      description: Update application review's package type
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewPackageType'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/package/negotiated_rates:
    get:
      operationId: GetApplicationReviewNegotiatedRates
      tags:
        - 'underwriting'
      description: Return application review's negotiated rates and its details
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewNegotiatedRates'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewNegotiatedRates
      tags:
        - 'underwriting'
      description: Update application review's negotiated rates and its details
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewNegotiatedRates'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/coverages:
    get:
      operationId: GetApplicationReviewCoverages
      tags:
        - 'underwriting'
      description: Return application review's coverages
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewCoverages'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewCoverages
      tags:
        - 'underwriting'
      description: Update application review's coverages
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewCoverages'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/quote:
    post:
      operationId: UpdateApplicationReviewQuote
      tags:
        - 'underwriting'
      description: Trigger quote generation for an application review
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '201':
          description: Quote generation trigger successful
        '422':
          description: Unable to trigger quote generation
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    get:
      operationId: GetApplicationReviewQuote
      tags:
        - 'underwriting'
      description: Return application review's quote (global) widget data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewQuote'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/bindable-quote:
    post:
      operationId: CreateApplicationReviewBindableQuote
      tags:
        - 'underwriting'
      description: Create bindable quote for an application review
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '201':
          description: Creation of Application Review Bindable Quote successful
        '422':
          description: Unable to approve Application Review
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    get:
      operationId: GetApplicationReviewBindableQuote
      tags:
        - 'underwriting'
      description: Return bindable quote data for an application review
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewQuote'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/bindable-quote/confirm:
    post:
      operationId: ConfirmApplicationReviewBindableQuote
      tags:
        - 'underwriting'
      description: Trigger quote generation for an application review
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewConfirmBindableQuoteForm'
      responses:
        '201':
          description: Confirmation of Application Review Bindable Quote successful
        '422':
          description: Unable to approve Application Review
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/rollback:
    post:
      operationId: RollbackApplicaitonReview
      tags:
        - 'underwriting'
      description: Rollback ApplicaitonReview from declined state to pending state
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/RollbackMetadata'
      responses:
        '201':
          description: Application Review was successfully rolled back
        '422':
          description: Unable to rollback Application Review
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/decline:
    post:
      operationId: DeclineApplicationReview
      tags:
        - 'underwriting'
      description: Decline an application review
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewDeclineReasonsForm'
      responses:
        '201':
          description: Decline Application Review successful
        '422':
          description: Unable to decline Application Review
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/get_application_decline_reasons:
    get:
      operationId: GetApplicationDeclineReasons
      tags:
        - 'underwriting'
      description: Get decline reasons for an application review
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
        - in: query
          name: programType
          required: false
          schema:
            $ref: '../../components/common/spec.yaml#/components/schemas/ProgramType'
        - in: query
          name: version
          schema:
            type: integer
            minimum: 1
            example: 1
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewDeclineReasons'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/close:
    post:
      operationId: CloseApplicationReview
      tags:
        - 'underwriting'
      description: Close an application review
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewCloseReasonsForm'
      responses:
        '201':
          description: Close Application Review successful
        '422':
          description: Unable to close Application Review
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/get_application_close_reasons:
    get:
      operationId: GetApplicationCloseReasons
      tags:
        - 'underwriting'
      description: Get close reasons for an application review
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewCloseReasons'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/set_mvr_pull:
    post:
      operationId: SetMVRPull
      tags:
        - 'underwriting'
      description: Set MVR Pull flag as true
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '201':
          description: MVR Pull Flag was set to true
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/MVRFlag'
        '422':
          description: Unable to toggle MVR Pull
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/mvr/refetch:
    post:
      operationId: RefetchMVR
      tags:
        - "underwriting"
      description: Triggers MVR refetch for drivers, it also reruns pricing
      parameters:
        - $ref: "../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID"
      responses:
        '201':
          description: MVR refetch successful
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: "../../components/common/spec.yaml#/components/schemas/ErrorMessage"

  /underwriting/application_reviews/{applicationReviewID}/boards_info:
    get:
      operationId: GetApplicationReviewBoardsInfo
      tags:
        - 'underwriting'
      description: Return application review's boards info
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewBoardsInfo'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewBoardsInfo
      tags:
        - 'underwriting'
      description: Update application review's boards info
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewBoardsInfo'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'

  ###### Internal problem endpoints ##########
  /underwriting/internal/problems/{applicationID}/vin_list/:
    get:
      operationId: GetVinProblems
      tags:
        - 'underwriting'
      description: Get VIN problems for an application
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationID'
      responses:
        '200':
          description: Get successful
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationVinProblems'
    put:
      operationId: PutVinProblems
      tags:
        - 'underwriting'
      description: Update VIN problems for an application
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationVinProblems'
      responses:
        '200':
          description: VIN problems updated for application
  /underwriting/internal/problems/{applicationID}/mvr_list/:
    get:
      operationId: GetMvrProblems
      tags:
        - 'underwriting'
      description: Get MVR problems for an application
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationID'
      responses:
        '200':
          description: Get successful
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationMvrProblems'
    put:
      operationId: PutMvrProblems
      tags:
        - 'underwriting'
      description: Update MVR problems for an application
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationMvrProblems'
      responses:
        '200':
          description: MVR problems updated for application
  ## Adding/Editing/Removing ancillary coverages
  /underwriting/application_reviews/{applicationReviewID}/ancillary_coverage:
    get:
      operationId: GetAncillaryCoverages
      tags:
        - 'underwriting'
      description: Get ancillary coverages for an application review
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get All Ancillary Coverages for an application review
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/AncillaryCoverages'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    put:
      operationId: PutAncillaryCoverages
      tags:
        - 'underwriting'
      description: Update ancillary coverages for an application review
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/common/spec.yaml#/components/schemas/AncillaryCoverages'
      responses:
        '200':
          description: Ancillary coverages updated for application review
        '422':
          description: Unable to update ancillary coverages for application review
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application/{applicationID}/tsp_connection_info:
    get:
      operationId: GetApplicationTSPConnectionInfo
      tags:
        - 'underwriting'
      description: Return application's TSP connection info
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/TSPConnectionInfo'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application/{applicationID}/telematics_reminder_email_info:
    put:
      operationId: UpdateTelematicsReminderEmailInfo
      tags:
        - 'underwriting'
      description: Update application's email info
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/TelematicsReminderEmailInfo'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/losses/claim_history:
    get:
      operationId: GetApplicationReviewClaimHistory
      tags:
        - 'underwriting'
      description: Return application review's Claim History widget data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewClaimHistory'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/losses/claim_history/description:
    get:
      operationId: GetApplicationReviewClaimHistoryDescription
      tags:
        - 'underwriting'
      description: Return a specific claim history's entry policy & claim data
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/DocumentId'
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/PolicySn'
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ClaimSn'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewClaimHistoryDescription'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/safety/safer_info:
    get:
      operationId: GetSaferInfo
      tags:
        - 'underwriting'
      description: Return safer info for application review id or dot number
      parameters:
        - in: query
          name: forceRefresh
          schema:
            type: boolean
        - in: query
          name: dotNumber
          schema:
            type: string
        - in: query
          name: applicationID
          schema:
            type: string
            example: a81bc81b-dead-4e5d-abff-90865d1e13b1
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/GetSaferResponse'
        default:
          description: Error Response not received
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/set_attract_score:
    post:
      operationId: SetAttractScore
      tags:
        - 'underwriting'
      description: Set Attract Score flag as true
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '204':
          description: Attract Score Flag was set to true
        '404':
          description: Resource Not Found
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        '422':
          description: Unprocessable Entity
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  # Persistence APIs
  /underwriting/application_review/{applicationReviewID}/repull:
    post:
      operationId: RepullApplicationReviewWidget
      tags:
        - 'underwriting'
      description: Re-pull application review widget
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewWidgetRepull'
      responses:
        '201':
          description: Widget re-pulled successfully.
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewRepullWidgetResponse'
        '404':
          description: Widget not found
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'

  /underwriting/application_review/backfillWidgets:
    post:
      operationId: BackfillApplicationReview
      tags:
        - 'underwriting'
      description: Back-fill application review widgets data,rating,overrides
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewWidgetBackFillForm'
      responses:
        '201':
          description: Application review back-filled successfully.
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewWidgetBackfillResponse'
        '404':
          description: Application review not found
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_review/{applicationReviewID}/mileage_estimate_reasons:
    get:
      operationId: GetMileageEstimateReasons
      tags:
        - 'underwriting'
      description: Return mileage estimate reasons
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/MileageEstimateReasons'
        '500':
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/recommendation:
    get:
      operationId: GetApplicationReviewRecommendations
      tags:
        - 'underwriting'
      description: Returns the recommendations for an application review
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/Recommendations'
        '404':
          description: Recommendations not found
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/recommendation/{experimentId}/conclusion:
    patch:
      operationId: UpdateApplicationReviewRecommendationConclusion
      tags:
        - 'underwriting'
      description: Update application review's recommendation with conclusion (e.g. accept)
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
        - in: path
          name: experimentId
          required: true
          schema:
            type: string
            example: d754a727-80fd-468d-b2d0-8c8659eac8f3
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewRecommendationForm'
      responses:
        '200':
          description: Updated successfully
        '400':
          description: Bad Request (url params or form data are invalid)
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        '422':
          description: Unprocessable data
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'

  /underwriting/application_reviews/regenerate_authority:
    post:
      operationId: RegenerateAuthority
      tags:
        - 'underwriting'
      description: Regenerate authority data for all open application reviews
      responses:
        '200':
          description: Authority regenerated successfully
        '400':
          description: Bad Request (url params or form data are invalid)
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        '422':
          description: Unprocessable data
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'

  /underwriting/application_reviews/{applicationReviewID}/overview/recommended_action:
    get:
      operationId: GetApplicationReviewOverviewRecommendedAction
      tags:
        - 'underwriting'
      description: Fetches recommendation action, traditional risk factors and TRS details
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Recommended Action Overview fetched successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewOverviewRecommendedActionDetails'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'

  /underwriting/application_reviews/{applicationReviewID}/recommended_action_trail:
    get:
      operationId: GetApplicationReviewRecommendedActionTrail
      tags:
        - 'underwriting'
      description: Fetches recommendation action trail of change in recommended action, TRS & risk factors
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Recommended Action Trail fetched successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewRecommendedActionTrail'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/recommended_action/notification:
    get:
      operationId: GetRecommendedActionNotification
      tags:
        - 'underwriting'
      description: Fetches notification for recommended action
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Recommended Action Notification fetched successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/RecommendedActionNotificationInfo'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/recommended_action/notification/{notificationId}/acknowledge:
    patch:
      operationId: SetRecommendedActionNotificationAcknowledged
      tags:
        - 'underwriting'
      description: Acknowledge notification for recommended action
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
        - in: path
          name: notificationId
          required: true
          schema:
            type: string
            example: d754a727-80fd-468d-b2d0-8c8659eac8f3
      responses:
        '200':
          description: Updated successfully
        '400':
          description: Bad Request (url params or form data are invalid)
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        '422':
          description: Unprocessable data
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/actions:
    get:
      operationId: GetApplicationReviewActions
      tags:
        - 'underwriting'
        - 'action'
      description: Returns a list of actions visible to the user
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successful
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewGetActionsResponse'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'

  /underwriting/application_review/recalculate-current-telematics-connection-state:
    post:
      operationId: RecalculateTelematicsConnectionState
      tags:
        - 'underwriting'
        - 'adhoc'
      description: This is an adhoc API to recalculate and update current telematics connection state for an app review
      parameters:
        - in: query
          name: applicationReviewId
          schema:
            type: string
          required: true
          description: review ID for which recalculation needs to be done
        - in: query
          name: shouldUpdate
          schema:
            type: boolean
          required: false
          description: If passed then state is also updated in DB, otherwise state is only returned in response and DB is not updated, default is false
      responses:
        '201':
          description: API successful
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/RecalculateTelematicsConnectionStateResponse'

  /underwriting/application_review/{applicationReviewID}/vin_visibility:
    get:
      operationId: GetVinVisibility
      tags:
        - 'underwriting'
      description: Get VIN visibility for an application review
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successful
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewVinVisibility'
        '500':
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'

  /underwriting/application_review/{applicationReviewID}/vin_visibility_check_list:
    get:
      operationId: GetVinVisibilityCheckList
      tags:
        - 'underwriting'
      description: Get VIN visibility check list for an application review
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successful
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewVinVisibilityCheckListResponse'
        '500':
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    put:
        operationId: UpdateVinVisibilityCheckList
        tags:
            - 'underwriting'
        description: Update VIN visibility check list for an application review
        parameters:
            - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
        requestBody:
            required: true
            content:
              application/json:
                schema:
                  $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewVinVisibilityChecklistPut'
        responses:
            '200':
              description: VIN visibility check list updated for application review
            '422':
              description: Unable to update VIN visibility check list for application review
              content:
                application/json:
                  schema:
                      $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
 ## MST REFERRAL APIs ##
  /underwriting/application_review/{applicationReviewID}/mst_referral:
    get:
      operationId: GetMstReferral
      tags:
        - 'underwriting'
      description: Get MST Referral for an application review
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successful
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/GetApplicationReviewMstReferral'
        '500':
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
        operationId: UpdateMstReferral
        tags:
            - 'underwriting'
        description: Update MST Referral for an application review
        parameters:
            - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
        requestBody:
            required: true
            content:
              application/json:
                schema:
                  $ref: '../../components/underwriting/spec.yaml#/components/schemas/PatchApplicationReviewMstReferralRule'
        responses:
            '200':
              description: MST Referral updated for application review
            '422':
              description: Unable to update MST Referral for application review
              content:
                application/json:
                  schema:
                      $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'

  /underwriting/application_review/{applicationReviewID}/mst_referral_review:
    get:
      operationId: GetMstReferralReview
      tags:
        - 'underwriting'
      description: Get MST Referral Review
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successful
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewMstReferralReview'
        '500':
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'

    patch:
      operationId: UpdateMstReferralReview
      tags:
        - 'underwriting'
      description: Update MST Referral
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewMstReferralReviewForm'
      responses:
        '200':
          description: MST Referral updated
        '422':
          description: Unable to update MST Referral
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'

  /underwriting/explainability/{applicationReviewID}/factor-rankings:
    get:
      operationId: GetFactorRankings
      tags:
        - 'underwriting'
      description: Get factor rankings for a submissionID
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successful
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/FactorRankingsResponse'
        '422': 
          description: Unable to process artifact
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        '500':
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
                

  ###############################################################
  ################## Clearance Related APIs ###################
  ###############################################################

  /application/cleared_application:
    get:
      tags:
        - 'Clearance'
      description: Gets the cleared application for a given DOT number and effective date
      parameters:
        - in: query
          name: effectiveDate
          schema:
            type: string
            format: date
        - in: query
          name: dotNumber
          schema:
            type: string
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ClearedApplicationResponse'
        '400':
          description: Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'

  /application/{applicationID}/clear:
    post:
      tags:
        - 'Clearance'
      description: Clears the application for a given Application ID
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationID'
      responses:
        '200':
          description: Cleared successfully
        '400':
          description: Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'

  /underwriting/application_reviews/{applicationReviewID}/target_price:
    get:
      operationId: GetApplicationReviewTargetPrice
      tags:
        - 'underwriting'
      description: Get Target Price entered by the underwriter
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successful
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewTargetPriceOverride'
        '500':
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    put:
      operationId: UpdateApplicationReviewTargetPrice
      tags:
        - 'underwriting'
      description: Update application review's target price
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewTargetPriceOverride'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/review_readiness_task/{applicationReviewID}/list:
    get:
      operationId: GetReviewReadinessTaskList
      tags:
        - 'underwriting'
      description: Get review readiness tasks list for an application review
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ReviewReadinessTaskListResponse'
        '422':
          description: Unable to get review readiness tasks
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: BulkUpdateReviewReadinessTasks
      tags:
        - 'underwriting'
      description: Bulk update review readiness tasks
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/BulkUpdateReviewReadinessTaskRequest'
      responses:
        '200':
          description: Updated successfully
        '422':
          description: Unprocessable data
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/review_readiness_task/{applicationReviewID}/ready:
    post:
      operationId: ReadyApplicationReview
      tags:
        - 'underwriting'
      description: Make application ready for review
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Update successful
        '422':
          description: Unprocessable data
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/risk_factors:
    get:
      summary: List all risk factors
      operationId: listRiskFactors
      tags:
        - 'underwriting'
      responses:
        '200':
          description: A list of risk factors
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/RiskFactors'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/risk_factors/worksheet/{applicationReviewID}:
    get:
      summary: Get complete worksheet details including risk factors and pricing
      operationId: getWorksheet
      tags:
        - 'underwriting'
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Complete worksheet details
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/WorksheetResponse'
        '404':
          description: Complete worksheet details
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'

  /underwriting/risk_factors/worksheet/{worksheetID}:
    patch:
      summary: Update worksheet details
      operationId: updateWorksheet
      tags:
        - 'underwriting'
      parameters:
        - name: worksheetID
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/UpdateWorksheetRequest'
      responses:
        '200':
          description: Updated worksheet details
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/WorksheetResponse'
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        '404':
          description: Worksheet not found
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'

  /underwriting/risk_factors/worksheet/{worksheetID}/risk_factors:
    post:
      summary: Add a risk factor to worksheet
      operationId: addWorksheetRiskFactor
      tags:
        - underwriting
      parameters:
        - name: worksheetID
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/WorksheetRiskFactorAddRequest'
      responses:
        '201':
          description: Risk factor added to worksheet
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/WorksheetRiskFactor'
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        '404':
          description: Worksheet not found
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/risk_factors/worksheet/{worksheetRiskFactorID}:
    put:
      summary: Update a worksheet risk factor
      operationId: updateWorksheetRiskFactor
      tags:
        - underwriting
      parameters:
        - name: worksheetRiskFactorID
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/WorksheetRiskFactorUpdateRequest'
      responses:
        '200':
          description: Updated worksheet risk factor
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/WorksheetRiskFactor'
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        '404':
          description: Worksheet or risk factor not found
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'

    delete:
      summary: Delete a risk factor from worksheet
      operationId: deleteWorksheetRiskFactor
      tags:
        - underwriting
      parameters:
        - name: worksheetRiskFactorID
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: Risk factor removed from worksheet
        '404':
          description: Worksheet or risk factor not found
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'

  /underwriting/risk_factors/worksheet/{worksheetID}/pricing:
    patch:
      summary: Update worksheet pricing details
      operationId: updateWorksheetPricing
      tags:
        - underwriting
      parameters:
        - name: worksheetID
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/UpdatePricingRequest'
      responses:
        '200':
          description: Updated pricing details
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/WorksheetResponse'
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        '404':
          description: Worksheet not found
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'

  /underwriting/application_review/{applicationReviewID}/risk_worksheet:
    post:
      summary: Create a risk worksheet from review id
      operationId: createRiskWorksheet
      tags:
            - underwriting
      parameters:
            - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
            '201':
              description: Risk worksheet created
              content:
                application/json:
                  schema:
                    $ref: '../../components/underwriting/spec.yaml#/components/schemas/WorksheetResponse'
            '400':
              description: Invalid request
              content:
                application/json:
                  schema:
                    $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/risk_factors/{worksheetID}/suggest:
    post:
      summary: Suggest new risk factor
      operationId: suggestRiskFactor
      tags:
        - underwriting
      parameters:
        - name: worksheetID
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/SuggestedRiskFactor'
      responses:
        '201':
          description: Risk factor suggestion created
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/RiskFactor'
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'


  /underwriting/application_review/{applicationReviewID}/current_status:
    get:
      operationId: GetCurrentStatus
      tags:
        - 'underwriting'
      description: Get Current Status
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successful
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewCurrentStatusRequest'
        '500':
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'

    patch:
      operationId: UpdateCurrentStatus
      tags:
        - 'underwriting'
      description: Update Current Status
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewCurrentStatusForm'
      responses:
        '200':
          description: Review Current Status updated
        '422':
          description: Unable to update review current status
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'

  /underwriting/application_review/{applicationReviewID}/report:
    post:
      operationId: PostReport
      tags:
        - 'underwriting'
      description: Post a request to generate report
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewReportForm'
      responses:
        '200':
          description: Request to generate report successful
        '422':
          description: Unable to place a request to generate report
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/application_reviews/{applicationReviewID}/panel_notes:
    get:
      operationId: GetApplicationReviewPanelNotes
      tags:
        - 'underwriting'
      description: Return application review panel's notes
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewPanelNotes'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    patch:
      operationId: UpdateApplicationReviewPanelNotes
      tags:
        - 'underwriting'
      description: Update application review panel's notes
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewPanelNotes'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'

  /underwriting/application_reviews/{applicationReviewID}/v2/losses/loss_summary:
    post:
      operationId: GetApplicationReviewLossSummaryV2
      tags:
        - 'underwriting'
      description: Return application review's loss summary widget data (V2)
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewLossSummaryV2Request'
      responses:
        '200':
          description: Get successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewLossSummaryV2'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
    put:
      operationId: UpdateApplicationReviewLossSummaryV2
      tags:
        - 'underwriting'
      description: Update application review's loss summary widget data (V2)
      parameters:
        - $ref: '../../components/underwriting/spec.yaml#/components/parameters/ApplicationReviewID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ApplicationReviewLossSummaryV2Form'
      responses:
        '200':
          description: Updated successfully
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
                
  # Authority Request APIs
  /underwriting/authority/request:
    post:
      operationId: CreateAuthorityRequest
      tags:
        - 'underwriting'
      description: Create a new authority request for an application review
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/AuthorityRequest'
      responses:
        '201':
          description: Authority request created successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/AuthorityRequestResponse'
        '400':
          description: Invalid request data
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        '403':
          description: Forbidden - User does not have permission to create this type of request
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'

  /underwriting/application_review/{applicationReviewId}/authority/request/latest:
    get:
      operationId: GetAuthorityRequest
      tags:
        - 'underwriting'
      description: Get the latest authority request for an application review
      parameters:
        - in: path
          name: applicationReviewId
          required: true
          schema:
            type: string
            format: uuid
          description: The ID of the application review
      responses:
        '200':
          description: Authority request details retrieved successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/AuthorityRequestDetails'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        '403':
          description: Forbidden - User does not have permission to view this request
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        '404':
          description: Authority request not found
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
  /underwriting/authority/request/{requestId}/process:
    post:
      operationId: ProcessAuthorityRequest
      tags:
        - 'underwriting'
      description: Process (approve or reject) an authority request
      parameters:
        - in: path
          name: requestId
          required: true
          schema:
            type: string
            format: uuid
          description: The ID of the authority request to process
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '../../components/underwriting/spec.yaml#/components/schemas/ProcessAuthorityRequest'
      responses:
        '200':
          description: Authority request processed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Authority request processed successfully
                  request_id:
                    type: string
                    format: uuid
                  state:
                    $ref: '../../components/underwriting/spec.yaml#/components/schemas/AuthorityRequestState'
        '400':
          description: Invalid request - request not in pending state or invalid action
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        '403':
          description: Forbidden - User does not have permission to review authority requests
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        '404':
          description: Authority request not found
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        '422':
          description: Processing failed - execution error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'

  /underwriting/application_review/{applicationReviewId}/authority/requests:
    get:
      operationId: GetAuthorityRequestsForApplication
      tags:
        - 'underwriting'
      description: Get all authority requests for a specific application review
      parameters:
        - in: path
          name: applicationReviewId
          required: true
          schema:
            type: string
            format: uuid
          description: The ID of the application review
      responses:
        '200':
          description: Authority requests retrieved successfully
          content:
            application/json:
              schema:
                $ref: '../../components/underwriting/spec.yaml#/components/schemas/AuthorityRequestList'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        '404':
          description: Application review not found
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'
        default:
          description: Unknown Error
          content:
            application/json:
              schema:
                $ref: '../../components/common/spec.yaml#/components/schemas/ErrorMessage'

components:
  securitySchemes:
    sessionIdAuth:
      type: apiKey
      in: header
      name: JSESSIONID
