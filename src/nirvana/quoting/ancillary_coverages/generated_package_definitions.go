// Code generated by matrix_codegen. DO NOT EDIT.

package ancillary_coverages

import (
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
)

var PackageCommonCoverages = map[policy_enums.ProgramType][]PackageCommonAncillaryCoverage{
	policy_enums.ProgramTypeFleet: []PackageCommonAncillaryCoverage{
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageUM,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageUIM,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoveragePersonalInjuryProtection,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoveragePIPExcessAttendantCare,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoveragePersonalInjuryProtectionBasic,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoveragePersonalInjuryProtectionIncreased,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoveragePropertyProtectionInsurance,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageMedicalPayments,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageUMUIM,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageUMBIUIMBI,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageUninsuredMotoristPropertyDamage,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageUnderinsuredMotoristPropertyDamage,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageUninsuredMotoristBodilyInjury,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageUnderinsuredMotoristBodilyInjury,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageTerrorism,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageReeferWithHumanError,
			IsStatutory:  false,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageGuestPersonalInjuryProtection,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageReeferWithoutHumanError,
			IsStatutory:  false,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageStopGap,
			IsStatutory:  false,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageCargoAtScheduledTerminals,
			IsStatutory:  false,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageDebrisRemoval,
			IsStatutory:  false,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageEarnedFreight,
			IsStatutory:  false,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoveragePollutantCleanupAndRemoval,
			IsStatutory:  false,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageLossMitigationExpenses,
			IsStatutory:  false,
		},
	},
	policy_enums.ProgramTypeNonFleetAdmitted: []PackageCommonAncillaryCoverage{
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageUM,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageUIM,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoveragePersonalInjuryProtection,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoveragePIPExcessAttendantCare,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoveragePersonalInjuryProtectionBasic,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoveragePersonalInjuryProtectionIncreased,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoveragePropertyProtectionInsurance,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageMedicalPayments,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageUMUIM,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageUMBIUIMBI,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageUninsuredMotoristPropertyDamage,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageUnderinsuredMotoristPropertyDamage,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageUninsuredMotoristBodilyInjury,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageUnderinsuredMotoristBodilyInjury,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageTerrorism,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageReeferWithHumanError,
			IsStatutory:  false,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageReefer,
			IsStatutory:  false,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageDebrisRemoval,
			IsStatutory:  false,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageBroadenedPollution,
			IsStatutory:  false,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageTowingLaborAndStorage,
			IsStatutory:  false,
		},
	},
	policy_enums.ProgramTypeBusinessAuto: []PackageCommonAncillaryCoverage{
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoveragePersonalInjuryProtection,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageMedicalPayments,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageUMUIM,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageUninsuredMotoristPropertyDamage,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageUninsuredMotoristBodilyInjury,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageUnderinsuredMotoristBodilyInjury,
			IsStatutory:  true,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageBlanketAdditional,
			IsStatutory:  false,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageBlanketWaiverOfSubrogation,
			IsStatutory:  false,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageBlanketAdditionalPNC,
			IsStatutory:  false,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageTerrorism,
			IsStatutory:  false,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageTowingLaborAndStorage,
			IsStatutory:  false,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageRentalReimbursement,
			IsStatutory:  false,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageHiredAuto,
			IsStatutory:  false,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageNonOwnedAuto,
			IsStatutory:  false,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageMedicalExpenseBenefits,
			IsStatutory:  false,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageWorkLossBenefits,
			IsStatutory:  false,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageFuneralExpenseBenefits,
			IsStatutory:  false,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageAccidentalDeathBenefits,
			IsStatutory:  false,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageExtraordinaryMedicalBenefits,
			IsStatutory:  false,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageUMUIMAddedTo,
			IsStatutory:  false,
		},
		PackageCommonAncillaryCoverage{
			CoverageType: app_enums.CoverageUMUIMReducedBy,
			IsStatutory:  false,
		},
	},
}

var PackageSpecificCoverages = map[app_enums.IndicationOptionTag]map[policy_enums.ProgramType][]app_enums.Coverage{
	app_enums.IndicationOptionTagBasic: {
		policy_enums.ProgramTypeFleet:            []app_enums.Coverage{},
		policy_enums.ProgramTypeNonFleetAdmitted: []app_enums.Coverage{},
	},
	app_enums.IndicationOptionTagStandard: {
		policy_enums.ProgramTypeFleet: []app_enums.Coverage{
			app_enums.CoverageBlanketAdditional,
			app_enums.CoverageBroadenedPollution,
			app_enums.CoverageTrailerInterchange,
			app_enums.CoverageUIIA,
			app_enums.CoverageMiscellaneousEquipment,
		},
		policy_enums.ProgramTypeNonFleetAdmitted: []app_enums.Coverage{
			app_enums.CoverageRentalReimbursement,
			app_enums.CoverageBlanketAdditional,
			app_enums.CoverageBlanketWaiverOfSubrogation,
		},
	},
	app_enums.IndicationOptionTagComplete: {
		policy_enums.ProgramTypeFleet: []app_enums.Coverage{
			app_enums.CoverageBroadenedPollution,
			app_enums.CoverageBlanketAdditional,
			app_enums.CoverageTrailerInterchange,
			app_enums.CoverageBlanketWaiverOfSubrogation,
			app_enums.CoverageEnhancedPackageTowingLimit,
			app_enums.CoverageUIIA,
			app_enums.CoverageMiscellaneousEquipment,
			app_enums.CoverageCargoTrailerInterchange,
		},
		policy_enums.ProgramTypeNonFleetAdmitted: []app_enums.Coverage{
			app_enums.CoverageHiredAuto,
			app_enums.CoverageRentalReimbursement,
			app_enums.CoverageTrailerInterchange,
			app_enums.CoverageBlanketAdditional,
			app_enums.CoverageBlanketWaiverOfSubrogation,
			app_enums.CoverageNonOwnedTrailer,
			app_enums.CoverageUIIA,
		},
	},
}
