package jobs

import (
	"encoding/json"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/emailer/models"
	"nirvanatech.com/nirvana/jobber/jtypes"
	"nirvanatech.com/nirvana/quoting/app_state_machine/enums"
)

const (
	GenerateQuotePDF                           = "GenerateQuotePDF"
	GenerateAppPDF                             = "GenerateAppPDF"
	SendTelematicsConsentReminderEmail         = "SendTelematicsConsentReminderEmail"
	ScheduleTelematicsReminderEmails           = "ScheduleTelematicsReminderEmails"
	SendSubmissionAcknowledgmentEmail          = "SendSubmissionAcknowledgmentEmail"
	SendTelematicsConnectionSuccessfulEmail    = "SendTelematicsConnectionSuccessfulEmail"
	TransitionAppsToClosedState                = "TransitionAppsToClosedState"
	SendPostBindOnboardingEmail                = "SendPostBindOnboardingEmail"
	DataPipelineStateChanged                   = "DataPipelineStateChanged"
	MarkPendingAppReviewsToStale               = "MarkPendingAppReviewsToStale"
	TriggerCarrierLoyaltyMetaflow              = "TriggerCarrierLoyaltyMetaflow"
	PopulatePolicySet                          = "PopulatePolicySet"
	TransitionPoliciesToExpiredStatus          = "TransitionPoliciesToExpiredStatus"
	TransitionPoliciesToActiveStatus           = "TransitionPoliciesToActiveStatus"
	TriggerParseLossRuns                       = "TriggerParseLossRuns"
	AggregateLossRuns                          = "AggregateLossRuns"
	SendTelematicsConnectionOnAgentBehalfEmail = "SendTelematicsConnectionOnAgentBehalfEmail"
	SendTelematicsConnectionSMSToInsured       = "SendTelematicsConnectionSMSToInsured"

	CheckClearance                    = "CheckClearance"
	UpdateSfdcWinningCarrier          = "UpdateSfdcWinningCarrier"
	AppetiteLiteSnowflakePostgresSync = "AppetiteLiteSnowflakePostgresSync"
	ReportRiskMetrics                 = "ReportRiskMetrics"
	SendUserInviteEmail               = "SendUserInviteEmail"
	VerifyTSPConnection               = "VerifyTSPConnection"
	SyncApplicationBDs                = "SyncApplicationBDs"

	GenerateIndicationWorkflow = "GenerateIndicationWorkflow"
	GenerateIndicationRun      = "GenerateIndicationRun"

	GenerateSubmissionForUWWorkflow = "GenerateSubmissionForUWWorkflow"
	GenerateSubmissionForUWRun      = "GenerateSubmissionForUWRun"

	UpdateSubmissionForUWWorkflow     = "UpdateSubmissionForUWWorkflow"
	UpdateSubmissionForUWRun          = "UpdateSubmissionForUWRun"
	UpdateSubmissionForUWOrchestrator = "UpdateSubmissionForUWOrchestrator"
	GenerateQuoteWorkflow             = "GenerateQuoteWorkflow"
	GenerateQuoteRun                  = "GenerateQuoteRun"

	AssignUnderwriter           = "AssignUnderwriter"
	AutoSelectSafetyScore       = "AutoSelectSafetyScore"
	ExpressLaneAutoUnderwriting = "ExpressLaneAutoUnderwriting"
)

type GenerateQuotePDFArgs struct {
	SubmissionID      string
	SignaturePacketID string
}

func (s *GenerateQuotePDFArgs) MarshalJSON() ([]byte, error) {
	return json.Marshal(*s)
}

func (s *GenerateQuotePDFArgs) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(0)
}

func GenerateQuotePDFUnmarshalFn(data []byte, m jtypes.MessageVersion) (*GenerateQuotePDFArgs, error) {
	var s GenerateQuotePDFArgs
	if m != 0 {
		return &s, errors.Newf("wrong message version")
	}
	if err := json.Unmarshal(data, &s); err != nil {
		return &s, err
	}
	return &s, nil
}

type GenerateAppPDFArgs struct {
	SubmissionID string
}

func (s *GenerateAppPDFArgs) MarshalJSON() ([]byte, error) {
	return json.Marshal(*s)
}

func (s *GenerateAppPDFArgs) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(0)
}

func GenerateAppPDFUnmarshalFn(data []byte, m jtypes.MessageVersion) (*GenerateAppPDFArgs, error) {
	var s GenerateAppPDFArgs
	if m != 0 {
		return &s, errors.Newf("wrong message version")
	}
	if err := json.Unmarshal(data, &s); err != nil {
		return &s, err
	}
	return &s, nil
}

type SendTelematicsConsentReminderEmailArgs struct {
	ApplicationID string
}

func (s *SendTelematicsConsentReminderEmailArgs) MarshalJSON() ([]byte, error) {
	return json.Marshal(*s)
}

func (s *SendTelematicsConsentReminderEmailArgs) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(0)
}

func SendTelematicsConsentReminderEmailUnmarshalFn(
	data []byte, m jtypes.MessageVersion,
) (*SendTelematicsConsentReminderEmailArgs, error) {
	var s SendTelematicsConsentReminderEmailArgs
	if m != 0 {
		return &s, errors.Newf("wrong message version")
	}
	if err := json.Unmarshal(data, &s); err != nil {
		return &s, err
	}
	return &s, nil
}

type SendTelematicsConnectionSMSToInsuredArgs struct {
	ApplicationID string
	Recipients    []string
	ProgramType   policy_enums.ProgramType
}

func (s *SendTelematicsConnectionSMSToInsuredArgs) MarshalJSON() ([]byte, error) {
	if s == nil {
		return []byte{}, errors.New("SendTelematicsConnectionSMSToInsuredArgs is nil")
	}
	return json.Marshal(*s)
}

func (s *SendTelematicsConnectionSMSToInsuredArgs) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(0)
}

func SendTelematicsConnectionSMSToInsuredUnmarshalFn(
	data []byte,
	_ jtypes.MessageVersion,
) (*SendTelematicsConnectionSMSToInsuredArgs, error) {
	var s SendTelematicsConnectionSMSToInsuredArgs
	if err := json.Unmarshal(data, &s); err != nil {
		return &s, err
	}
	return &s, nil
}

type ScheduleTelematicsConsentReminderEmailsArgs struct {
	ApplicationID string
}

func (s *ScheduleTelematicsConsentReminderEmailsArgs) MarshalJSON() ([]byte, error) {
	return json.Marshal(*s)
}

func (s *ScheduleTelematicsConsentReminderEmailsArgs) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(0)
}

func ScheduleTelematicsConsentReminderEmailsUnmarshalFn(
	data []byte, m jtypes.MessageVersion,
) (*ScheduleTelematicsConsentReminderEmailsArgs, error) {
	var s ScheduleTelematicsConsentReminderEmailsArgs
	if m != 0 {
		return &s, errors.Newf("wrong message version")
	}
	if err := json.Unmarshal(data, &s); err != nil {
		return &s, err
	}
	return &s, nil
}

type SendSubmissionAcknowledgmentEmailArgs struct {
	ApplicationID string
}

func (s *SendSubmissionAcknowledgmentEmailArgs) MarshalJSON() ([]byte, error) {
	return json.Marshal(*s)
}

func (s *SendSubmissionAcknowledgmentEmailArgs) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(0)
}

func SendSubmissionAcknowledgmentEmailUnmarshalFn(
	data []byte, m jtypes.MessageVersion,
) (*SendSubmissionAcknowledgmentEmailArgs, error) {
	var s SendSubmissionAcknowledgmentEmailArgs
	if m != 0 {
		return &s, errors.Newf("wrong message version")
	}
	if err := json.Unmarshal(data, &s); err != nil {
		return &s, err
	}
	return &s, nil
}

type SendPostBindOnboardingEmailArgs struct {
	ApplicationID string
}

func (s *SendPostBindOnboardingEmailArgs) MarshalJSON() ([]byte, error) {
	return json.Marshal(*s)
}

func (s *SendPostBindOnboardingEmailArgs) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(0)
}

func SendPostBindOnboardingEmailUnmarshalFn(
	data []byte, m jtypes.MessageVersion,
) (*SendPostBindOnboardingEmailArgs, error) {
	var s SendPostBindOnboardingEmailArgs
	if m != 0 {
		return &s, errors.Newf("wrong message version")
	}
	if err := json.Unmarshal(data, &s); err != nil {
		return &s, err
	}
	return &s, nil
}

type MarkPendingAppReviewsToStaleArgs struct {
	ApplicationId string
}

func (m *MarkPendingAppReviewsToStaleArgs) MarshalJSON() ([]byte, error) {
	return json.Marshal(*m)
}

func (m *MarkPendingAppReviewsToStaleArgs) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(0)
}

func MarkPendingAppReviewToStaleUnmarshalFn(
	data []byte,
	m jtypes.MessageVersion,
) (*MarkPendingAppReviewsToStaleArgs, error) {
	var s MarkPendingAppReviewsToStaleArgs
	if m != 0 {
		return &s, errors.Newf("wrong message version")
	}
	if err := json.Unmarshal(data, &s); err != nil {
		return &s, err
	}
	return &s, nil
}

var _ jtypes.Message = &MarkPendingAppReviewsToStaleArgs{}

type TriggerCarrierLoyaltyMetaflowArgs struct {
	ApplicationId string
	SubmissionId  string
}

func (t *TriggerCarrierLoyaltyMetaflowArgs) MarshalJSON() ([]byte, error) {
	return json.Marshal(*t)
}

func (t *TriggerCarrierLoyaltyMetaflowArgs) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(0)
}

func TriggerCarrierLoyaltyMetaflowUnmarshalFn(data []byte, m jtypes.MessageVersion,
) (*TriggerCarrierLoyaltyMetaflowArgs, error) {
	var s TriggerCarrierLoyaltyMetaflowArgs
	if m != 0 {
		return &s, errors.Newf("wrong message version")
	}
	if err := json.Unmarshal(data, &s); err != nil {
		return &s, err
	}
	return &s, nil
}

type PopulatePolicySetArgs struct{}

func (p *PopulatePolicySetArgs) MarshalJSON() ([]byte, error) {
	return json.Marshal(*p)
}

func (p *PopulatePolicySetArgs) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(0)
}

func PopulatePolicySetUnmarshalFn(
	data []byte, m jtypes.MessageVersion,
) (*PopulatePolicySetArgs, error) {
	var p PopulatePolicySetArgs
	if m != 0 {
		return &p, errors.Newf("wrong message version")
	}
	return &p, nil
}

type TriggerParseLossRunsArgs struct {
	ApplicationId string
	SubmissionId  string
	AgencyId      uuid.UUID
}

func (t *TriggerParseLossRunsArgs) MarshalJSON() ([]byte, error) {
	return json.Marshal(*t)
}

func (t *TriggerParseLossRunsArgs) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(0)
}

func TriggerParseLossRunsUnmarshalFn(data []byte, m jtypes.MessageVersion,
) (*TriggerParseLossRunsArgs, error) {
	var s TriggerParseLossRunsArgs
	if m != 0 {
		return &s, errors.Newf("wrong message version")
	}
	if err := json.Unmarshal(data, &s); err != nil {
		return &s, err
	}
	return &s, nil
}

type AggregateLossRunsArgs struct {
	ApplicationId string
}

func (a *AggregateLossRunsArgs) MarshalJSON() ([]byte, error) {
	return json.Marshal(*a)
}

func (a *AggregateLossRunsArgs) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(0)
}

func AggregateLossRunsUnmarshalFn(data []byte, m jtypes.MessageVersion) (*AggregateLossRunsArgs, error) {
	var a AggregateLossRunsArgs
	if m != 0 {
		return &a, errors.Newf("wrong message version")
	}
	if err := json.Unmarshal(data, &a); err != nil {
		return &a, err
	}
	return &a, nil
}

type SendTelematicsConnectionOnAgentBehalfEmailArgs struct {
	ApplicationID     string
	PrimaryRecipients []models.Contact
	CCRecipients      []models.Contact
	EmailTemplate     *models.Email
	ProgramType       *policy_enums.ProgramType
}

func (s *SendTelematicsConnectionOnAgentBehalfEmailArgs) MarshalJSON() ([]byte, error) {
	if s == nil {
		return []byte{}, errors.New("SendTelematicsConnectionOnAgentBehalfEmailArgs is nil")
	}
	return json.Marshal(*s)
}

func (s *SendTelematicsConnectionOnAgentBehalfEmailArgs) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(0)
}

func SendTelematicsConnectionOnAgentBehalfEmailUnmarshalFn(
	data []byte,
	_ jtypes.MessageVersion,
) (*SendTelematicsConnectionOnAgentBehalfEmailArgs, error) {
	var s SendTelematicsConnectionOnAgentBehalfEmailArgs
	if err := json.Unmarshal(data, &s); err != nil {
		return &s, err
	}
	return &s, nil
}

type CheckClearanceArgs struct {
	ApplicationID string
}

func (c *CheckClearanceArgs) MarshalJSON() ([]byte, error) {
	if c == nil {
		return []byte{}, errors.New("CheckClearanceArgs is nil")
	}
	return json.Marshal(*c)
}

func (c *CheckClearanceArgs) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(0)
}

func CheckClearanceUnmarshalFn(
	data []byte,
	_ jtypes.MessageVersion,
) (*CheckClearanceArgs, error) {
	var c CheckClearanceArgs
	if err := json.Unmarshal(data, &c); err != nil {
		return &c, err
	}
	return &c, nil
}

type SendUserInviteEmailArgs struct {
	InvitedUserId uuid.UUID
	InviterUserId uuid.UUID
	ShareID       uuid.UUID
	AgencyID      uuid.UUID
}

func (s *SendUserInviteEmailArgs) MarshalJSON() ([]byte, error) {
	return json.Marshal(*s)
}

func (s *SendUserInviteEmailArgs) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(0)
}

func SendUserInviteEmailUnmarshalFn(
	data []byte, m jtypes.MessageVersion,
) (*SendUserInviteEmailArgs, error) {
	var s SendUserInviteEmailArgs
	if m != 0 {
		return &s, errors.Newf("wrong message version")
	}
	if err := json.Unmarshal(data, &s); err != nil {
		return &s, err
	}
	return &s, nil
}

type ReportRiskMetricsArgs struct {
	SubmissionId string

	AppState enums.AppState
}

func (s ReportRiskMetricsArgs) MarshalJSON() ([]byte, error) {
	type alias ReportRiskMetricsArgs
	return json.Marshal(alias(s))
}

func (s ReportRiskMetricsArgs) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(2)
}

func ReportRiskMetricsUnmarshalFn(
	data []byte,
	m jtypes.MessageVersion,
) (ReportRiskMetricsArgs, error) {
	var r ReportRiskMetricsArgs
	if m != 2 {
		return r, errors.Newf("wrong message version")
	}
	if err := json.Unmarshal(data, &r); err != nil {
		return r, err
	}
	return r, nil
}

//go:generate go run github.com/dmarkham/enumer -type=UpdateSubmissionForUWTriggerReason -json
type UpdateSubmissionForUWTriggerReason int

const (
	// InvalidTriggerReason to handle unmarshalling of old messages
	InvalidTriggerReason UpdateSubmissionForUWTriggerReason = iota
	QuoteRefresh
	ReadyForReview
)

type IndicationOptionSpec struct {
	ID          uuid.UUID
	PackageType app_enums.IndicationOptionTag
}

type IndicationWorkflowMessage struct {
	SubmissionID          uuid.UUID
	IndicationOptionSpecs []IndicationOptionSpec
}

func (s *IndicationWorkflowMessage) MarshalJSON() ([]byte, error) {
	return json.Marshal(*s)
}

func (s *IndicationWorkflowMessage) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(0)
}

var _ jtypes.Message = (*IndicationWorkflowMessage)(nil)

func IndicationWorkflowMessageUnmarshalFn(
	data []byte, m jtypes.MessageVersion,
) (*IndicationWorkflowMessage, error) {
	var s IndicationWorkflowMessage
	if m != 0 {
		return &s, errors.Newf("wrong message version")
	}
	if err := json.Unmarshal(data, &s); err != nil {
		return &s, err
	}
	return &s, nil
}

type IndicationRunMessage struct {
	SubmissionID  uuid.UUID
	IndicationReq IndicationOptionSpec
}

func (s *IndicationRunMessage) MarshalJSON() ([]byte, error) {
	return json.Marshal(*s)
}

func (s *IndicationRunMessage) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(1)
}

var _ jtypes.Message = (*IndicationRunMessage)(nil)

func IndicationRunMessageUnmarshalFn(
	data []byte, m jtypes.MessageVersion,
) (*IndicationRunMessage, error) {
	var s IndicationRunMessage
	if m != 1 {
		return &s, errors.Newf("wrong message version")
	}
	if err := json.Unmarshal(data, &s); err != nil {
		return &s, err
	}
	return &s, nil
}

type SyncApplicationBDArgs struct {
	AgencyId    string
	ProgramType string
}

func (c *SyncApplicationBDArgs) MarshalJSON() ([]byte, error) {
	if c == nil {
		return []byte{}, errors.New("SyncApplicationBDArgs is nil")
	}
	return json.Marshal(*c)
}

func (c *SyncApplicationBDArgs) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(0)
}

func SyncApplicationBDUnmarshalFn(
	data []byte,
	_ jtypes.MessageVersion,
) (*SyncApplicationBDArgs, error) {
	var c SyncApplicationBDArgs
	if err := json.Unmarshal(data, &c); err != nil {
		return &c, err
	}
	return &c, nil
}

type SubmissionForUWWorkflowMessage struct {
	SubmissionID          uuid.UUID
	IndicationOptionSpecs []IndicationOptionSpec
}

func (s *SubmissionForUWWorkflowMessage) MarshalJSON() ([]byte, error) {
	return json.Marshal(*s)
}

func (s *SubmissionForUWWorkflowMessage) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(0)
}

var _ jtypes.Message = (*SubmissionForUWWorkflowMessage)(nil)

func SubmissionForUWWorkflowMessageUnmarshalFn(
	data []byte, m jtypes.MessageVersion,
) (*SubmissionForUWWorkflowMessage, error) {
	var s SubmissionForUWWorkflowMessage
	if m != 0 {
		return &s, errors.Newf("wrong message version")
	}
	if err := json.Unmarshal(data, &s); err != nil {
		return &s, err
	}
	return &s, nil
}

type SubmissionForUWRunMessage struct {
	SubmissionID  uuid.UUID
	IndicationReq IndicationOptionSpec
}

func (s *SubmissionForUWRunMessage) MarshalJSON() ([]byte, error) {
	return json.Marshal(*s)
}

func (s *SubmissionForUWRunMessage) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(1)
}

var _ jtypes.Message = (*SubmissionForUWRunMessage)(nil)

func SubmissionForUWRunMessageUnmarshalFn(
	data []byte, m jtypes.MessageVersion,
) (*SubmissionForUWRunMessage, error) {
	var s SubmissionForUWRunMessage
	if m != 1 {
		return &s, errors.Newf("wrong message version")
	}
	if err := json.Unmarshal(data, &s); err != nil {
		return &s, err
	}
	return &s, nil
}

type UpdateSubmissionForUWWorkflowMessage struct {
	ReviewID     string
	SubmissionID uuid.UUID
	// If MVR problem for a driver exists then MVR reports will never be refetched unless this flag is true. If this flag
	// is true then also refetch will be done only if problem is not reviewed yet.
	ShouldRefetchMVRForUnreviewedProblems bool
	Reason                                UpdateSubmissionForUWTriggerReason
	IndicationOptionSpecs                 []IndicationOptionSpec
}

func (s *UpdateSubmissionForUWWorkflowMessage) MarshalJSON() ([]byte, error) {
	return json.Marshal(*s)
}

func (s *UpdateSubmissionForUWWorkflowMessage) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(0)
}

var _ jtypes.Message = (*UpdateSubmissionForUWWorkflowMessage)(nil)

func UpdateSubmissionForUWWorkflowMessageUnmarshalFn(
	data []byte, m jtypes.MessageVersion,
) (*UpdateSubmissionForUWWorkflowMessage, error) {
	var s UpdateSubmissionForUWWorkflowMessage
	if m != 0 {
		return &s, errors.Newf("wrong message version")
	}
	if err := json.Unmarshal(data, &s); err != nil {
		return &s, err
	}
	// for backward compatibility of messages created before `Reason` was added in args
	if s.Reason == InvalidTriggerReason {
		s.Reason = QuoteRefresh
	}
	return &s, nil
}

type UpdateSubmissionForUWRunMessage struct {
	ReviewID     string
	SubmissionID uuid.UUID
	// If MVR problem for a driver exists then MVR reports will never be refetched unless this flag is true. If this flag
	// is true then also refetch will be done only if problem is not reviewed yet.
	ShouldRefetchMVRForUnreviewedProblems bool
	Reason                                UpdateSubmissionForUWTriggerReason
	IndicationReq                         IndicationOptionSpec
}

func (s *UpdateSubmissionForUWRunMessage) MarshalJSON() ([]byte, error) {
	return json.Marshal(*s)
}

func (s *UpdateSubmissionForUWRunMessage) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(1)
}

var _ jtypes.Message = (*UpdateSubmissionForUWRunMessage)(nil)

func UpdateSubmissionForUWRunMessageUnmarshalFn(
	data []byte, m jtypes.MessageVersion,
) (*UpdateSubmissionForUWRunMessage, error) {
	var s UpdateSubmissionForUWRunMessage
	if m != 1 {
		return &s, errors.Newf("wrong message version")
	}
	if err := json.Unmarshal(data, &s); err != nil {
		return &s, err
	}
	// for backward compatibility of messages created before `Reason` was added in args
	if s.Reason == InvalidTriggerReason {
		s.Reason = QuoteRefresh
	}
	return &s, nil
}

type GenerateQuoteWorkflowMessage struct {
	SubmissionID          string
	AppReviewID           string
	IndicationOptionSpecs []IndicationOptionSpec
}

func (s *GenerateQuoteWorkflowMessage) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(0)
}

func (s *GenerateQuoteWorkflowMessage) MarshalJSON() ([]byte, error) {
	return json.Marshal(*s)
}

var _ jtypes.Message = &GenerateQuoteWorkflowMessage{}

func GenerateQuoteWorkflowMessageUnmarshalFn(
	data []byte, m jtypes.MessageVersion,
) (*GenerateQuoteWorkflowMessage, error) {
	var s GenerateQuoteWorkflowMessage
	if m != 0 {
		return &s, errors.Newf("wrong message version")
	}
	if err := json.Unmarshal(data, &s); err != nil {
		return &s, err
	}
	return &s, nil
}

type GenerateQuoteRunMessage struct {
	SubmissionID  string
	AppReviewID   string
	IndicationReq IndicationOptionSpec
}

func (s GenerateQuoteRunMessage) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(1)
}

func (s GenerateQuoteRunMessage) MarshalJSON() ([]byte, error) {
	type alias GenerateQuoteRunMessage
	return json.Marshal(alias(s))
}

var _ jtypes.Message = &GenerateQuoteRunMessage{}

func GenerateQuoteRunMessageUnmarshalFn(
	data []byte, m jtypes.MessageVersion,
) (*GenerateQuoteRunMessage, error) {
	var s GenerateQuoteRunMessage
	if m != 1 {
		return &s, errors.Newf("wrong message version")
	}
	if err := json.Unmarshal(data, &s); err != nil {
		return &s, err
	}
	return &s, nil
}

type AssignUnderwriterMessage struct {
	ApplicationID            uuid.UUID
	OverrideUnderwriterEmail *string
}

func (s *AssignUnderwriterMessage) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(0)
}

func (s *AssignUnderwriterMessage) MarshalJSON() ([]byte, error) {
	return json.Marshal(*s)
}

var _ jtypes.Message = &AssignUnderwriterMessage{}

func AssignUnderwriterMessageUnmarshalFn(
	data []byte, m jtypes.MessageVersion,
) (*AssignUnderwriterMessage, error) {
	var s AssignUnderwriterMessage
	if m != 0 {
		return &s, errors.Newf("wrong message version")
	}
	if err := json.Unmarshal(data, &s); err != nil {
		return &s, err
	}
	return &s, nil
}

type AutoSelectSafetyScoreMessage struct {
	ApplicationID uuid.UUID
	ProgramType   policy_enums.ProgramType
}

func (s *AutoSelectSafetyScoreMessage) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(0)
}

func (s *AutoSelectSafetyScoreMessage) MarshalJSON() ([]byte, error) {
	return json.Marshal(*s)
}

var _ jtypes.Message = &AutoSelectSafetyScoreMessage{}

func AutoSelectSafetyScoreMessageUnmarshalFn(
	data []byte, m jtypes.MessageVersion,
) (*AutoSelectSafetyScoreMessage, error) {
	var s AutoSelectSafetyScoreMessage
	if m != 0 {
		return &s, errors.Newf("wrong message version")
	}
	if err := json.Unmarshal(data, &s); err != nil {
		return &s, err
	}
	return &s, nil
}

type ExpressLaneAutoUnderwritingMessage struct {
	ApplicationID uuid.UUID `json:"applicationId"`
}

// Version implements jtypes.Message.
func (m *ExpressLaneAutoUnderwritingMessage) Version() jtypes.MessageVersion {
	return jtypes.MessageVersion(0)
}

// MarshalJSON implements jtypes.Message.
func (m *ExpressLaneAutoUnderwritingMessage) MarshalJSON() ([]byte, error) {
	return json.Marshal(*m)
}

var _ jtypes.Message = &ExpressLaneAutoUnderwritingMessage{}

func ExpressLaneAutoUnderwritingMessageUnmarshalFn(
	data []byte, m jtypes.MessageVersion,
) (*ExpressLaneAutoUnderwritingMessage, error) {
	var s ExpressLaneAutoUnderwritingMessage
	if m != 0 {
		return &s, errors.Newf("wrong message version")
	}
	if err := json.Unmarshal(data, &s); err != nil {
		return &s, err
	}
	return &s, nil
}
