package impl

import (
	"context"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/external/pibit"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/jobber/job_utils"
	"nirvanatech.com/nirvana/jobber/jtypes"
	"nirvanatech.com/nirvana/parsed_loss_runs/aggregation"
	"nirvanatech.com/nirvana/quoting/jobs"
	"nirvanatech.com/nirvana/underwriting/app_review/widgets/lossesv2"
)

const (
	NewAggregateLossRunsID             = "NewAggregateLossRuns"
	aggregateLossRunsJobFailureCounter = "aggregate_loss_runs_job.failure.count"
)

type aggregateLossRuns struct {
	job_utils.DefaultRetryable[*jobs.AggregateLossRunsArgs]
	job_utils.NoopUndoTask[*jobs.AggregateLossRunsArgs]
	deps *Deps
}

func NewAggregateLossRuns(deps *Deps) (*jtypes.Job[*jobs.AggregateLossRunsArgs], error) {
	return jtypes.NewJob(
		jobs.AggregateLossRuns,
		[]jtypes.TaskCreator[*jobs.AggregateLossRunsArgs]{
			func() jtypes.Task[*jobs.AggregateLossRunsArgs] {
				return &aggregateLossRuns{deps: deps}
			},
		},
		jobs.AggregateLossRunsUnmarshalFn,
	)
}

func (a *aggregateLossRuns) ID() string {
	return NewAggregateLossRunsID
}

func (a *aggregateLossRuns) Retry(ctx jtypes.Context, message *jobs.AggregateLossRunsArgs) error {
	return a.Run(ctx, message)
}

func (a *aggregateLossRuns) Run(ctx jtypes.Context, message *jobs.AggregateLossRunsArgs) error {
	if err := a.run(ctx, message); err != nil {
		log.Error(
			ctx,
			"AggregateLossRuns job run failed",
			log.Any("aggregateLossRunsArgs", message),
			log.Err(err),
		)
		metricErr := a.deps.MetricsClient.Inc(aggregateLossRunsJobFailureCounter, 1, 1)
		if metricErr != nil {
			return errors.Wrapf(metricErr, "failed to emit metric %s", aggregateLossRunsJobFailureCounter)
		}
		return errors.Wrapf(err, "aggregate loss runs job failed for application id %s", message.ApplicationId)
	}
	return nil
}

func (a *aggregateLossRuns) run(ctx jtypes.Context,
	message *jobs.AggregateLossRunsArgs,
) error {
	if message.ApplicationId == "" {
		return errors.New("application id is empty")
	}
	// 1. get all documents for application
	documents, err := aggregation.GetAllDocuments(ctx, message.ApplicationId, a.deps.AppWrapper, a.deps.ParsedLossRunWrapper)
	if err != nil {
		return errors.Wrapf(err, "error getting documents for applicationId %s", message.ApplicationId)
	}
	log.Info(ctx, "fetched documents", log.Int("count", len(documents)))

	// 2. fetch all data for these docs
	parsedData, err := aggregation.GetParsedLossRunData(ctx, documents, a.deps.ParsedLossRunWrapper)
	if err != nil {
		return errors.Wrapf(err, "error getting parsed data for documents %v", documents)
	}

	// 3. get flattened loss lines (with policy and claim)
	lossLinesWithPoliciesAndClaims, err := aggregation.GetLossLinesWithPolicyAndClaimData(*parsedData)
	if err != nil {
		return errors.Wrapf(err, "error flattening parsed data")
	}
	log.Info(ctx, "fetched loss lines", log.Int("count", len(lossLinesWithPoliciesAndClaims)))

	// 4. dedup
	dedupResult, err := aggregation.DeduplicateLossLines(lossLinesWithPoliciesAndClaims)
	if err != nil {
		return errors.Wrapf(err, "error deduplicating loss lines for applicationId %s", message.ApplicationId)
	}
	log.Info(ctx, "deduplicated loss lines", log.Int("selectedCount", len(dedupResult.SelectedLosses)), log.Int("duplicateCount", len(dedupResult.Duplicates)))

	// 5. figure out aggregation periods
	appReview, err := a.deps.AppReviewWrapper.GetLatestPendingReview(ctx, message.ApplicationId)
	if err != nil {
		return errors.Wrapf(err, "error getting latest pending app review")
	}
	aggregationPeriods, err := aggregation.FetchAggregationPeriods(appReview.Submission.LossInfo.LossRunSummary)
	if err != nil {
		return errors.Wrapf(err, "fetch aggregation periods failed for application %s", message.ApplicationId)
	}
	log.Info(ctx, "aggregationPeriods fetched", log.Any("periods", aggregationPeriods))

	// 6. find missing periods
	aggregationPeriodsWithGaps, err := aggregation.AddGapsToCoveragePeriods(ctx, *dedupResult, aggregationPeriods)
	if err != nil {
		return errors.Wrapf(err, "error finding gaps in periods for applicationId %s", message.ApplicationId)
	}
	log.Info(ctx, "periodsWithGaps computed", log.Any("periods", aggregationPeriodsWithGaps))

	// 7. assign losses
	aggregationId := uuid.New()
	processedLosses := aggregation.BuildProcessedLosses(aggregationId, dedupResult.SelectedLosses, aggregationPeriods)
	log.Info(ctx, "processed losses", log.Int("count", len(processedLosses)))

	// 8. summarize
	aggregationSummary, err := aggregation.GenerateAggregationSummary(ctx, processedLosses, aggregationPeriodsWithGaps, appReview.EffectiveDate)
	if err != nil {
		return errors.Wrapf(err, "error generating aggregation summary for applicationId %s", message.ApplicationId)
	}
	aggregationConfidence, err := aggregation.ComputeAggregationConfidence(processedLosses, aggregationPeriodsWithGaps)
	if err != nil {
		return errors.Wrapf(err, "error computing aggregation confidence for applicationId %s", message.ApplicationId)
	}
	aggregationObject := pibit.Aggregation{
		ID:                  aggregationId.String(),
		ApplicationID:       message.ApplicationId,
		ApplicationReviewID: appReview.Id,
		Status:              pibit.AggregationStatusActive,
		ConfidenceInfo:      *aggregationConfidence,
		CreatedAt:           time.Now(),
		UpdatedAt:           nil,
		Summary:             aggregationSummary,
	}

	// 9. recompute overrides snapshot for app review loss summary
	// todo enable with experiment flag later
	shouldRecomputeOverrides := false
	var recomputedOverrides []*application.LossRunSummaryPerCoverage
	if shouldRecomputeOverrides {
		recomputedOverrides, err = lossesv2.RecomputeOverrides(*appReview, aggregationObject)
		if err != nil {
			return errors.Wrapf(err, "failed to recompute overrides for app review %s", appReview.Id)
		}
	}

	// 9. persist
	err = a.deactivateOldAggregations(ctx, message.ApplicationId)
	if err != nil {
		return errors.Wrapf(err, "failed to deactivate old aggregations")
	}
	err = a.persistNewAggregation(ctx, aggregationObject)
	if err != nil {
		return errors.Wrapf(err, "failed to persist new aggregation")
	}
	err = a.persistProcessedLosses(ctx, processedLosses)
	if err != nil {
		return errors.Wrapf(err, "failed to persist processed losses")
	}
	if shouldRecomputeOverrides {
		err = a.persistLossSummaryOverrides(ctx, appReview.Id, recomputedOverrides)
		if err != nil {
			return errors.Wrapf(err, "failed to persist recomputed overrides for app review %s", appReview.Id)
		}
	}
	return nil
}

func (a *aggregateLossRuns) persistProcessedLosses(ctx context.Context, processedLosses []pibit.ProcessedLoss) error {
	for _, processedLoss := range processedLosses {
		err := a.deps.ParsedLossRunWrapper.InsertProcessedLoss(ctx, processedLoss)
		if err != nil {
			return errors.Wrapf(err, "failed to persist processed loss for id %s", processedLoss.ID)
		}
	}
	return nil
}

func (a *aggregateLossRuns) persistLossSummaryOverrides(ctx context.Context, reviewId string, overrides []*application.LossRunSummaryPerCoverage) error {
	err := a.deps.AppReviewWrapper.UpdateAppReview(
		ctx,
		reviewId,
		func(reviewToUpdate uw.ApplicationReview) (uw.ApplicationReview, error) {
			reviewToUpdate.Overrides.LossSummary = pointer_utils.ToPointer(overrides)
			return reviewToUpdate, nil
		},
	)
	if err != nil {
		return errors.Wrapf(err, "failed to save recomputed overrides for app review %s", reviewId)
	}
	return nil
}

func (a *aggregateLossRuns) deactivateOldAggregations(ctx context.Context, applicationId string) error {
	activeAggregations, err := a.deps.ParsedLossRunWrapper.GetAggregationsByApplicationAndStatus(ctx, applicationId, pibit.AggregationStatusActive)
	if err != nil {
		return errors.Wrapf(err, "unable to fetch active aggregations for applicationId %s", applicationId)
	}
	if len(activeAggregations) > 1 {
		return errors.Newf("more than one active aggregation found for applicationId %s", applicationId)
	}
	for _, aggn := range activeAggregations {
		aggn.Status = pibit.AggregationStatusStale
		aggn.UpdatedAt = pointer_utils.ToPointer(time.Now())
		err = a.deps.ParsedLossRunWrapper.UpdateAggregation(ctx, aggn)
		if err != nil {
			return errors.Wrapf(err, "unable to update aggregation for id %s", aggn.ID)
		}
	}
	return nil
}

func (a *aggregateLossRuns) persistNewAggregation(
	ctx context.Context,
	newAggregation pibit.Aggregation,
) error {
	err := a.deps.ParsedLossRunWrapper.InsertAggregation(ctx, newAggregation)
	if err != nil {
		return errors.Wrapf(err, "failed to persist new aggregation")
	}
	return nil
}
