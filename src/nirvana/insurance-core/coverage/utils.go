package coverage

import (
	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/slice_utils"
	appenums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
)

func GetDescriptionForCoverage(coverageID string) *string {
	description := ""
	switch coverageID {
	case "Auto Liability":
		description = "Covers injuries/damage caused to others during accidents"
	case "Motor Truck Cargo":
		description = "Protects cargo hauled damaged or lost during transit"
	case "Auto Physical Damage":
		description = "Repair/replace your damaged truck"
	case "General Liability":
		description = "Covers injuries/damage caused to others unrelated to accidents"
	default:
		return nil
	}

	return &description
}

// GetPrimaryCoverageFromSubCoverages is the reverse mapping of GetSubCoverageFromPrimaryCoverage
// It accepts a list of subCovIds and returns the corresponding primary coverage if the provided
// sub-coverages exactly match a primary coverage group (no more, no less).
func GetPrimaryCoverageFromSubCoverages(subCovIds []appenums.Coverage) (appenums.Coverage, error) {
	if len(subCovIds) == 0 {
		return appenums.Coverage(-1), errors.New("subCovIds list cannot be empty")
	}

	// Convert subCovIds to a set for efficient comparison
	subCovSet := make(map[appenums.Coverage]bool)
	for _, subCov := range subCovIds {
		if !subCovSet[subCov] {
			subCovSet[subCov] = true
		} else {
			return appenums.Coverage(-1), errors.New("subCovIds list contains duplicate coverage types")
		}
	}

	// Define primary coverage groups with their subcoverages
	coverageGroups := map[appenums.Coverage]map[appenums.Coverage]bool{
		appenums.CoverageAutoLiability: {
			appenums.CoverageBodilyInjury:   true,
			appenums.CoveragePropertyDamage: true,
		},
		appenums.CoverageAutoPhysicalDamage: {
			appenums.CoverageCollision:     true,
			appenums.CoverageComprehensive: true,
		},
		appenums.CoverageMotorTruckCargo: {
			appenums.CoverageMotorTruckCargo: true,
		},
		appenums.CoverageGeneralLiability: {
			appenums.CoverageGeneralLiability: true,
		},
	}

	// Find the primary coverage group that exactly matches the provided subcoverages
	for primaryCoverage, expectedSubCoverages := range coverageGroups {
		if mapsEqual(subCovSet, expectedSubCoverages) {
			return primaryCoverage, nil
		}
	}

	// If we reach here, the list contains mixed coverage types or invalid coverages
	return appenums.Coverage(-1), errors.New("subCovIds list contains mixed or invalid coverage types")
}

// mapsEqual checks if two maps with boolean values have exactly the same keys
func mapsEqual(map1, map2 map[appenums.Coverage]bool) bool {
	if len(map1) != len(map2) {
		return false
	}

	for key := range map1 {
		if !map2[key] {
			return false
		}
	}

	return true
}

func GetSubCoverageFromPrimaryCoverage(cov appenums.Coverage) []appenums.Coverage {
	subCovIds := make([]appenums.Coverage, 0)
	// nolint:exhaustive
	switch cov {
	case appenums.CoverageAutoLiability:
		// BI and PD are a part of AL and are not separately represented as an ancillary coverage today
		subCovIds = []appenums.Coverage{
			appenums.CoverageBodilyInjury,
			appenums.CoveragePropertyDamage,
		}
	case appenums.CoverageAutoPhysicalDamage:
		// Coll and Comp are a part of APD and are not separately represented as an ancillary coverage today
		subCovIds = []appenums.Coverage{
			appenums.CoverageCollision,
			appenums.CoverageComprehensive,
		}
	case appenums.CoverageMotorTruckCargo:
		// Currently the primary coverage is reused to be in harmony with pricing, update exact sub coverages later.
		subCovIds = []appenums.Coverage{appenums.CoverageMotorTruckCargo}
	case appenums.CoverageGeneralLiability:
		// Currently the primary coverage is reused to be in harmony with pricing, update exact sub coverages later.
		subCovIds = []appenums.Coverage{appenums.CoverageGeneralLiability}
	default:
		// No sub coverages for default case
	}
	return subCovIds
}

// IsAppCoverageAPolicyModifier returns true if the coverage is a policy modifier, as in currently mapped as an
// ancillary coverage but is actually applicable across all coverages of a policy.
func IsAppCoverageAPolicyModifier(cov appenums.Coverage) bool {
	return cov == appenums.CoverageBlanketWaiverOfSubrogation ||
		cov == appenums.CoverageBlanketAdditional
}

// GetSubCoverageStringFromPrimaryCoverage returns the same list as GetSubCoverageFromPrimaryCoverage but as strings.
func GetSubCoverageStringFromPrimaryCoverage(cov appenums.Coverage) []string {
	return slice_utils.ToString(GetSubCoverageFromPrimaryCoverage(cov))
}
