package coverage

import (
	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	appenums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

func GetAppCoverageFromPricingSubCoverage(subCovType ptypes.SubCoverageType) (*appenums.Coverage, error) {
	var appCov appenums.Coverage
	// nolint:exhaustive
	switch subCovType {
	case ptypes.SubCoverageType_SubCoverageType_Comprehensive:
		appCov = appenums.CoverageComprehensive
	case ptypes.SubCoverageType_SubCoverageType_Collision:
		appCov = appenums.CoverageCollision
	case ptypes.SubCoverageType_SubCoverageType_TrailerInterchange:
		appCov = appenums.CoverageTrailerInterchange
	case ptypes.SubCoverageType_SubCoverageType_NonOwnedTrailer:
		appCov = appenums.CoverageNonOwnedTrailer
	case ptypes.SubCoverageType_SubCoverageType_Towing:
		appCov = appenums.CoverageTowingLaborAndStorage
	case ptypes.SubCoverageType_SubCoverageType_RentalReimbursement:
		appCov = appenums.CoverageRentalReimbursement
	case ptypes.SubCoverageType_SubCoverageType_Cargo:
		appCov = appenums.CoverageMotorTruckCargo
	case ptypes.SubCoverageType_SubCoverageType_ReeferWithoutHumanError:
		appCov = appenums.CoverageReefer
	case ptypes.SubCoverageType_SubCoverageType_ReeferWithHumanError:
		appCov = appenums.CoverageReeferWithHumanError
	case ptypes.SubCoverageType_SubCoverageType_BodilyInjury:
		appCov = appenums.CoverageBodilyInjury
	case ptypes.SubCoverageType_SubCoverageType_PropertyDamage:
		appCov = appenums.CoveragePropertyDamage
	case ptypes.SubCoverageType_SubCoverageType_UninsuredMotorist:
		appCov = appenums.CoverageUM
	case ptypes.SubCoverageType_SubCoverageType_UnderInsuredMotorist:
		appCov = appenums.CoverageUIM
	case ptypes.SubCoverageType_SubCoverageType_UMUIM:
		appCov = appenums.CoverageUMUIM
	case ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage:
		appCov = appenums.CoverageUninsuredMotoristPropertyDamage
	case ptypes.SubCoverageType_SubCoverageType_MedicalPayments:
		appCov = appenums.CoverageMedicalPayments
	case ptypes.SubCoverageType_SubCoverageType_PersonalInjuryProtection:
		appCov = appenums.CoveragePersonalInjuryProtection
	case ptypes.SubCoverageType_SubCoverageType_PIPAttendantCare:
		appCov = appenums.CoveragePIPExcessAttendantCare
	case ptypes.SubCoverageType_SubCoverageType_PIPWorkLossAndRPLService: // Limit and Deductible are not separate here.
		appCov = appenums.CoveragePIPWorkLossAndRPLService
	case ptypes.SubCoverageType_SubCoverageType_PropertyProtectionInsurance:
		appCov = appenums.CoveragePropertyProtectionInsurance
	case ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury:
		appCov = appenums.CoverageUninsuredMotoristBodilyInjury
	case ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury:
		appCov = appenums.CoverageUnderinsuredMotoristBodilyInjury
	case ptypes.SubCoverageType_SubCoverageType_HiredAutoLiability:
		appCov = appenums.CoverageHiredAuto
	case ptypes.SubCoverageType_SubCoverageType_NonOwnedVehicle:
		appCov = appenums.CoverageNonOwnedAuto
	case ptypes.SubCoverageType_SubCoverageType_GeneralLiability:
		appCov = appenums.CoverageGeneralLiability
	case ptypes.SubCoverageType_SubCoverageType_HiredAuto:
		appCov = appenums.CoverageHiredAuto
	case ptypes.SubCoverageType_SubCoverageType_MedicalExpenseBenefits:
		appCov = appenums.CoverageMedicalExpenseBenefits
	case ptypes.SubCoverageType_SubCoverageType_WorkLossBenefits:
		appCov = appenums.CoverageWorkLossBenefits
	case ptypes.SubCoverageType_SubCoverageType_AccidentalDeathBenefits:
		appCov = appenums.CoverageAccidentalDeathBenefits
	case ptypes.SubCoverageType_SubCoverageType_ExtraordinaryMedicalBenefits:
		appCov = appenums.CoverageExtraordinaryMedicalBenefits
	case ptypes.SubCoverageType_SubCoverageType_FuneralExpenseBenefits:
		appCov = appenums.CoverageFuneralExpenseBenefits
	case ptypes.SubCoverageType_SubCoverageType_EssentialServiceExpenses:
		appCov = appenums.CoverageEssentialServiceExpenses
	case ptypes.SubCoverageType_SubCoverageType_HiredAutoPhysicalDamage:
		appCov = appenums.CoverageHiredAutoPD
	case ptypes.SubCoverageType_SubCoverageType_Unspecified:
		return nil, errors.Newf("unsupported sub-coverage type: %s", subCovType.String())
	default:
		return nil, errors.Newf("unsupported sub-coverage type: %s", subCovType.String())
	}
	return pointer_utils.ToPointer(appCov), nil
}

func GetPricingSubCoverageFromAppCoverage(coverageType appenums.Coverage) (*ptypes.SubCoverageType, error) {
	var pSubCov ptypes.SubCoverageType
	//nolint:exhaustive
	switch coverageType {
	case appenums.CoverageComprehensive:
		pSubCov = ptypes.SubCoverageType_SubCoverageType_Comprehensive
	case appenums.CoverageCollision:
		pSubCov = ptypes.SubCoverageType_SubCoverageType_Collision
	case appenums.CoverageTrailerInterchange:
		pSubCov = ptypes.SubCoverageType_SubCoverageType_TrailerInterchange
	case appenums.CoverageNonOwnedTrailer:
		pSubCov = ptypes.SubCoverageType_SubCoverageType_NonOwnedTrailer
	case appenums.CoverageTowingLaborAndStorage:
		pSubCov = ptypes.SubCoverageType_SubCoverageType_Towing
	case appenums.CoverageRentalReimbursement:
		pSubCov = ptypes.SubCoverageType_SubCoverageType_RentalReimbursement
	case appenums.CoverageMotorTruckCargo:
		pSubCov = ptypes.SubCoverageType_SubCoverageType_Cargo
	case appenums.CoverageReefer:
		pSubCov = ptypes.SubCoverageType_SubCoverageType_ReeferWithoutHumanError
	case appenums.CoverageReeferWithHumanError:
		pSubCov = ptypes.SubCoverageType_SubCoverageType_ReeferWithHumanError
	case appenums.CoverageBodilyInjury:
		pSubCov = ptypes.SubCoverageType_SubCoverageType_BodilyInjury
	case appenums.CoveragePropertyDamage:
		pSubCov = ptypes.SubCoverageType_SubCoverageType_PropertyDamage
	case appenums.CoverageUM:
		pSubCov = ptypes.SubCoverageType_SubCoverageType_UninsuredMotorist
	case appenums.CoverageUIM:
		pSubCov = ptypes.SubCoverageType_SubCoverageType_UnderInsuredMotorist
	case appenums.CoverageUMUIM:
		pSubCov = ptypes.SubCoverageType_SubCoverageType_UMUIM
	case appenums.CoverageUninsuredMotoristPropertyDamage:
		pSubCov = ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage
	case appenums.CoverageMedicalPayments:
		pSubCov = ptypes.SubCoverageType_SubCoverageType_MedicalPayments
	case appenums.CoveragePersonalInjuryProtection:
		pSubCov = ptypes.SubCoverageType_SubCoverageType_PersonalInjuryProtection
	case appenums.CoveragePIPExcessAttendantCare:
		pSubCov = ptypes.SubCoverageType_SubCoverageType_PIPAttendantCare
	case appenums.CoveragePIPWorkLossAndRPLService:
		pSubCov = ptypes.SubCoverageType_SubCoverageType_PIPWorkLossAndRPLService
	case appenums.CoveragePropertyProtectionInsurance:
		pSubCov = ptypes.SubCoverageType_SubCoverageType_PropertyProtectionInsurance
	case appenums.CoverageUninsuredMotoristBodilyInjury:
		pSubCov = ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury
	case appenums.CoverageUnderinsuredMotoristBodilyInjury:
		pSubCov = ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury
	case appenums.CoverageNonOwnedAuto:
		pSubCov = ptypes.SubCoverageType_SubCoverageType_NonOwnedVehicle
	case appenums.CoverageGeneralLiability:
		pSubCov = ptypes.SubCoverageType_SubCoverageType_GeneralLiability
	case appenums.CoverageHiredAuto:
		pSubCov = ptypes.SubCoverageType_SubCoverageType_HiredAuto
	default:
		return nil, errors.Newf("unsupported coverage type: %s", coverageType.String())
	}
	return pointer_utils.ToPointer(pSubCov), nil
}
