package pricing

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"nirvanatech.com/nirvana/business-auto/model"
	"nirvanatech.com/nirvana/common-go/us_states"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

func TestAddLimitAndDeductible(t *testing.T) {
	tests := []struct {
		name             string
		inputs           *coverageInputs
		limitAmount      *float64
		deductibleAmount *float64
		subCovs          []ptypes.SubCoverageType
		expectedLimits   int
		expectedDeds     int
		expectedSubCovs  int
	}{
		{
			name:             "empty subcovs does nothing",
			inputs:           &coverageInputs{},
			limitAmount:      floatPtr(100000),
			deductibleAmount: floatPtr(1000),
			subCovs:          []ptypes.SubCoverageType{},
			expectedLimits:   0,
			expectedDeds:     0,
			expectedSubCovs:  0,
		},
		{
			name:             "limit only",
			inputs:           &coverageInputs{},
			limitAmount:      floatPtr(100000),
			deductibleAmount: nil,
			subCovs:          []ptypes.SubCoverageType{ptypes.SubCoverageType_SubCoverageType_BodilyInjury},
			expectedLimits:   1,
			expectedDeds:     0,
			expectedSubCovs:  1,
		},
		{
			name:             "deductible only",
			inputs:           &coverageInputs{},
			limitAmount:      nil,
			deductibleAmount: floatPtr(1000),
			subCovs:          []ptypes.SubCoverageType{ptypes.SubCoverageType_SubCoverageType_Comprehensive},
			expectedLimits:   0,
			expectedDeds:     1,
			expectedSubCovs:  1,
		},
		{
			name:             "both limit and deductible",
			inputs:           &coverageInputs{},
			limitAmount:      floatPtr(100000),
			deductibleAmount: floatPtr(1000),
			subCovs: []ptypes.SubCoverageType{
				ptypes.SubCoverageType_SubCoverageType_Collision,
				ptypes.SubCoverageType_SubCoverageType_Comprehensive,
			},
			expectedLimits:  1,
			expectedDeds:    1,
			expectedSubCovs: 2,
		},
		{
			name: "appends to existing",
			inputs: &coverageInputs{
				SubCoverages: []ptypes.SubCoverageType{ptypes.SubCoverageType_SubCoverageType_BodilyInjury},
				LimitSpecs:   []*ptypes.LimitSpec{{}},
			},
			limitAmount:      floatPtr(50000),
			deductibleAmount: nil,
			subCovs:          []ptypes.SubCoverageType{ptypes.SubCoverageType_SubCoverageType_PropertyDamage},
			expectedLimits:   2,
			expectedDeds:     0,
			expectedSubCovs:  2,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			addLimitAndDeductible(tt.inputs, tt.limitAmount, tt.deductibleAmount, tt.subCovs, nil)

			assert.Len(t, tt.inputs.LimitSpecs, tt.expectedLimits)
			assert.Len(t, tt.inputs.DeductibleSpecs, tt.expectedDeds)
			assert.Len(t, tt.inputs.SubCoverages, tt.expectedSubCovs)

			// Verify the specs have the right subcoverage groups
			if tt.limitAmount != nil && len(tt.subCovs) > 0 {
				lastLimit := tt.inputs.LimitSpecs[len(tt.inputs.LimitSpecs)-1]
				assert.Equal(t, *tt.limitAmount, lastLimit.Amount)
				assert.Equal(t, tt.subCovs, lastLimit.SubCoverageGroup.SubCoverages)
			}

			if tt.deductibleAmount != nil && len(tt.subCovs) > 0 {
				lastDed := tt.inputs.DeductibleSpecs[len(tt.inputs.DeductibleSpecs)-1]
				assert.Equal(t, *tt.deductibleAmount, lastDed.Amount)
				assert.Equal(t, tt.subCovs, lastDed.SubCoverageGroup.SubCoverages)
			}
		})
	}
}

func TestDeduplicateSubCoverages(t *testing.T) {
	tests := []struct {
		name     string
		input    []ptypes.SubCoverageType
		expected []ptypes.SubCoverageType
	}{
		{
			name:     "empty slice",
			input:    []ptypes.SubCoverageType{},
			expected: []ptypes.SubCoverageType{},
		},
		{
			name:     "no duplicates",
			input:    []ptypes.SubCoverageType{ptypes.SubCoverageType_SubCoverageType_BodilyInjury, ptypes.SubCoverageType_SubCoverageType_PropertyDamage},
			expected: []ptypes.SubCoverageType{ptypes.SubCoverageType_SubCoverageType_BodilyInjury, ptypes.SubCoverageType_SubCoverageType_PropertyDamage},
		},
		{
			name: "with duplicates",
			input: []ptypes.SubCoverageType{
				ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
				ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_Comprehensive,
				ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
			},
			expected: []ptypes.SubCoverageType{
				ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
				ptypes.SubCoverageType_SubCoverageType_Comprehensive,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := deduplicateSubCoverages(tt.input)
			assert.ElementsMatch(t, tt.expected, result)
		})
	}
}

// Helper function to create float64 pointers
func floatPtr(f float64) *float64 {
	return &f
}

// Verifies that parent-only PIP limit stored in DB expands to child sub-coverages at pricing time
func TestPricingExpansion_PIP_TX(t *testing.T) {
	app := &model.BusinessAutoApp{
		CompanyInfo: model.CompanyInfo{USState: us_states.TX},
		CoveragesInfo: &model.CoveragesInfo{
			PrimaryCoverages: []model.PrimaryCoverage{{
				ID:             app_enums.CoverageAutoLiability,
				SubCoverageIDs: []app_enums.Coverage{app_enums.CoveragePersonalInjuryProtection},
			}},
			Limits: []model.Limit{{
				SubCoverageIDs: []app_enums.Coverage{app_enums.CoveragePersonalInjuryProtection},
				Amount:         10000,
			}},
		},
	}

	inputs := buildLimitAndDeductibleSpecsForPolicyAndCoverages(app, map[app_enums.Coverage]bool{
		app_enums.CoveragePersonalInjuryProtection: true,
	})

	assert.NotNil(t, inputs)
	if assert.Len(t, inputs.LimitSpecs, 1) {
		assert.Equal(t, float64(10000), inputs.LimitSpecs[0].Amount)
		// Expect expansion into 4 child sub-coverages
		assert.Len(t, inputs.LimitSpecs[0].SubCoverageGroup.SubCoverages, 4)
	}
	// SubCoverages list should include the same 4 children (deduped)
	assert.GreaterOrEqual(t, len(inputs.SubCoverages), 4)
}

// Verifies that parent-only UMUIM limit stored in DB expands at pricing time (TX)
func TestPricingExpansion_UMUIM_TX(t *testing.T) {
	app := &model.BusinessAutoApp{
		CompanyInfo: model.CompanyInfo{USState: us_states.TX},
		CoveragesInfo: &model.CoveragesInfo{
			PrimaryCoverages: []model.PrimaryCoverage{{
				ID:             app_enums.CoverageAutoLiability,
				SubCoverageIDs: []app_enums.Coverage{app_enums.CoverageUMUIM},
			}},
			Limits: []model.Limit{{
				SubCoverageIDs: []app_enums.Coverage{app_enums.CoverageUMUIM},
				Amount:         300000,
			}},
		},
	}

	inputs := buildLimitAndDeductibleSpecsForPolicyAndCoverages(app, map[app_enums.Coverage]bool{
		app_enums.CoverageUMUIM: true,
	})

	assert.NotNil(t, inputs)
	if assert.Len(t, inputs.LimitSpecs, 1) {
		assert.Equal(t, float64(300000), inputs.LimitSpecs[0].Amount)
		assert.Len(t, inputs.LimitSpecs[0].SubCoverageGroup.SubCoverages, 4)
	}
}

// Verifies GA behavior for UM/UIM when using AddedTo parent coverage:
// - Parent limit expands to 4 sub-coverages
// - Limit.AddedOn is set to true
func TestPricingExpansion_UMUIM_GA_AddedTo_SetsLimitAddedOnTrueAndExpands(t *testing.T) {
	app := &model.BusinessAutoApp{
		CompanyInfo: model.CompanyInfo{USState: us_states.GA},
		CoveragesInfo: &model.CoveragesInfo{
			PrimaryCoverages: []model.PrimaryCoverage{{
				ID:             app_enums.CoverageAutoLiability,
				SubCoverageIDs: []app_enums.Coverage{app_enums.CoverageUMUIMAddedTo},
			}},
			Limits: []model.Limit{{
				SubCoverageIDs: []app_enums.Coverage{app_enums.CoverageUMUIMAddedTo},
				Amount:         75000,
			}},
		},
	}

	inputs := buildLimitAndDeductibleSpecsForPolicyAndCoverages(app, map[app_enums.Coverage]bool{
		app_enums.CoverageUMUIMAddedTo: true,
	})

	assert.NotNil(t, inputs)

	// App data should be mutated by expansion step
	if assert.Len(t, app.CoveragesInfo.Limits, 1) {
		limit := app.CoveragesInfo.Limits[0]
		// Expect 4 children after expansion
		assert.Len(t, limit.SubCoverageIDs, 4)
		// Validate expected sub-coverages irrespective of order
		assert.ElementsMatch(t, []app_enums.Coverage{
			app_enums.CoverageUninsuredMotoristBodilyInjury,
			app_enums.CoverageUnderinsuredMotoristBodilyInjury,
			app_enums.CoverageUninsuredMotoristPropertyDamage,
			app_enums.CoverageUnderinsuredMotoristPropertyDamage,
		}, limit.SubCoverageIDs)
	}

	// Pricing inputs should have a single limit spec with the 4 sub-coverages and AddedOn=true
	if assert.Len(t, inputs.LimitSpecs, 1) {
		limitSpec := inputs.LimitSpecs[0]
		assert.Equal(t, float64(75000), limitSpec.Amount)
		assert.Len(t, limitSpec.SubCoverageGroup.SubCoverages, 4)
		assert.True(t, limitSpec.AddedOn, "AddedOn should be true for GA AddedTo")
	}
}

// Verifies GA behavior for UM/UIM when using ReducedBy parent coverage:
// - Parent limit expands to 4 sub-coverages
// - Limit.AddedOn is set to false
func TestPricingExpansion_UMUIM_GA_ReducedBy_SetsLimitAddedOnFalseAndExpands(t *testing.T) {
	app := &model.BusinessAutoApp{
		CompanyInfo: model.CompanyInfo{USState: us_states.GA},
		CoveragesInfo: &model.CoveragesInfo{
			PrimaryCoverages: []model.PrimaryCoverage{{
				ID:             app_enums.CoverageAutoLiability,
				SubCoverageIDs: []app_enums.Coverage{app_enums.CoverageUMUIMReducedBy},
			}},
			Limits: []model.Limit{{
				SubCoverageIDs: []app_enums.Coverage{app_enums.CoverageUMUIMReducedBy},
				Amount:         50000,
			}},
		},
	}

	inputs := buildLimitAndDeductibleSpecsForPolicyAndCoverages(app, map[app_enums.Coverage]bool{
		app_enums.CoverageUMUIMReducedBy: true,
	})

	assert.NotNil(t, inputs)

	if assert.Len(t, app.CoveragesInfo.Limits, 1) {
		limit := app.CoveragesInfo.Limits[0]
		assert.Len(t, limit.SubCoverageIDs, 4)
		assert.ElementsMatch(t, []app_enums.Coverage{
			app_enums.CoverageUninsuredMotoristBodilyInjury,
			app_enums.CoverageUnderinsuredMotoristBodilyInjury,
			app_enums.CoverageUninsuredMotoristPropertyDamage,
			app_enums.CoverageUnderinsuredMotoristPropertyDamage,
		}, limit.SubCoverageIDs)
	}

	if assert.Len(t, inputs.LimitSpecs, 1) {
		limitSpec := inputs.LimitSpecs[0]
		assert.Equal(t, float64(50000), limitSpec.Amount)
		assert.Len(t, limitSpec.SubCoverageGroup.SubCoverages, 4)
		assert.False(t, limitSpec.AddedOn, "AddedOn should be false for GA ReducedBy")
	}
}

// Comprehensive test based on real GA pricing service test data
// Verifies complete GA UMUIM expansion with AddedOn flag and deductibles
func TestPricingExpansion_UMUIM_GA_ComprehensiveRealScenario(t *testing.T) {
	app := &model.BusinessAutoApp{
		CompanyInfo: model.CompanyInfo{USState: us_states.GA},
		CoveragesInfo: &model.CoveragesInfo{
			PrimaryCoverages: []model.PrimaryCoverage{{
				ID:             app_enums.CoverageAutoLiability,
				SubCoverageIDs: []app_enums.Coverage{app_enums.CoverageUMUIMAddedTo},
			}},
			Limits: []model.Limit{{
				SubCoverageIDs: []app_enums.Coverage{app_enums.CoverageUMUIMAddedTo},
				Amount:         300000,
			}},
			Deductibles: []model.Deductible{{
				SubCoverageIDs: []app_enums.Coverage{app_enums.CoverageUMUIMAddedTo},
				Amount:         1000,
			}},
		},
	}

	inputs := buildLimitAndDeductibleSpecsForPolicyAndCoverages(app, map[app_enums.Coverage]bool{
		app_enums.CoverageUMUIMAddedTo: true,
	})

	assert.NotNil(t, inputs)

	// Verify limit expansion and AddedOn flag
	if assert.Len(t, app.CoveragesInfo.Limits, 1) {
		limit := app.CoveragesInfo.Limits[0]
		// Should expand to 4 sub-coverages
		assert.Len(t, limit.SubCoverageIDs, 4)
		assert.Contains(t, limit.SubCoverageIDs, app_enums.CoverageUninsuredMotoristBodilyInjury)
		assert.Contains(t, limit.SubCoverageIDs, app_enums.CoverageUnderinsuredMotoristBodilyInjury)
		assert.Contains(t, limit.SubCoverageIDs, app_enums.CoverageUninsuredMotoristPropertyDamage)
		assert.Contains(t, limit.SubCoverageIDs, app_enums.CoverageUnderinsuredMotoristPropertyDamage)
	}

	// Verify deductible expansion (no AddedOn flag)
	if assert.Len(t, app.CoveragesInfo.Deductibles, 1) {
		deductible := app.CoveragesInfo.Deductibles[0]
		// Should expand to 4 sub-coverages
		assert.Len(t, deductible.SubCoverageIDs, 4)
		assert.Contains(t, deductible.SubCoverageIDs, app_enums.CoverageUninsuredMotoristBodilyInjury)
		assert.Contains(t, deductible.SubCoverageIDs, app_enums.CoverageUnderinsuredMotoristBodilyInjury)
		assert.Contains(t, deductible.SubCoverageIDs, app_enums.CoverageUninsuredMotoristPropertyDamage)
		assert.Contains(t, deductible.SubCoverageIDs, app_enums.CoverageUnderinsuredMotoristPropertyDamage)
	}

	// Verify pricing inputs
	if assert.Len(t, inputs.LimitSpecs, 1) {
		limitSpec := inputs.LimitSpecs[0]
		assert.Equal(t, float64(300000), limitSpec.Amount)
		assert.True(t, limitSpec.AddedOn, "AddedOn should be true for GA CoverageUMUIMAddedTo")
		assert.Len(t, limitSpec.SubCoverageGroup.SubCoverages, 4)
		// Verify all 4 UMUIM sub-coverages are present in pricing
		subCovs := limitSpec.SubCoverageGroup.SubCoverages
		assert.Contains(t, subCovs, ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury)
		assert.Contains(t, subCovs, ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury)
		assert.Contains(t, subCovs, ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage)
		assert.Contains(t, subCovs, ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristPropertyDamage)
	}

	if assert.Len(t, inputs.DeductibleSpecs, 1) {
		deductibleSpec := inputs.DeductibleSpecs[0]
		assert.Equal(t, float64(1000), deductibleSpec.Amount)
		assert.Len(t, deductibleSpec.SubCoverageGroup.SubCoverages, 4)
		// Verify all 4 UMUIM sub-coverages are present in deductible pricing
		subCovs := deductibleSpec.SubCoverageGroup.SubCoverages
		assert.Contains(t, subCovs, ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury)
		assert.Contains(t, subCovs, ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury)
		assert.Contains(t, subCovs, ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage)
		assert.Contains(t, subCovs, ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristPropertyDamage)
	}

	// Verify total sub-coverages include the expanded UMUIM ones
	assert.GreaterOrEqual(t, len(inputs.SubCoverages), 4)
}
