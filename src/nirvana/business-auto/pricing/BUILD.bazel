load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "pricing",
    srcs = [
        "additional_modifiers.go",
        "artifact.go",
        "company.go",
        "coverage.go",
        "coverage_filter.go",
        "coverage_flags.go",
        "driver_class.go",
        "experience.go",
        "limit_deductible_helpers.go",
        "lossfree.go",
        "mod_utils.go",
        "request.go",
        "response.go",
        "schedule.go",
        "subcoverage_utils.go",
        "vehicles.go",
    ],
    importpath = "nirvanatech.com/nirvana/business-auto/pricing",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/business-auto/converters",
        "//nirvana/business-auto/coverage",
        "//nirvana/business-auto/model",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/proto",
        "//nirvana/common-go/struct_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/application/quoting",
        "//nirvana/db-api/db_wrappers/policy",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/policy/business_auto",
        "//nirvana/rating/artifacts",
        "//nirvana/rating/pricing/api/ptypes",
        "//nirvana/rating/pricing/api/ptypes/programs/business_auto",
        "@com_github_benbjohnson_clock//:clock",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_shopspring_decimal//:decimal",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "pricing_test",
    srcs = [
        "coverage_filter_test.go",
        "limit_deductible_helpers_test.go",
        "mod_utils_test.go",
        "request_e2e_test.go",
    ],
    embed = [":pricing"],
    deps = [
        "//nirvana/business-auto/model",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/infra/fx/testfixtures/biz_auto_app_fixture",
        "//nirvana/infra/fx/testloader",
        "//nirvana/rating/pricing/api",
        "//nirvana/rating/pricing/api/engine",
        "//nirvana/rating/pricing/api/metrics",
        "//nirvana/rating/pricing/api/ptypes",
        "@com_github_benbjohnson_clock//:clock",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//suite",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
    ],
)
