package pricing

import (
	"nirvanatech.com/nirvana/common-go/us_states"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
)

// GetCoverageAddedOnFlag returns the AddedOn flag value for a coverage in a given state.
// Returns:
//   - &true for GA CoverageUMUIMAddedTo
//   - &false for GA CoverageUMUIMReducedBy
//   - nil for all other cases (no AddedOn flag needed)
func GetCoverageAddedOnFlag(state us_states.USState, coverage app_enums.Coverage) *bool {
	switch state {
	case us_states.GA:
		switch coverage {
		case app_enums.CoverageUMUIMAddedTo:
			return &[]bool{true}[0] // AddedTo = AddedOn: true
		case app_enums.CoverageUMUIMReducedBy:
			return &[]bool{false}[0] // ReducedBy = AddedOn: false
		default:
			return nil // No AddedOn flag for other GA coverages
		}
	default:
		return nil // No AddedOn flag for other states
	}
}
