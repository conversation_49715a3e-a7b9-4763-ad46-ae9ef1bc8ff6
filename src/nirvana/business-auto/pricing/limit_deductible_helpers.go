package pricing

import (
	"nirvanatech.com/nirvana/business-auto/converters"
	"nirvanatech.com/nirvana/business-auto/coverage"
	"nirvanatech.com/nirvana/business-auto/model"
	"nirvanatech.com/nirvana/common-go/us_states"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

// addLimitAndDeductible adds a limit spec and/or deductible spec to the inputs
func addLimitAndDeductible(
	inputs *coverageInputs,
	limitAmount *float64,
	deductibleAmount *float64,
	subCovs []ptypes.SubCoverageType,
	addedOn *bool, // Optional AddedOn flag for ptypes.LimitSpec
) {
	if len(subCovs) == 0 {
		return
	}

	// Add subcoverages to the list (deduplicated later if needed)
	inputs.SubCoverages = append(inputs.SubCoverages, subCovs...)

	// Create LimitSpec if amount provided
	if limitAmount != nil {
		limitSpec := &ptypes.LimitSpec{
			Amount:  *limitAmount,
			Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
			SubCoverageGroup: &ptypes.SubCoverageGroup{
				SubCoverages: append([]ptypes.SubCoverageType(nil), subCovs...),
			},
		}

		// Apply AddedOn flag if provided
		if addedOn != nil {
			limitSpec.AddedOn = *addedOn
		}

		inputs.LimitSpecs = append(inputs.LimitSpecs, limitSpec)
	}

	// Create DeductibleSpec if amount provided
	if deductibleAmount != nil {
		deductibleSpec := &ptypes.DeductibleSpec{
			Amount: *deductibleAmount,
			SubCoverageGroup: &ptypes.SubCoverageGroup{
				SubCoverages: append([]ptypes.SubCoverageType(nil), subCovs...),
			},
		}
		inputs.DeductibleSpecs = append(inputs.DeductibleSpecs, deductibleSpec)
	}
}

// deduplicateSubCoverages removes duplicate SubCoverageType values while preserving order
func deduplicateSubCoverages(subCoverages []ptypes.SubCoverageType) []ptypes.SubCoverageType {
	seen := make(map[ptypes.SubCoverageType]bool)
	result := make([]ptypes.SubCoverageType, 0, len(subCoverages))

	for _, subCov := range subCoverages {
		if !seen[subCov] {
			seen[subCov] = true
			result = append(result, subCov)
		}
	}

	return result
}

// buildLimitAndDeductibleSpecs builds coverage inputs for either policy-level or vehicle-level
// by processing only the limits and deductibles for relevant sub-coverages
// - If specificVIN is nil, processes policy-level (VIN == null)
// - If specificVIN is provided, processes vehicle-level (VIN == specificVIN)
func buildLimitAndDeductibleSpecs(
	app *model.BusinessAutoApp,
	specificVIN *string,
	relevantSubCoverageIDs map[app_enums.Coverage]bool,
) *coverageInputs {
	result := &coverageInputs{
		SubCoverages:    make([]ptypes.SubCoverageType, 0),
		LimitSpecs:      make([]*ptypes.LimitSpec, 0),
		DeductibleSpecs: make([]*ptypes.DeductibleSpec, 0),
	}

	// Resolve state for expansion at pricing-time
	state := app.CompanyInfo.USState
	if app.ModelPinConfig != nil {
		state = app.ModelPinConfig.RateML.USState
	}

	// STEP 0: Expand app data structures and build relevancy map in single pass
	limitAddedOnFlags := expandAppDataAndBuildRelevancy(app, state, relevantSubCoverageIDs)

	// STEP 1: Process ALL limits first - now using expanded data
	for i, limit := range app.CoveragesInfo.Limits {
		// Apply VIN filter based on whether we're processing policy or vehicle level
		if specificVIN == nil {
			// Policy-level: only process limits with VIN == null
			if limit.VIN != nil {
				continue
			}
		} else {
			// Vehicle-level: only process limits with VIN == specificVIN
			if limit.VIN == nil || *limit.VIN != *specificVIN {
				continue
			}
		}

		// Process already-expanded coverage IDs with simple filtering
		var subCovTypes []ptypes.SubCoverageType
		for _, coverageID := range limit.SubCoverageIDs {
			if !relevantSubCoverageIDs[coverageID] {
				continue
			}
			subCovType, err := converters.ConvertCoverageToSubCoverageType(coverageID)
			if err != nil || subCovType == ptypes.SubCoverageType_SubCoverageType_Unspecified {
				continue
			}
			subCovTypes = append(subCovTypes, subCovType)
		}

		if len(subCovTypes) == 0 {
			continue
		}

		result.SubCoverages = append(result.SubCoverages, subCovTypes...)
		// Use the captured AddedOn flag for this limit
		addedOn := limitAddedOnFlags[i]
		addLimitAndDeductible(result, &limit.Amount, nil, subCovTypes, addedOn)
	}

	// STEP 2: Process ALL deductibles separately - now using expanded data
	for _, deductible := range app.CoveragesInfo.Deductibles {
		// Apply VIN filter based on whether we're processing policy or vehicle level
		if specificVIN == nil {
			// Policy-level: only process deductibles with VIN == null
			if deductible.VIN != nil {
				continue
			}
		} else {
			// Vehicle-level: only process deductibles with VIN == specificVIN
			if deductible.VIN == nil || *deductible.VIN != *specificVIN {
				continue
			}
		}

		// Process already-expanded coverage IDs with simple filtering
		var subCovTypes []ptypes.SubCoverageType
		for _, coverageID := range deductible.SubCoverageIDs {
			if !relevantSubCoverageIDs[coverageID] {
				continue
			}
			subCovType, err := converters.ConvertCoverageToSubCoverageType(coverageID)
			if err != nil || subCovType == ptypes.SubCoverageType_SubCoverageType_Unspecified {
				continue
			}
			subCovTypes = append(subCovTypes, subCovType)
		}

		if len(subCovTypes) == 0 {
			continue
		}

		result.SubCoverages = append(result.SubCoverages, subCovTypes...)
		addLimitAndDeductible(result, nil, &deductible.Amount, subCovTypes, nil)
	}

	// Deduplicate sub-coverages
	result.SubCoverages = deduplicateSubCoverages(result.SubCoverages)
	return result
}

// buildLimitAndDeductibleSpecsForVehicleAndCoverages builds coverage inputs for a specific vehicle
// by processing only the limits and deductibles for that specific VIN and only for relevant sub-coverages
// we also build relevantSubCoverageIDs from the primary coverages as part of the buildPolicyLevelCoverageInputs function
// which includes coverages meant to be included in the spec.
func buildLimitAndDeductibleSpecsForVehicleAndCoverages(
	app *model.BusinessAutoApp,
	specificVIN string,
	relevantSubCoverageIDs map[app_enums.Coverage]bool,
) *coverageInputs {
	return buildLimitAndDeductibleSpecs(app, &specificVIN, relevantSubCoverageIDs)
}

// buildLimitAndDeductibleSpecsForPolicyAndCoverages builds coverage inputs for policy-level
// by processing only the limits and deductibles with VIN: null and only for relevant sub-coverages
// we also build relevantSubCoverageIDs from the primary coverages as part of the buildPolicyLevelCoverageInputs function
// which includes coverages meant to be included in the policy spec.
func buildLimitAndDeductibleSpecsForPolicyAndCoverages(
	app *model.BusinessAutoApp,
	relevantSubCoverageIDs map[app_enums.Coverage]bool,
) *coverageInputs {
	return buildLimitAndDeductibleSpecs(app, nil, relevantSubCoverageIDs)
}

// expandAppDataAndBuildRelevancy expands PIP/UMUIM in app data structures and populates relevantSubCoverageIDs
// Returns a map of limit index to AddedOn flag for state-specific behavior
func expandAppDataAndBuildRelevancy(app *model.BusinessAutoApp, state us_states.USState, relevantSubCoverageIDs map[app_enums.Coverage]bool) map[int]*bool {
	limitAddedOnFlags := make(map[int]*bool)
	// Expand Limits
	for i := range app.CoveragesInfo.Limits {
		limit := &app.CoveragesInfo.Limits[i]
		expandedIDs := make([]app_enums.Coverage, 0, len(limit.SubCoverageIDs))
		// if limit has one subCoverage group (UMUIM for ex), fetch Sub Coverage and capture AddedOn flag
		if len(limit.SubCoverageIDs) == 1 {
			originalCoverage := limit.SubCoverageIDs[0]
			if subs, _, ok := coverage.GetExpansionMapping(state, originalCoverage); ok && len(subs) > 0 {
				// Capture AddedOn flag for this original coverage before expansion
				if addedOn := GetCoverageAddedOnFlag(state, originalCoverage); addedOn != nil {
					limitAddedOnFlags[i] = addedOn
				}
				expandedIDs = append(expandedIDs, subs...)
				for _, sub := range subs {
					relevantSubCoverageIDs[sub] = true
				}
			} else {
				expandedIDs = append(expandedIDs, limit.SubCoverageIDs[0])
			}
		} else {
			// else pass ids without expanding to coverage group
			expandedIDs = append(expandedIDs, limit.SubCoverageIDs...)
		}

		// Update the actual limit with expanded IDs
		limit.SubCoverageIDs = expandedIDs
	}

	// Expand Deductibles
	for i := range app.CoveragesInfo.Deductibles {
		deductible := &app.CoveragesInfo.Deductibles[i]
		expandedIDs := make([]app_enums.Coverage, 0, len(deductible.SubCoverageIDs))

		for _, id := range deductible.SubCoverageIDs {
			if subs, _, ok := coverage.GetExpansionMapping(state, id); ok && len(subs) > 0 {
				expandedIDs = append(expandedIDs, subs...)
				for _, sub := range subs {
					relevantSubCoverageIDs[sub] = true
				}
			} else {
				expandedIDs = append(expandedIDs, id)
			}
		}

		deductible.SubCoverageIDs = expandedIDs
	}

	return limitAddedOnFlags
}
