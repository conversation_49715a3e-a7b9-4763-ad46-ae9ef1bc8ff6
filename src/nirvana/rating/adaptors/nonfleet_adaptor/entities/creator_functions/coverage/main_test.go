package coverage

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/proto"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/rating/adaptors/common"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

func TestCreateCoverage(t *testing.T) {
	states := []string{
		"Ohio",
		"Illinois",
		"Michigan",
		"Minnesota",
		"Indiana",
		"Missouri",
	}

	expectedRmlCoverage := getCoverage()

	for _, state := range states {
		t.Run(fmt.Sprintf("State - %s", state), func(t *testing.T) {
			input := &creator_functions.Input{
				Input: common.Input{
					PolicyName: ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
					BundleChunkSpec: &ptypes.BundleSpec_ChunkSpec{
						Data: &ptypes.BundleSpec_ChunkSpec_NonFleetBundleChunkSpecData{
							NonFleetBundleChunkSpecData: &ptypes.NonFleet_BundleChunkSpecData{},
						},
					},
					PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
						Data: &ptypes.PolicySpec_ChunkSpec_NonFleetPolicyChunkSpecData{
							NonFleetPolicyChunkSpecData: &ptypes.NonFleet_PolicyChunkSpecData{
								Company: &ptypes.NonFleet_Company{
									UsState: state,
								},
							},
						},
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
							ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
						},
						LimitSpecs: []*ptypes.LimitSpec{
							{
								SubCoverageGroup: &ptypes.SubCoverageGroup{
									SubCoverages: []ptypes.SubCoverageType{
										ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
										ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
									},
								},
								Amount:  100000,
								Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
							},
						},
						DeductibleSpecs: []*ptypes.DeductibleSpec{
							{
								SubCoverageGroup: &ptypes.SubCoverageGroup{
									SubCoverages: []ptypes.SubCoverageType{
										ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
										ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
									},
								},
								Amount: 250,
							},
						},
					},
					PolicyOriginalDates: &proto.Interval{
						Start: timestamppb.New(time_utils.NewDate(2021, 10, 8).ToTime()),
					},
				},
			}

			got, err := New(
				context.Background(),
				nil,
				input,
			)
			require.NoError(t, err)
			require.Equal(t, expectedRmlCoverage, got)
		})
	}
}

func TestBlanketModifiers_WithSuccess(t *testing.T) {
	tests := []struct {
		name        string
		policyName  ptypes.PolicyName
		blanketAI   []*ptypes.BlanketRegularAdditionalInsured
		blanketPNC  []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured
		blanketWOS  []*ptypes.BlanketWaiverOfSubrogation
		expectedAI  bool
		expectedPNC bool
		expectedWOS bool
	}{
		{
			name:       "MOTOR_CARRIER with liab blankets",
			policyName: ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
			blanketAI: []*ptypes.BlanketRegularAdditionalInsured{
				{SubCoverageGroup: liabSubCoverageGroup},
				{SubCoverageGroup: pipSubCoverageGroup},
			},
			blanketPNC: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
				{SubCoverageGroup: liabSubCoverageGroup},
				{SubCoverageGroup: pipSubCoverageGroup},
			},
			blanketWOS: []*ptypes.BlanketWaiverOfSubrogation{
				{SubCoverageGroup: liabSubCoverageGroup},
				{SubCoverageGroup: pipSubCoverageGroup},
			},
			expectedAI:  true,
			expectedPNC: true,
			expectedWOS: true,
		},
		{
			name:       "MOTOR_CARRIER with non liab blankets",
			policyName: ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
			blanketAI: []*ptypes.BlanketRegularAdditionalInsured{
				{SubCoverageGroup: cargoSubCoverageGroup},
			},
			blanketPNC: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
				{SubCoverageGroup: cargoSubCoverageGroup},
			},
			blanketWOS: []*ptypes.BlanketWaiverOfSubrogation{
				{SubCoverageGroup: cargoSubCoverageGroup},
			},
			expectedAI:  false,
			expectedPNC: false,
			expectedWOS: false,
		},
		{
			name:        "MOTOR_CARRIER with no blankets",
			policyName:  ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
			blanketAI:   nil,
			blanketPNC:  nil,
			blanketWOS:  nil,
			expectedAI:  false,
			expectedPNC: false,
			expectedWOS: false,
		},
		{
			name:       "MOTOR_TRUCK_CARGO with cargo blankets",
			policyName: ptypes.PolicyName_PolicyName_MOTOR_TRUCK_CARGO,
			blanketAI: []*ptypes.BlanketRegularAdditionalInsured{
				{SubCoverageGroup: cargoSubCoverageGroup},
				{SubCoverageGroup: pipSubCoverageGroup},
			},
			blanketPNC: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
				{SubCoverageGroup: cargoSubCoverageGroup},
				{SubCoverageGroup: pipSubCoverageGroup},
			},
			blanketWOS: []*ptypes.BlanketWaiverOfSubrogation{
				{SubCoverageGroup: cargoSubCoverageGroup},
				{SubCoverageGroup: pipSubCoverageGroup},
			},
			expectedAI:  true,
			expectedPNC: true,
			expectedWOS: true,
		},
		{
			name:       "MOTOR_TRUCK_CARGO without cargo blankets",
			policyName: ptypes.PolicyName_PolicyName_MOTOR_TRUCK_CARGO,
			blanketAI: []*ptypes.BlanketRegularAdditionalInsured{
				{SubCoverageGroup: liabSubCoverageGroup},
			},
			blanketPNC: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
				{SubCoverageGroup: liabSubCoverageGroup},
			},
			blanketWOS: []*ptypes.BlanketWaiverOfSubrogation{
				{SubCoverageGroup: liabSubCoverageGroup},
			},
			expectedAI:  false,
			expectedPNC: false,
			expectedWOS: false,
		},
		{
			name:       "GENERAL_LIABILITY with GL blankets",
			policyName: ptypes.PolicyName_PolicyName_GENERAL_LIABILITY,
			blanketAI: []*ptypes.BlanketRegularAdditionalInsured{
				{SubCoverageGroup: glSubCoverageGroup},
				{SubCoverageGroup: pipSubCoverageGroup},
			},
			blanketPNC: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
				{SubCoverageGroup: glSubCoverageGroup},
				{SubCoverageGroup: pipSubCoverageGroup},
			},
			blanketWOS: []*ptypes.BlanketWaiverOfSubrogation{
				{SubCoverageGroup: glSubCoverageGroup},
				{SubCoverageGroup: pipSubCoverageGroup},
			},
			expectedAI:  true,
			expectedPNC: true,
			expectedWOS: true,
		},
		{
			name:       "GENERAL_LIABILITY without gl blankets",
			policyName: ptypes.PolicyName_PolicyName_GENERAL_LIABILITY,
			blanketAI: []*ptypes.BlanketRegularAdditionalInsured{
				{SubCoverageGroup: cargoSubCoverageGroup},
			},
			blanketPNC: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
				{SubCoverageGroup: cargoSubCoverageGroup},
			},
			blanketWOS: []*ptypes.BlanketWaiverOfSubrogation{
				{SubCoverageGroup: cargoSubCoverageGroup},
			},
			expectedAI:  false,
			expectedPNC: false,
			expectedWOS: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			input := &creator_functions.Input{
				Input: common.Input{
					PolicyName: tt.policyName,
					BundleChunkSpec: &ptypes.BundleSpec_ChunkSpec{
						Data: &ptypes.BundleSpec_ChunkSpec_NonFleetBundleChunkSpecData{
							NonFleetBundleChunkSpecData: &ptypes.NonFleet_BundleChunkSpecData{},
						},
					},
					PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
						RegularAdditionalInsuredBlankets:                   tt.blanketAI,
						PrimaryAndNonContributoryAdditionalInsuredBlankets: tt.blanketPNC,
						WaiverOfSubrogationBlankets:                        tt.blanketWOS,
						Data: &ptypes.PolicySpec_ChunkSpec_NonFleetPolicyChunkSpecData{
							NonFleetPolicyChunkSpecData: &ptypes.NonFleet_PolicyChunkSpecData{
								Company: &ptypes.NonFleet_Company{
									UsState: "Ohio",
								},
							},
						},
					},
					PolicyOriginalDates: &proto.Interval{
						Start: timestamppb.New(time_utils.NewDate(2021, 10, 8).ToTime()),
					},
				},
			}

			got, err := New(
				context.Background(),
				nil,
				input,
			)

			require.NoError(t, err)
			require.NotNil(t, got)
			require.Equal(t, tt.expectedAI, got.HasBlanketAdditionalInsured)
			require.Equal(t, tt.expectedPNC, got.HasBlanketPNCAdditionalInsured)
			require.Equal(t, tt.expectedWOS, got.HasBlanketWaiverOfSubrogation)
		})
	}
}

func TestBlanketModifiers_WithUnsupportedPolicyName(t *testing.T) {
	input := &creator_functions.Input{
		Input: common.Input{
			PolicyName: ptypes.PolicyName_PolicyName_Unspecified,
			BundleChunkSpec: &ptypes.BundleSpec_ChunkSpec{
				Data: &ptypes.BundleSpec_ChunkSpec_NonFleetBundleChunkSpecData{
					NonFleetBundleChunkSpecData: &ptypes.NonFleet_BundleChunkSpecData{},
				},
			},
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				Data: &ptypes.PolicySpec_ChunkSpec_NonFleetPolicyChunkSpecData{
					NonFleetPolicyChunkSpecData: &ptypes.NonFleet_PolicyChunkSpecData{
						Company: &ptypes.NonFleet_Company{
							UsState: "Ohio",
						},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(time_utils.NewDate(2021, 10, 8).ToTime()),
			},
		},
	}

	got, err := New(
		context.Background(),
		nil,
		input,
	)

	require.Error(t, err)
	require.Nil(t, got)
	require.Regexp(t, "failed to get sub coverage group for policy PolicyName_Unspecified", err)
}

func getCoverage() *entities.Coverage {
	return &entities.Coverage{
		Id:                  "coverage", // singleton,
		EffectiveDateYear:   2021,
		PolicyEffectiveDate: 20211008,
		LiabCsl:             "100000",
		LiabDeductible:      "250",
		UmLimit:             "100000",
		UimLimit:            "100000",
		UmUimLimit:          "100000",
		UmPdLimit:           "25000",
		MedPayLimit:         "500",
		PipLimit:            "2500",
		GlAggLimit:          "2000000",
		GlOccuranceLimit:    "1000000",
		MtcLimit:            "100000",
		RentalLimit:         "30perday900max",
		TlsLimit:            "5000",
		TrlintLimit:         "15000",
		NotLimit:            "40000",
		UmPdDeductible:      "250",
		ApdDeductible:       "1000",
		MtcDeductible:       "1000",
		TrlintDeductible:    "1000",
		NotDeductible:       "1000",
		HasLiabCoverage:     true,
	}
}
