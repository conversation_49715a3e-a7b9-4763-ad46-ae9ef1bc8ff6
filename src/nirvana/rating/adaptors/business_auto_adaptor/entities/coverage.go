package entities

import (
	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/rating/adaptors/common"
)

type Coverage struct {
	Id string

	// in YYYYMMDD format
	PolicyEffectiveDate int64 `rml:"integer,number.integer"`

	HasLiabBlanketRegularAdditionalInsured bool `rml:"bool,boolean"`
	HasLiabBlanketPNCAdditionalInsured     bool `rml:"bool,boolean"`
	HasLiabBlanketWaiverOfSubrogation      bool `rml:"bool,boolean"`

	HasWorkersCompPolicy bool `rml:"bool,boolean"`

	HasLiabCoverage                         bool `rml:"bool,boolean"`
	HasMedPayCoverage                       bool `rml:"bool,boolean"`
	HasUIMBICoverage                        bool `rml:"bool,boolean"`
	HasHiredAutoLiabCoverage                bool `rml:"bool,boolean"`
	HasHiredAutoPDCoverage                  bool `rml:"bool,boolean"`
	HasNonOwnedVehicleCoverage              bool `rml:"bool,boolean"`
	HasMedicalExpenseBenefitsCoverage       bool `rml:"bool,boolean"`
	HasWorkLossBenefitsCoverage             bool `rml:"bool,boolean"`
	HasFuneralExpenseBenefitsCoverage       bool `rml:"bool,boolean"`
	HasAccidentalDeathBenefitsCoverage      bool `rml:"bool,boolean"`
	HasExtraordinaryMedicalBenefitsCoverage bool `rml:"bool,boolean"`
	HasPIPCoverage                          bool `rml:"bool,boolean"`
	HasUMUIMCoverage                        bool `rml:"bool,boolean"`

	LiabLimit   string `rml:"enum,LiabLimitEnum"`
	MedPayLimit string `rml:"enum,MedPayLimitEnum"`
	UmLimit     string `rml:"enum,UMLimitEnum"`
	UmbiLimit   string `rml:"enum,UMBILimitEnum"`
	UimbiLimit  string `rml:"enum,UIMBILimitEnum"`
	PipLimit    string `rml:"enum,PIPLimitEnum"`
	UmuimLimit  string `rml:"enum,UMUIMLimitEnum"`

	AreUMLimitsStacked    bool `rml:"bool,boolean"`
	AreUMUIMLimitsAddedOn bool `rml:"bool,boolean"`

	MedicalExpenseBenefitsLimit       string `rml:"enum,MedicalExpenseBenefitsLimitEnum"`
	WorkLossBenefitsLimit             string `rml:"enum,WorkLossBenefitsLimitEnum"`
	FuneralExpenseBenefitsLimit       string `rml:"enum,FuneralExpenseBenefitsLimitEnum"`
	AccidentalDeathBenefitsLimit      string `rml:"enum,AccidentalDeathBenefitsLimitEnum"`
	ExtraordinaryMedicalBenefitsLimit string `rml:"enum,ExtraordinaryMedicalBenefitsLimitEnum"`

	HiredAutoPDDeductible string `rml:"enum,HiredAutoPDDeductibleEnum"`
	UmuimDeductible       string `rml:"enum,UMUIMDeductibleEnum"`

	LiabScheduleMod         float64 `rml:"float,number.decimal"`
	LiabLossFreeMod         float64 `rml:"float,number.decimal"`
	LiabExperienceRatingMod float64 `rml:"float,number.decimal"`

	PdScheduleMod         float64 `rml:"float,number.decimal"`
	PdLossFreeMod         float64 `rml:"float,number.decimal"`
	PdExperienceRatingMod float64 `rml:"float,number.decimal"`

	DriverClassMod float64 `rml:"float,number.decimal"`

	LiabBlanketRegularAdditionalInsuredPremium float64 `rml:"float,number.decimal,output"`
	LiabBlanketPNCAdditionalInsuredPremium     float64 `rml:"float,number.decimal,output"`
	LiabBlanketWaiverOfSubrogationPremium      float64 `rml:"float,number.decimal,output"`
}

var _ common.Entity = (*Coverage)(nil)

func (c *Coverage) RatemlId() string {
	return c.Id
}

func (c *Coverage) RatemlTypeName() common.EntityType {
	return common.EntityTypeCoverage
}

func (c *Coverage) HandleConnectedEntity(e common.Entity) error {
	return errors.Newf(
		"unhandled entity %v(id=%s) for entity %v(id=%s)",
		e.RatemlTypeName(),
		e.RatemlId(),
		c.RatemlTypeName(),
		c.RatemlId(),
	)
}

func (c *Coverage) ConnectedEntityTypes() []common.EntityType {
	return nil
}
