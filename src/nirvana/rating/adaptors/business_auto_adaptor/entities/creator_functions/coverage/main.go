package coverage

import (
	"context"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/rating/adaptors/business_auto_adaptor/entities"
	"nirvanatech.com/nirvana/rating/adaptors/business_auto_adaptor/entities/creator_functions"
	"nirvanatech.com/nirvana/rating/adaptors/common"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes/programs/business_auto"
)

const (
	defaultScheduleModValue         = 1.0
	defaultExperienceRatingModValue = 1.0
	defaultLossFreeModValue         = 1.0
)

type CreatorFn func(
	ctx context.Context,
	pc *common.ProgramContext,
	input *creator_functions.Input,
) (*entities.Coverage, error)

func NewCreatorFn(additionalSubCovFieldsSetterFns ...SubCovFieldsSetterFn) CreatorFn {
	return func(
		_ context.Context,
		pc *common.ProgramContext,
		input *creator_functions.Input,
	) (*entities.Coverage, error) {
		policyOriginalStartDate, err := input.GetPolicyOriginalStartDate()
		if err != nil {
			return nil, err
		}

		policyOriginalStartDateAsTime := policyOriginalStartDate.AsTime()
		policyOriginalStartDateAsYYYYMMDD, err := time_utils.FormatTimeToYYYYMMDD(policyOriginalStartDateAsTime)
		if err != nil {
			return nil, errors.Wrapf(err, "Failed to convert effective date to YYYYMMDD format")
		}

		hasLiabBlanketRegularAI, err := input.HasBlanketRegularAI(business_auto.LiabSubCoverageGroup)
		if err != nil {
			return nil, err
		}

		hasLiabBlanketPNCAI, err := input.HasBlanketPNCAI(business_auto.LiabSubCoverageGroup)
		if err != nil {
			return nil, err
		}

		hasLiabBlanketWOS, err := input.HasBlanketWOS(business_auto.LiabSubCoverageGroup)
		if err != nil {
			return nil, err
		}

		mk := pc.ModelKey
		defaultCoverageValuesForState, ok := env.DefaultCoverageValuesMap[mk]
		if !ok {
			return nil, errors.Errorf("default coverage values not found for model key: %s", mk)
		}

		company, err := input.GetCompany()
		if err != nil {
			return nil, errors.Wrap(err, "failed to get company inputs")
		}

		c := &entities.Coverage{
			Id:                                     "coverage",
			PolicyEffectiveDate:                    int64(policyOriginalStartDateAsYYYYMMDD),
			HasLiabBlanketRegularAdditionalInsured: hasLiabBlanketRegularAI,
			HasLiabBlanketPNCAdditionalInsured:     hasLiabBlanketPNCAI,
			HasLiabBlanketWaiverOfSubrogation:      hasLiabBlanketWOS,

			// We do this as a hack given that RateML doesn't support optional fields.
			// You can read more in the documentation of the defaultCoverageValuesPerModelKey map.
			LiabLimit:                         defaultCoverageValuesForState.LiabLimit,
			MedPayLimit:                       defaultCoverageValuesForState.MedPayLimit,
			UmLimit:                           defaultCoverageValuesForState.UmLimit,
			UmuimLimit:                        defaultCoverageValuesForState.UmuimLimit,
			UmbiLimit:                         defaultCoverageValuesForState.UmbiLimit,
			UimbiLimit:                        defaultCoverageValuesForState.UimbiLimit,
			HiredAutoPDDeductible:             defaultCoverageValuesForState.HiredAutoPDDeductible,
			MedicalExpenseBenefitsLimit:       defaultCoverageValuesForState.MedicalExpenseBenefitsLimit,
			WorkLossBenefitsLimit:             defaultCoverageValuesForState.WorkLossBenefitsLimit,
			FuneralExpenseBenefitsLimit:       defaultCoverageValuesForState.FuneralExpenseBenefitsLimit,
			AccidentalDeathBenefitsLimit:      defaultCoverageValuesForState.AccidentalDeathBenefitsLimit,
			ExtraordinaryMedicalBenefitsLimit: defaultCoverageValuesForState.ExtraordinaryMedicalBenefitsLimit,
			PipLimit:                          defaultCoverageValuesForState.PipLimit,
			UmuimDeductible:                   defaultCoverageValuesForState.UmuimDeductible,

			HasWorkersCompPolicy: company.HasWorkersCompPolicy,
		}

		subCovFieldsSetterFunctions := append(
			[]SubCovFieldsSetterFn{
				setLiabFields,
				setHiredAutoLiabFlag,
				setHiredAutoPDFields,
				setNonOwnedVehicleFlag,
			},
			additionalSubCovFieldsSetterFns...,
		)

		for _, fn := range subCovFieldsSetterFunctions {
			err := fn(pc, c, input)
			if err != nil {
				return nil, err
			}
		}

		err = setModificationFields(c, input)
		if err != nil {
			return nil, errors.Wrap(err, "failed to set modification fields")
		}

		return c, nil
	}
}

func setModificationFields(c *entities.Coverage, input *creator_functions.Input) error {
	liabScheduleMod, err := getScheduleModificationPercentage(input, business_auto.LiabSubCoverageGroup)
	if err != nil {
		return errors.Wrap(err, "failed to get liability schedule modification")
	}
	c.LiabScheduleMod = liabScheduleMod

	pdScheduleMod, err := getScheduleModificationPercentage(input, business_auto.PDSubCoverageGroup)
	if err != nil {
		return errors.Wrap(err, "failed to get PD schedule modification")
	}
	c.PdScheduleMod = pdScheduleMod

	liabExperienceRatingMod, err := getExperienceRatingModificationPercentage(input, business_auto.LiabSubCoverageGroup)
	if err != nil {
		return errors.Wrap(err, "failed to get liability experience rating modification")
	}
	c.LiabExperienceRatingMod = liabExperienceRatingMod

	pdExperienceRatingMod, err := getExperienceRatingModificationPercentage(input, business_auto.PDSubCoverageGroup)
	if err != nil {
		return errors.Wrap(err, "failed to get PD experience rating modification")
	}
	c.PdExperienceRatingMod = pdExperienceRatingMod

	liabLossFreeMod, err := getLossFreeModificationPercentage(input, business_auto.LiabSubCoverageGroup)
	if err != nil {
		return errors.Wrap(err, "failed to get liability loss free modification")
	}
	c.LiabLossFreeMod = liabLossFreeMod

	pdLossFreeMod, err := getLossFreeModificationPercentage(input, business_auto.PDSubCoverageGroup)
	if err != nil {
		return errors.Wrap(err, "failed to get PD loss free modification")
	}
	c.PdLossFreeMod = pdLossFreeMod

	driverClassMod, err := input.GetDriverClassModification()
	if err != nil {
		return errors.Wrap(err, "failed to get driver class modification")
	}
	c.DriverClassMod = driverClassMod

	return nil
}

// getScheduleModificationPercentage returns the percentage value for a schedule modification,
// using the default value only when ScheduleModificationNotFoundError occurs.
func getScheduleModificationPercentage(
	input *creator_functions.Input,
	subCovGroup *ptypes.SubCoverageGroup,
) (float64, error) {
	scheduleMod, err := input.GetScheduleModification(subCovGroup)
	if err != nil {
		if errors.Is(err, creator_functions.ScheduleModificationNotFoundError) {
			return defaultScheduleModValue, nil
		}
		return 0, err
	}
	return scheduleMod.GetPercentage(), nil
}

// getExperienceRatingModificationPercentage returns the percentage value for an experience rating modification,
// using the default value only when ExperienceRatingModificationNotFoundError occurs.
func getExperienceRatingModificationPercentage(
	input *creator_functions.Input,
	subCovGroup *ptypes.SubCoverageGroup,
) (float64, error) {
	experienceRatingMod, err := input.GetExperienceRatingModification(subCovGroup)
	if err != nil {
		if errors.Is(err, creator_functions.ExperienceRatingModificationNotFoundError) {
			return defaultExperienceRatingModValue, nil
		}
		return 0, err
	}
	return experienceRatingMod.GetPercentage(), nil
}

// getLossFreeModificationPercentage returns the percentage value for a loss free modification,
// using the default value only when LossFreeModificationNotFoundError occurs.
func getLossFreeModificationPercentage(
	input *creator_functions.Input,
	subCovGroup *ptypes.SubCoverageGroup,
) (float64, error) {
	lossFreeMod, err := input.GetLossFreeModification(subCovGroup)
	if err != nil {
		if errors.Is(err, creator_functions.LossFreeModificationNotFoundError) {
			return defaultLossFreeModValue, nil
		}
		return 0, err
	}
	return lossFreeMod.GetPercentage(), nil
}
