package coverage

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/proto"
	"nirvanatech.com/nirvana/common-go/us_states"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	"nirvanatech.com/nirvana/rating/adaptors/business_auto_adaptor/entities"
	"nirvanatech.com/nirvana/rating/adaptors/business_auto_adaptor/entities/creator_functions"
	"nirvanatech.com/nirvana/rating/adaptors/business_auto_adaptor/entities/creator_functions/common"
	adaptors "nirvanatech.com/nirvana/rating/adaptors/common"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes/programs/business_auto"
	"nirvanatech.com/nirvana/rating/rtypes"
)

type NewFnTestEnv struct {
	fx.In
}

type NewFnTestSuite struct {
	suite.Suite

	env   NewFnTestEnv
	fxApp *fxtest.App

	ctx            context.Context
	modelKey       rtypes.ModelKey
	programContext *adaptors.ProgramContext
}

func TestNewFn(t *testing.T) {
	suite.Run(t, new(NewFnTestSuite))
}

func (s *NewFnTestSuite) SetupSuite() {
	s.ctx = context.Background()
	s.modelKey = rtypes.NewModelKey("business_auto", us_states.AK, rtypes.Version101)

	newMockDefaultCoverageValuesMap := func() common.DefaultCoverageValuesMap {
		return common.DefaultCoverageValuesMap{
			s.modelKey: {
				LiabLimit:                         "1",
				MedPayLimit:                       "2",
				UmbiLimit:                         "3",
				UimbiLimit:                        "4",
				HiredAutoPDDeductible:             "5",
				UmLimit:                           "6",
				UmuimLimit:                        "7",
				MedicalExpenseBenefitsLimit:       "8",
				WorkLossBenefitsLimit:             "9",
				FuneralExpenseBenefitsLimit:       "10",
				AccidentalDeathBenefitsLimit:      "11",
				ExtraordinaryMedicalBenefitsLimit: "12",
				PipLimit:                          "13",
				UmuimDeductible:                   "14",
			},
		}
	}

	s.fxApp = testloader.RequireStart(
		s.T(),
		&s.env,
		testloader.Use(fx.Decorate(newMockDefaultCoverageValuesMap)),
	)
}

func (s *NewFnTestSuite) SetupTest() {
	s.programContext = adaptors.NewProgramContext(s.modelKey)
}

func (s *NewFnTestSuite) TearDownSuite() {
	s.fxApp.RequireStop()
}

func (s *NewFnTestSuite) Test_WithoutCompany() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}
	got, err := NewCreatorFn()(s.ctx, s.programContext, input)
	s.Require().Nil(got)
	s.Require().Error(err)
	s.Require().Regexp("failed to get company inputs", err.Error())
}

func (s *NewFnTestSuite) Test_WithNoAdditionalSubCovSetterFns_WithNonDefaultData_WithSuccess() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
					ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
					ptypes.SubCoverageType_SubCoverageType_MedicalPayments,
					ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
					ptypes.SubCoverageType_SubCoverageType_HiredAutoLiability,
					ptypes.SubCoverageType_SubCoverageType_HiredAutoPhysicalDamage,
					ptypes.SubCoverageType_SubCoverageType_NonOwnedVehicle,
				},
				LimitSpecs: []*ptypes.LimitSpec{
					{
						SubCoverageGroup: business_auto.LiabSubCoverageGroup,
						Cadence:          ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
						Amount:           1000000,
					},
					{
						SubCoverageGroup: business_auto.MedPaySubCoverageGroup,
						Cadence:          ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
						Amount:           5000,
					},
					{
						SubCoverageGroup: business_auto.UIMBISubCoverageGroup,
						Cadence:          ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
						Amount:           200000,
					},
				},
				DeductibleSpecs: []*ptypes.DeductibleSpec{
					{
						SubCoverageGroup: business_auto.HiredAutoPDSubCoverageGroup,
						Amount:           500,
					},
				},
				RegularAdditionalInsuredBlankets: []*ptypes.BlanketRegularAdditionalInsured{{
					SubCoverageGroup: business_auto.LiabSubCoverageGroup,
				}},
				PrimaryAndNonContributoryAdditionalInsuredBlankets: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{{
					SubCoverageGroup: business_auto.LiabSubCoverageGroup,
				}},
				WaiverOfSubrogationBlankets: []*ptypes.BlanketWaiverOfSubrogation{{
					SubCoverageGroup: business_auto.LiabSubCoverageGroup,
				}},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						DriverClassModification: 0.7,
						ScheduleModifications: []*ptypes.ScheduleModification{
							{
								SubCoverageGroup: business_auto.LiabSubCoverageGroup,
								Percentage:       1.2,
							},
							{
								SubCoverageGroup: business_auto.PDSubCoverageGroup,
								Percentage:       0.9,
							},
						},
						ExperienceRatingModifications: []*ptypes.ExperienceRatingModification{
							{
								SubCoverageGroup: business_auto.LiabSubCoverageGroup,
								Percentage:       1.5,
							},
							{
								SubCoverageGroup: business_auto.PDSubCoverageGroup,
								Percentage:       1.4,
							},
						},
						LossFreeModifications: []*ptypes.LossFreeModification{
							{
								SubCoverageGroup: business_auto.LiabSubCoverageGroup,
								Percentage:       0.8,
							},
							{
								SubCoverageGroup: business_auto.PDSubCoverageGroup,
								Percentage:       1.3,
							},
						},
						Company: &ptypes.BusinessAuto_Company{
							HasWorkersCompPolicy: true,
						},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	expected := &entities.Coverage{
		Id:                                     "coverage",
		PolicyEffectiveDate:                    20230615,
		HasLiabBlanketRegularAdditionalInsured: true,
		HasLiabBlanketPNCAdditionalInsured:     true,
		HasLiabBlanketWaiverOfSubrogation:      true,
		HasLiabCoverage:                        true,
		HasHiredAutoLiabCoverage:               true,
		HasHiredAutoPDCoverage:                 true,
		HasNonOwnedVehicleCoverage:             true,
		HasWorkersCompPolicy:                   true,
		LiabLimit:                              "1000000",
		MedPayLimit:                            "2",
		UmbiLimit:                              "3",
		UimbiLimit:                             "4",
		UmLimit:                                "6",
		UmuimLimit:                             "7",
		HiredAutoPDDeductible:                  "500",
		LiabScheduleMod:                        1.2,
		LiabLossFreeMod:                        0.8,
		LiabExperienceRatingMod:                1.5,
		PdScheduleMod:                          0.9,
		PdLossFreeMod:                          1.3,
		PdExperienceRatingMod:                  1.4,
		DriverClassMod:                         0.7,
		MedicalExpenseBenefitsLimit:            "8",
		WorkLossBenefitsLimit:                  "9",
		FuneralExpenseBenefitsLimit:            "10",
		AccidentalDeathBenefitsLimit:           "11",
		ExtraordinaryMedicalBenefitsLimit:      "12",
		PipLimit:                               "13",
		UmuimDeductible:                        "14",
	}

	got, err := NewCreatorFn()(s.ctx, s.programContext, input)
	s.Require().NoError(err)
	s.Require().Equal(expected, got)
}

func (s *NewFnTestSuite) Test_WithNoAdditionalSubCovSetterFns_WithDefaultData_WithSuccess() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	expected := &entities.Coverage{
		Id:                                "coverage",
		PolicyEffectiveDate:               20230615,
		LiabScheduleMod:                   1.0,
		LiabLossFreeMod:                   1.0,
		LiabExperienceRatingMod:           1.0,
		PdScheduleMod:                     1.0,
		PdLossFreeMod:                     1.0,
		PdExperienceRatingMod:             1.0,
		LiabLimit:                         "1",
		MedPayLimit:                       "2",
		UmbiLimit:                         "3",
		UimbiLimit:                        "4",
		HiredAutoPDDeductible:             "5",
		UmLimit:                           "6",
		UmuimLimit:                        "7",
		MedicalExpenseBenefitsLimit:       "8",
		WorkLossBenefitsLimit:             "9",
		FuneralExpenseBenefitsLimit:       "10",
		AccidentalDeathBenefitsLimit:      "11",
		ExtraordinaryMedicalBenefitsLimit: "12",
		PipLimit:                          "13",
		UmuimDeductible:                   "14",
	}

	got, err := NewCreatorFn()(s.ctx, s.programContext, input)
	s.Require().NoError(err)
	s.Require().Equal(expected, got)
}

func (s *NewFnTestSuite) Test_WithMissingLiabLimit() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
					ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
				},
				DeductibleSpecs: []*ptypes.DeductibleSpec{
					{
						SubCoverageGroup: business_auto.LiabSubCoverageGroup,
						Amount:           1000,
					},
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	got, err := NewCreatorFn()(s.ctx, s.programContext, input)
	s.Require().Error(err)
	s.Require().Regexp("failed to obtain Liab limit", err.Error())
	s.Require().Nil(got)
}

func (s *NewFnTestSuite) Test_WithMissingMedPayLimit() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_MedicalPayments,
				},
				DeductibleSpecs: []*ptypes.DeductibleSpec{
					{
						SubCoverageGroup: business_auto.MedPaySubCoverageGroup,
						Amount:           250,
					},
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	got, err := NewCreatorFn(SetMedPayFields)(s.ctx, s.programContext, input)
	s.Require().Error(err)
	s.Require().Regexp("failed to obtain MedPay limit", err.Error())
	s.Require().Nil(got)
}

func (s *NewFnTestSuite) Test_WithSetMedPayFields_WithSuccess() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_MedicalPayments,
				},
				LimitSpecs: []*ptypes.LimitSpec{
					{
						SubCoverageGroup: business_auto.MedPaySubCoverageGroup,
						Amount:           5000,
						Cadence:          ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
					},
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	expected := &entities.Coverage{
		Id:                                "coverage",
		PolicyEffectiveDate:               20230615,
		HasMedPayCoverage:                 true,
		LiabScheduleMod:                   1.0,
		LiabLossFreeMod:                   1.0,
		LiabExperienceRatingMod:           1.0,
		PdScheduleMod:                     1.0,
		PdLossFreeMod:                     1.0,
		PdExperienceRatingMod:             1.0,
		LiabLimit:                         "1",
		MedPayLimit:                       "5000",
		UmbiLimit:                         "3",
		UimbiLimit:                        "4",
		HiredAutoPDDeductible:             "5",
		UmLimit:                           "6",
		UmuimLimit:                        "7",
		MedicalExpenseBenefitsLimit:       "8",
		WorkLossBenefitsLimit:             "9",
		FuneralExpenseBenefitsLimit:       "10",
		AccidentalDeathBenefitsLimit:      "11",
		ExtraordinaryMedicalBenefitsLimit: "12",
		PipLimit:                          "13",
		UmuimDeductible:                   "14",
	}

	got, err := NewCreatorFn(SetMedPayFields)(s.ctx, s.programContext, input)
	s.Require().NoError(err)
	s.Require().Equal(expected, got)
}

func (s *NewFnTestSuite) Test_WithSetUIMBIFields_WithMissingUIMBILimit() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
				},
				DeductibleSpecs: []*ptypes.DeductibleSpec{
					{
						SubCoverageGroup: business_auto.UIMBISubCoverageGroup,
						Amount:           250,
					},
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	got, err := NewCreatorFn(SetUIMBIFields)(s.ctx, s.programContext, input)
	s.Require().Error(err)
	s.Require().Regexp("failed to obtain UIMBI limit", err.Error())
	s.Require().Nil(got)
}

func (s *NewFnTestSuite) Test_WithSetUIMBIFields_WithSuccess() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
				},
				LimitSpecs: []*ptypes.LimitSpec{
					{
						SubCoverageGroup: business_auto.UIMBISubCoverageGroup,
						Cadence:          ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
						Amount:           200000,
					},
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	expected := &entities.Coverage{
		Id:                                "coverage",
		PolicyEffectiveDate:               20230615,
		HasUIMBICoverage:                  true,
		LiabScheduleMod:                   1.0,
		LiabLossFreeMod:                   1.0,
		LiabExperienceRatingMod:           1.0,
		PdScheduleMod:                     1.0,
		PdLossFreeMod:                     1.0,
		PdExperienceRatingMod:             1.0,
		LiabLimit:                         "1",
		MedPayLimit:                       "2",
		UmbiLimit:                         "3",
		UimbiLimit:                        "200000",
		HiredAutoPDDeductible:             "5",
		UmLimit:                           "6",
		UmuimLimit:                        "7",
		MedicalExpenseBenefitsLimit:       "8",
		WorkLossBenefitsLimit:             "9",
		FuneralExpenseBenefitsLimit:       "10",
		AccidentalDeathBenefitsLimit:      "11",
		ExtraordinaryMedicalBenefitsLimit: "12",
		PipLimit:                          "13",
		UmuimDeductible:                   "14",
	}

	got, err := NewCreatorFn(SetUIMBIFields)(s.ctx, s.programContext, input)
	s.Require().NoError(err)
	s.Require().Equal(expected, got)
}

func (s *NewFnTestSuite) Test_WithSetUMUIMFields_WithMissingUMUIMLimit() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
					ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
					ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage,
					ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristPropertyDamage,
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	got, err := NewCreatorFn(SetUMUIMFlagAndLimit)(s.ctx, s.programContext, input)
	s.Require().Error(err)
	s.Require().Regexp("failed to obtain UMUIM limit", err.Error())
	s.Require().Nil(got)
}

func (s *NewFnTestSuite) Test_WithSetUMUIMFields_WithSuccess() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
					ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
					ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage,
					ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristPropertyDamage,
				},
				LimitSpecs: []*ptypes.LimitSpec{
					{
						SubCoverageGroup: business_auto.UMUIMSubCoverageGroup,
						Cadence:          ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
						Amount:           300000,
					},
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	expected := &entities.Coverage{
		Id:                                "coverage",
		PolicyEffectiveDate:               20230615,
		HasUMUIMCoverage:                  true,
		LiabScheduleMod:                   1.0,
		LiabLossFreeMod:                   1.0,
		LiabExperienceRatingMod:           1.0,
		PdScheduleMod:                     1.0,
		PdLossFreeMod:                     1.0,
		PdExperienceRatingMod:             1.0,
		LiabLimit:                         "1",
		MedPayLimit:                       "2",
		UmbiLimit:                         "3",
		UimbiLimit:                        "4",
		HiredAutoPDDeductible:             "5",
		UmLimit:                           "6",
		UmuimLimit:                        "300000",
		MedicalExpenseBenefitsLimit:       "8",
		WorkLossBenefitsLimit:             "9",
		FuneralExpenseBenefitsLimit:       "10",
		AccidentalDeathBenefitsLimit:      "11",
		ExtraordinaryMedicalBenefitsLimit: "12",
		PipLimit:                          "13",
		UmuimDeductible:                   "14",
	}

	got, err := NewCreatorFn(SetUMUIMFlagAndLimit)(s.ctx, s.programContext, input)
	s.Require().NoError(err)
	s.Require().Equal(expected, got)
}

func (s *NewFnTestSuite) Test_WithSetUMBILimit_WithMissingUMBILimit() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
				},
				DeductibleSpecs: []*ptypes.DeductibleSpec{
					{
						SubCoverageGroup: business_auto.UMBISubCoverageGroup,
						Amount:           250,
					},
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	got, err := NewCreatorFn(SetUMBILimit)(s.ctx, s.programContext, input)
	s.Require().Error(err)
	s.Require().Regexp("failed to obtain UMBI limit", err.Error())
	s.Require().Nil(got)
}

func (s *NewFnTestSuite) Test_WithSetUMBILimit_WithSuccess() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
				},
				LimitSpecs: []*ptypes.LimitSpec{
					{
						SubCoverageGroup: business_auto.UMBISubCoverageGroup,
						Amount:           25000,
						Cadence:          ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
					},
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	expected := &entities.Coverage{
		Id:                                "coverage",
		PolicyEffectiveDate:               20230615,
		LiabScheduleMod:                   1.0,
		LiabLossFreeMod:                   1.0,
		LiabExperienceRatingMod:           1.0,
		PdScheduleMod:                     1.0,
		PdLossFreeMod:                     1.0,
		PdExperienceRatingMod:             1.0,
		LiabLimit:                         "1",
		MedPayLimit:                       "2",
		UmbiLimit:                         "25000",
		UimbiLimit:                        "4",
		HiredAutoPDDeductible:             "5",
		UmLimit:                           "6",
		UmuimLimit:                        "7",
		MedicalExpenseBenefitsLimit:       "8",
		WorkLossBenefitsLimit:             "9",
		FuneralExpenseBenefitsLimit:       "10",
		AccidentalDeathBenefitsLimit:      "11",
		ExtraordinaryMedicalBenefitsLimit: "12",
		PipLimit:                          "13",
		UmuimDeductible:                   "14",
	}

	got, err := NewCreatorFn(SetUMBILimit)(s.ctx, s.programContext, input)
	s.Require().NoError(err)
	s.Require().Equal(expected, got)
}

func (s *NewFnTestSuite) Test_WithMissingHiredAutoPDDeductible() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_HiredAutoLiability,
					ptypes.SubCoverageType_SubCoverageType_HiredAutoPhysicalDamage,
				},
				LimitSpecs: []*ptypes.LimitSpec{
					{
						SubCoverageGroup: business_auto.HiredAutoPDSubCoverageGroup,
						Cadence:          ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
						Amount:           25000,
					},
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	got, err := NewCreatorFn()(s.ctx, s.programContext, input)
	s.Require().Error(err)
	s.Require().Regexp("failed to obtain HiredAutoPD deductible", err.Error())
	s.Require().Nil(got)
}

func (s *NewFnTestSuite) Test_WithSetUMLimit_WithoutVehicles() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	got, err := NewCreatorFn(SetUMLimit)(s.ctx, s.programContext, input)
	s.Require().Error(err)
	s.Require().Regexp("no vehicles found in program context", err.Error())
	s.Require().Nil(got)
}

func (s *NewFnTestSuite) Test_WithSetUMLimit_WithoutVehiclesWithUMPD() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	vehicles := []*entities.Vehicle{
		{Id: "vin-1"},
		{Id: "vin-2"},
		{Id: "vin-3"},
	}

	for _, v := range vehicles {
		err := s.programContext.AddEntity(s.ctx, v)
		s.Require().NoError(err)
	}

	expected := &entities.Coverage{
		Id:                                "coverage",
		PolicyEffectiveDate:               20230615,
		LiabScheduleMod:                   1.0,
		LiabLossFreeMod:                   1.0,
		LiabExperienceRatingMod:           1.0,
		PdScheduleMod:                     1.0,
		PdLossFreeMod:                     1.0,
		PdExperienceRatingMod:             1.0,
		LiabLimit:                         "1",
		MedPayLimit:                       "2",
		UmbiLimit:                         "3",
		UimbiLimit:                        "4",
		HiredAutoPDDeductible:             "5",
		UmLimit:                           "6",
		UmuimLimit:                        "7",
		MedicalExpenseBenefitsLimit:       "8",
		WorkLossBenefitsLimit:             "9",
		FuneralExpenseBenefitsLimit:       "10",
		AccidentalDeathBenefitsLimit:      "11",
		ExtraordinaryMedicalBenefitsLimit: "12",
		PipLimit:                          "13",
		UmuimDeductible:                   "14",
	}

	got, err := NewCreatorFn(SetUMLimit)(s.ctx, s.programContext, input)
	s.Require().NoError(err)
	s.Require().Equal(expected, got)
}

func (s *NewFnTestSuite) Test_WithSetMedicalExpenseBenefitsFields_WithMissingMedicalExpenseBenefitsLimit() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_MedicalExpenseBenefits,
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	got, err := NewCreatorFn(SetMedicalExpenseBenefitsFields)(s.ctx, s.programContext, input)
	s.Require().Error(err)
	s.Require().Regexp("failed to obtain Medical Expense Benefits limit", err.Error())
	s.Require().Nil(got)
}

func (s *NewFnTestSuite) Test_SetMedicalExpenseBenefitsFields_WithSuccess() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_MedicalExpenseBenefits,
				},
				LimitSpecs: []*ptypes.LimitSpec{
					{
						SubCoverageGroup: business_auto.MedicalExpenseBenefitsSubCoverageGroup,
						Cadence:          ptypes.LimitCadenceType_LimitCadenceType_Aggregate,
						Amount:           10000,
					},
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	expected := &entities.Coverage{
		Id:                                "coverage",
		PolicyEffectiveDate:               20230615,
		LiabScheduleMod:                   1.0,
		LiabLossFreeMod:                   1.0,
		LiabExperienceRatingMod:           1.0,
		PdScheduleMod:                     1.0,
		PdLossFreeMod:                     1.0,
		PdExperienceRatingMod:             1.0,
		LiabLimit:                         "1",
		MedPayLimit:                       "2",
		UmbiLimit:                         "3",
		UimbiLimit:                        "4",
		HiredAutoPDDeductible:             "5",
		UmLimit:                           "6",
		HasMedicalExpenseBenefitsCoverage: true,
		MedicalExpenseBenefitsLimit:       "10000",
		UmuimLimit:                        "7",
		WorkLossBenefitsLimit:             "9",
		FuneralExpenseBenefitsLimit:       "10",
		AccidentalDeathBenefitsLimit:      "11",
		ExtraordinaryMedicalBenefitsLimit: "12",
		PipLimit:                          "13",
		UmuimDeductible:                   "14",
	}

	got, err := NewCreatorFn(SetMedicalExpenseBenefitsFields)(s.ctx, s.programContext, input)
	s.Require().NoError(err)
	s.Require().Equal(expected, got)
}

func (s *NewFnTestSuite) Test_WithSetWorkLossBenefitsFields_WithMissingWorkLossBenefitsMonthlyLimit() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_WorkLossBenefits,
				},
				LimitSpecs: []*ptypes.LimitSpec{
					{
						SubCoverageGroup: business_auto.WorkLossBenefitsSubCoverageGroup,
						Cadence:          ptypes.LimitCadenceType_LimitCadenceType_Aggregate,
						Amount:           15000,
					},
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	got, err := NewCreatorFn(SetWorkLossBenefitsFields)(s.ctx, s.programContext, input)
	s.Require().Error(err)
	s.Require().Regexp("failed to obtain Work Loss Benefits monthly limit", err.Error())
	s.Require().Nil(got)
}

func (s *NewFnTestSuite) Test_WithSetWorkLossBenefitsFields_WithMissingWorkLossBenefitsAggregateLimit() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_WorkLossBenefits,
				},
				LimitSpecs: []*ptypes.LimitSpec{
					{
						SubCoverageGroup: business_auto.WorkLossBenefitsSubCoverageGroup,
						Cadence:          ptypes.LimitCadenceType_LimitCadenceType_Monthly,
						Amount:           1500,
					},
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	got, err := NewCreatorFn(SetWorkLossBenefitsFields)(s.ctx, s.programContext, input)
	s.Require().Error(err)
	s.Require().Regexp("failed to obtain Work Loss Benefits aggregate limit", err.Error())
	s.Require().Nil(got)
}

func (s *NewFnTestSuite) Test_WithSetWorkLossBenefitsFields_WithSuccess() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_WorkLossBenefits,
				},
				LimitSpecs: []*ptypes.LimitSpec{
					{
						SubCoverageGroup: business_auto.WorkLossBenefitsSubCoverageGroup,
						Cadence:          ptypes.LimitCadenceType_LimitCadenceType_Monthly,
						Amount:           1500,
					},
					{
						SubCoverageGroup: business_auto.WorkLossBenefitsSubCoverageGroup,
						Cadence:          ptypes.LimitCadenceType_LimitCadenceType_Aggregate,
						Amount:           15000,
					},
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	expected := &entities.Coverage{
		Id:                                "coverage",
		PolicyEffectiveDate:               20230615,
		LiabScheduleMod:                   1.0,
		LiabLossFreeMod:                   1.0,
		LiabExperienceRatingMod:           1.0,
		PdScheduleMod:                     1.0,
		PdLossFreeMod:                     1.0,
		PdExperienceRatingMod:             1.0,
		LiabLimit:                         "1",
		MedPayLimit:                       "2",
		UmbiLimit:                         "3",
		UimbiLimit:                        "4",
		HiredAutoPDDeductible:             "5",
		UmLimit:                           "6",
		HasWorkLossBenefitsCoverage:       true,
		UmuimLimit:                        "7",
		MedicalExpenseBenefitsLimit:       "8",
		WorkLossBenefitsLimit:             "1500/15000",
		FuneralExpenseBenefitsLimit:       "10",
		AccidentalDeathBenefitsLimit:      "11",
		ExtraordinaryMedicalBenefitsLimit: "12",
		PipLimit:                          "13",
		UmuimDeductible:                   "14",
	}

	got, err := NewCreatorFn(SetWorkLossBenefitsFields)(s.ctx, s.programContext, input)
	s.Require().NoError(err)
	s.Require().Equal(expected, got)
}

func (s *NewFnTestSuite) Test_WithSetFuneralExpenseBenefitsFields_WithMissingFuneralExpenseBenefitsLimit() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_FuneralExpenseBenefits,
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	got, err := NewCreatorFn(SetFuneralExpenseBenefitsFields)(s.ctx, s.programContext, input)
	s.Require().Error(err)
	s.Require().Regexp("failed to obtain Funeral Expense Benefits limit", err.Error())
	s.Require().Nil(got)
}

func (s *NewFnTestSuite) Test_WithSetFuneralExpenseBenefitsFields_WithSuccess() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_FuneralExpenseBenefits,
				},
				LimitSpecs: []*ptypes.LimitSpec{
					{
						SubCoverageGroup: business_auto.FuneralExpenseBenefitsSubCoverageGroup,
						Cadence:          ptypes.LimitCadenceType_LimitCadenceType_Aggregate,
						Amount:           5000,
					},
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	expected := &entities.Coverage{
		Id:                                "coverage",
		PolicyEffectiveDate:               20230615,
		LiabScheduleMod:                   1.0,
		LiabLossFreeMod:                   1.0,
		LiabExperienceRatingMod:           1.0,
		PdScheduleMod:                     1.0,
		PdLossFreeMod:                     1.0,
		PdExperienceRatingMod:             1.0,
		LiabLimit:                         "1",
		MedPayLimit:                       "2",
		UmbiLimit:                         "3",
		UimbiLimit:                        "4",
		HiredAutoPDDeductible:             "5",
		UmLimit:                           "6",
		HasFuneralExpenseBenefitsCoverage: true,
		UmuimLimit:                        "7",
		MedicalExpenseBenefitsLimit:       "8",
		WorkLossBenefitsLimit:             "9",
		FuneralExpenseBenefitsLimit:       "5000",
		AccidentalDeathBenefitsLimit:      "11",
		ExtraordinaryMedicalBenefitsLimit: "12",
		PipLimit:                          "13",
		UmuimDeductible:                   "14",
	}

	got, err := NewCreatorFn(SetFuneralExpenseBenefitsFields)(s.ctx, s.programContext, input)
	s.Require().NoError(err)
	s.Require().Equal(expected, got)
}

func (s *NewFnTestSuite) Test_WithSetAccidentalDeathBenefitsFields_WithMissingAccidentalDeathBenefitsLimit() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_AccidentalDeathBenefits,
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	got, err := NewCreatorFn(SetAccidentalDeathBenefitsFields)(s.ctx, s.programContext, input)
	s.Require().Error(err)
	s.Require().Regexp("failed to obtain Accidental Death Benefits limit", err.Error())
	s.Require().Nil(got)
}

func (s *NewFnTestSuite) Test_WithSetAccidentalDeathBenefits_WithSuccess() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_AccidentalDeathBenefits,
				},
				LimitSpecs: []*ptypes.LimitSpec{
					{
						SubCoverageGroup: business_auto.AccidentalDeathBenefitsSubCoverageGroup,
						Cadence:          ptypes.LimitCadenceType_LimitCadenceType_Aggregate,
						Amount:           50000,
					},
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	expected := &entities.Coverage{
		Id:                                 "coverage",
		PolicyEffectiveDate:                20230615,
		LiabScheduleMod:                    1.0,
		LiabLossFreeMod:                    1.0,
		LiabExperienceRatingMod:            1.0,
		PdScheduleMod:                      1.0,
		PdLossFreeMod:                      1.0,
		PdExperienceRatingMod:              1.0,
		LiabLimit:                          "1",
		MedPayLimit:                        "2",
		UmbiLimit:                          "3",
		UimbiLimit:                         "4",
		HiredAutoPDDeductible:              "5",
		UmLimit:                            "6",
		HasAccidentalDeathBenefitsCoverage: true,
		UmuimLimit:                         "7",
		MedicalExpenseBenefitsLimit:        "8",
		WorkLossBenefitsLimit:              "9",
		FuneralExpenseBenefitsLimit:        "10",
		AccidentalDeathBenefitsLimit:       "50000",
		ExtraordinaryMedicalBenefitsLimit:  "12",
		PipLimit:                           "13",
		UmuimDeductible:                    "14",
	}

	got, err := NewCreatorFn(SetAccidentalDeathBenefitsFields)(s.ctx, s.programContext, input)
	s.Require().NoError(err)
	s.Require().Equal(expected, got)
}

func (s *NewFnTestSuite) Test_WithSetExtraordinaryMedicalBenefitsFields_WithMissingExtraordinaryMedicalBenefitsFieldsLimit() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_ExtraordinaryMedicalBenefits,
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	got, err := NewCreatorFn(SetExtraordinaryMedicalBenefitsFields)(s.ctx, s.programContext, input)
	s.Require().Error(err)
	s.Require().Regexp("failed to obtain Extraordinary Medical Benefits limit", err.Error())
	s.Require().Nil(got)
}

func (s *NewFnTestSuite) Test_WithSetExtraordinaryMedicalBenefitsFields_WithSuccess() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_ExtraordinaryMedicalBenefits,
				},
				LimitSpecs: []*ptypes.LimitSpec{
					{
						SubCoverageGroup: business_auto.ExtraordinaryMedicalBenefitsSubCoverageGroup,
						Cadence:          ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
						Amount:           25000,
					},
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	expected := &entities.Coverage{
		Id:                                      "coverage",
		PolicyEffectiveDate:                     20230615,
		LiabScheduleMod:                         1.0,
		LiabLossFreeMod:                         1.0,
		LiabExperienceRatingMod:                 1.0,
		PdScheduleMod:                           1.0,
		PdLossFreeMod:                           1.0,
		PdExperienceRatingMod:                   1.0,
		LiabLimit:                               "1",
		MedPayLimit:                             "2",
		UmbiLimit:                               "3",
		UimbiLimit:                              "4",
		HiredAutoPDDeductible:                   "5",
		UmLimit:                                 "6",
		HasExtraordinaryMedicalBenefitsCoverage: true,
		UmuimLimit:                              "7",
		MedicalExpenseBenefitsLimit:             "8",
		WorkLossBenefitsLimit:                   "9",
		FuneralExpenseBenefitsLimit:             "10",
		AccidentalDeathBenefitsLimit:            "11",
		ExtraordinaryMedicalBenefitsLimit:       "25000",
		PipLimit:                                "13",
		UmuimDeductible:                         "14",
	}

	got, err := NewCreatorFn(SetExtraordinaryMedicalBenefitsFields)(s.ctx, s.programContext, input)
	s.Require().NoError(err)
	s.Require().Equal(expected, got)
}

func (s *NewFnTestSuite) Test_WithSetUMLimit_WithMissingUMLimit() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	vehicles := []*entities.Vehicle{
		{Id: "vin-1"},
		{Id: "vin-2", HasUMCoverage: true},
		{Id: "vin-3"},
	}

	for _, v := range vehicles {
		err := s.programContext.AddEntity(s.ctx, v)
		s.Require().NoError(err)
	}

	got, err := NewCreatorFn(SetUMLimit)(s.ctx, s.programContext, input)
	s.Require().Error(err)
	s.Require().Regexp("failed to obtain UM limit", err.Error())
	s.Require().Nil(got)
}

func (s *NewFnTestSuite) Test_WithSetUMLimit_WithSuccess() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
				},
				LimitSpecs: []*ptypes.LimitSpec{
					{
						SubCoverageGroup: business_auto.UMSubCoverageGroup,
						Amount:           25000,
						Cadence:          ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
					},
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	vehicles := []*entities.Vehicle{
		{Id: "vin-1"},
		{Id: "vin-2", HasUMCoverage: true},
		{Id: "vin-3"},
	}

	for _, v := range vehicles {
		err := s.programContext.AddEntity(s.ctx, v)
		s.Require().NoError(err)
	}

	expected := &entities.Coverage{
		Id:                                "coverage",
		PolicyEffectiveDate:               20230615,
		LiabScheduleMod:                   1.0,
		LiabLossFreeMod:                   1.0,
		LiabExperienceRatingMod:           1.0,
		PdScheduleMod:                     1.0,
		PdLossFreeMod:                     1.0,
		PdExperienceRatingMod:             1.0,
		LiabLimit:                         "1",
		MedPayLimit:                       "2",
		UmbiLimit:                         "3",
		UimbiLimit:                        "4",
		HiredAutoPDDeductible:             "5",
		UmLimit:                           "25000",
		UmuimLimit:                        "7",
		MedicalExpenseBenefitsLimit:       "8",
		WorkLossBenefitsLimit:             "9",
		FuneralExpenseBenefitsLimit:       "10",
		AccidentalDeathBenefitsLimit:      "11",
		ExtraordinaryMedicalBenefitsLimit: "12",
		PipLimit:                          "13",
		UmuimDeductible:                   "14",
	}

	got, err := NewCreatorFn(SetUMLimit)(s.ctx, s.programContext, input)
	s.Require().NoError(err)
	s.Require().Equal(expected, got)
}

func (s *NewFnTestSuite) Test_WithSetUMStackedFlag_WithOnlyUIMBILimit_Stacked_Success() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
				},
				LimitSpecs: []*ptypes.LimitSpec{
					{
						SubCoverageGroup: business_auto.UIMBISubCoverageGroup,
						Cadence:          ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
						Amount:           200000,
						Stacked:          true,
					},
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	expected := &entities.Coverage{
		Id:                                "coverage",
		PolicyEffectiveDate:               20230615,
		LiabScheduleMod:                   1.0,
		LiabLossFreeMod:                   1.0,
		LiabExperienceRatingMod:           1.0,
		PdScheduleMod:                     1.0,
		PdLossFreeMod:                     1.0,
		PdExperienceRatingMod:             1.0,
		LiabLimit:                         "1",
		MedPayLimit:                       "2",
		UmbiLimit:                         "3",
		UimbiLimit:                        "4",
		HiredAutoPDDeductible:             "5",
		UmLimit:                           "6",
		UmuimLimit:                        "7",
		MedicalExpenseBenefitsLimit:       "8",
		WorkLossBenefitsLimit:             "9",
		FuneralExpenseBenefitsLimit:       "10",
		AccidentalDeathBenefitsLimit:      "11",
		ExtraordinaryMedicalBenefitsLimit: "12",
		PipLimit:                          "13",
		UmuimDeductible:                   "14",
		AreUMLimitsStacked:                true,
	}

	got, err := NewCreatorFn(SetUMLimitsStackedFlag)(s.ctx, s.programContext, input)
	s.Require().NoError(err)
	s.Require().Equal(expected, got)
}

func (s *NewFnTestSuite) Test_WithSetUMStackedFlag_WithOnlyUIMBILimit_NotStacked_Success() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
				},
				LimitSpecs: []*ptypes.LimitSpec{
					{
						SubCoverageGroup: business_auto.UIMBISubCoverageGroup,
						Cadence:          ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
						Amount:           200000,
						Stacked:          false,
					},
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	expected := &entities.Coverage{
		Id:                                "coverage",
		PolicyEffectiveDate:               20230615,
		LiabScheduleMod:                   1.0,
		LiabLossFreeMod:                   1.0,
		LiabExperienceRatingMod:           1.0,
		PdScheduleMod:                     1.0,
		PdLossFreeMod:                     1.0,
		PdExperienceRatingMod:             1.0,
		LiabLimit:                         "1",
		MedPayLimit:                       "2",
		UmbiLimit:                         "3",
		UimbiLimit:                        "4",
		HiredAutoPDDeductible:             "5",
		UmLimit:                           "6",
		UmuimLimit:                        "7",
		MedicalExpenseBenefitsLimit:       "8",
		WorkLossBenefitsLimit:             "9",
		FuneralExpenseBenefitsLimit:       "10",
		AccidentalDeathBenefitsLimit:      "11",
		ExtraordinaryMedicalBenefitsLimit: "12",
		PipLimit:                          "13",
		UmuimDeductible:                   "14",
		AreUMLimitsStacked:                false,
	}

	got, err := NewCreatorFn(SetUMLimitsStackedFlag)(s.ctx, s.programContext, input)
	s.Require().NoError(err)
	s.Require().Equal(expected, got)
}

func (s *NewFnTestSuite) Test_WithSetUMStackedFlag_WithOnlyUMBILimit_Stacked_Success() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
				},
				LimitSpecs: []*ptypes.LimitSpec{
					{
						SubCoverageGroup: business_auto.UMBISubCoverageGroup,
						Cadence:          ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
						Amount:           200000,
						Stacked:          true,
					},
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	expected := &entities.Coverage{
		Id:                                "coverage",
		PolicyEffectiveDate:               20230615,
		LiabScheduleMod:                   1.0,
		LiabLossFreeMod:                   1.0,
		LiabExperienceRatingMod:           1.0,
		PdScheduleMod:                     1.0,
		PdLossFreeMod:                     1.0,
		PdExperienceRatingMod:             1.0,
		LiabLimit:                         "1",
		MedPayLimit:                       "2",
		UmbiLimit:                         "3",
		UimbiLimit:                        "4",
		HiredAutoPDDeductible:             "5",
		UmLimit:                           "6",
		UmuimLimit:                        "7",
		MedicalExpenseBenefitsLimit:       "8",
		WorkLossBenefitsLimit:             "9",
		FuneralExpenseBenefitsLimit:       "10",
		AccidentalDeathBenefitsLimit:      "11",
		ExtraordinaryMedicalBenefitsLimit: "12",
		PipLimit:                          "13",
		UmuimDeductible:                   "14",
		AreUMLimitsStacked:                true,
	}

	got, err := NewCreatorFn(SetUMLimitsStackedFlag)(s.ctx, s.programContext, input)
	s.Require().NoError(err)
	s.Require().Equal(expected, got)
}

func (s *NewFnTestSuite) Test_WithSetUMStackedFlag_WithOnlyUMBILimit_NotStacked_Success() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
				},
				LimitSpecs: []*ptypes.LimitSpec{
					{
						SubCoverageGroup: business_auto.UMBISubCoverageGroup,
						Cadence:          ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
						Amount:           200000,
						Stacked:          false,
					},
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	expected := &entities.Coverage{
		Id:                                "coverage",
		PolicyEffectiveDate:               20230615,
		LiabScheduleMod:                   1.0,
		LiabLossFreeMod:                   1.0,
		LiabExperienceRatingMod:           1.0,
		PdScheduleMod:                     1.0,
		PdLossFreeMod:                     1.0,
		PdExperienceRatingMod:             1.0,
		LiabLimit:                         "1",
		MedPayLimit:                       "2",
		UmbiLimit:                         "3",
		UimbiLimit:                        "4",
		HiredAutoPDDeductible:             "5",
		UmLimit:                           "6",
		UmuimLimit:                        "7",
		MedicalExpenseBenefitsLimit:       "8",
		WorkLossBenefitsLimit:             "9",
		FuneralExpenseBenefitsLimit:       "10",
		AccidentalDeathBenefitsLimit:      "11",
		ExtraordinaryMedicalBenefitsLimit: "12",
		PipLimit:                          "13",
		UmuimDeductible:                   "14",
		AreUMLimitsStacked:                false,
	}

	got, err := NewCreatorFn(SetUMLimitsStackedFlag)(s.ctx, s.programContext, input)
	s.Require().NoError(err)
	s.Require().Equal(expected, got)
}

func (s *NewFnTestSuite) Test_WithSetUMStackedFlag_WithoutAnyUMs() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	expected := &entities.Coverage{
		Id:                                "coverage",
		PolicyEffectiveDate:               20230615,
		LiabScheduleMod:                   1.0,
		LiabLossFreeMod:                   1.0,
		LiabExperienceRatingMod:           1.0,
		PdScheduleMod:                     1.0,
		PdLossFreeMod:                     1.0,
		PdExperienceRatingMod:             1.0,
		LiabLimit:                         "1",
		MedPayLimit:                       "2",
		UmbiLimit:                         "3",
		UimbiLimit:                        "4",
		HiredAutoPDDeductible:             "5",
		UmLimit:                           "6",
		UmuimLimit:                        "7",
		MedicalExpenseBenefitsLimit:       "8",
		WorkLossBenefitsLimit:             "9",
		FuneralExpenseBenefitsLimit:       "10",
		AccidentalDeathBenefitsLimit:      "11",
		ExtraordinaryMedicalBenefitsLimit: "12",
		PipLimit:                          "13",
		UmuimDeductible:                   "14",
		AreUMLimitsStacked:                false,
	}

	got, err := NewCreatorFn(SetUMLimitsStackedFlag)(s.ctx, s.programContext, input)
	s.Require().NoError(err)
	s.Require().Equal(expected, got)
}

func (s *NewFnTestSuite) Test_WithSetUMStackedFlag_WithMismatchedStackingConfiguration() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
					ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
				},
				LimitSpecs: []*ptypes.LimitSpec{
					{
						SubCoverageGroup: business_auto.UMBISubCoverageGroup,
						Cadence:          ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
						Amount:           200000,
						Stacked:          true,
					},
					{
						SubCoverageGroup: business_auto.UIMBISubCoverageGroup,
						Cadence:          ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
						Amount:           200000,
						Stacked:          false,
					},
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	got, err := NewCreatorFn(SetUMLimitsStackedFlag)(s.ctx, s.programContext, input)
	s.Require().Error(err)
	s.Require().Regexp("UMBI and UIMBI limits must have the same stacking configuration", err.Error())
	s.Require().Nil(got)
}

func (s *NewFnTestSuite) Test_WithSetUMStackedFlag_WithBothLimitsStacked_Success() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
					ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
				},
				LimitSpecs: []*ptypes.LimitSpec{
					{
						SubCoverageGroup: business_auto.UMBISubCoverageGroup,
						Cadence:          ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
						Amount:           200000,
						Stacked:          true,
					},
					{
						SubCoverageGroup: business_auto.UIMBISubCoverageGroup,
						Cadence:          ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
						Amount:           200000,
						Stacked:          true,
					},
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	expected := &entities.Coverage{
		Id:                                "coverage",
		PolicyEffectiveDate:               20230615,
		LiabScheduleMod:                   1.0,
		LiabLossFreeMod:                   1.0,
		LiabExperienceRatingMod:           1.0,
		PdScheduleMod:                     1.0,
		PdLossFreeMod:                     1.0,
		PdExperienceRatingMod:             1.0,
		LiabLimit:                         "1",
		MedPayLimit:                       "2",
		UmbiLimit:                         "3",
		UimbiLimit:                        "4",
		HiredAutoPDDeductible:             "5",
		UmLimit:                           "6",
		UmuimLimit:                        "7",
		MedicalExpenseBenefitsLimit:       "8",
		WorkLossBenefitsLimit:             "9",
		FuneralExpenseBenefitsLimit:       "10",
		AccidentalDeathBenefitsLimit:      "11",
		ExtraordinaryMedicalBenefitsLimit: "12",
		PipLimit:                          "13",
		UmuimDeductible:                   "14",
		AreUMLimitsStacked:                true,
	}

	got, err := NewCreatorFn(SetUMLimitsStackedFlag)(s.ctx, s.programContext, input)
	s.Require().NoError(err)
	s.Require().Equal(expected, got)
}

func (s *NewFnTestSuite) Test_WithSetUMStackedFlag_WithBothLimitsNotStacked_Success() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
					ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
				},
				LimitSpecs: []*ptypes.LimitSpec{
					{
						SubCoverageGroup: business_auto.UMBISubCoverageGroup,
						Cadence:          ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
						Amount:           200000,
						Stacked:          false,
					},
					{
						SubCoverageGroup: business_auto.UIMBISubCoverageGroup,
						Cadence:          ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
						Amount:           200000,
						Stacked:          false,
					},
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	expected := &entities.Coverage{
		Id:                                "coverage",
		PolicyEffectiveDate:               20230615,
		LiabScheduleMod:                   1.0,
		LiabLossFreeMod:                   1.0,
		LiabExperienceRatingMod:           1.0,
		PdScheduleMod:                     1.0,
		PdLossFreeMod:                     1.0,
		PdExperienceRatingMod:             1.0,
		LiabLimit:                         "1",
		MedPayLimit:                       "2",
		UmbiLimit:                         "3",
		UimbiLimit:                        "4",
		HiredAutoPDDeductible:             "5",
		UmLimit:                           "6",
		UmuimLimit:                        "7",
		MedicalExpenseBenefitsLimit:       "8",
		WorkLossBenefitsLimit:             "9",
		FuneralExpenseBenefitsLimit:       "10",
		AccidentalDeathBenefitsLimit:      "11",
		ExtraordinaryMedicalBenefitsLimit: "12",
		PipLimit:                          "13",
		UmuimDeductible:                   "14",
		AreUMLimitsStacked:                false,
	}

	got, err := NewCreatorFn(SetUMLimitsStackedFlag)(s.ctx, s.programContext, input)
	s.Require().NoError(err)
	s.Require().Equal(expected, got)
}

func (s *NewFnTestSuite) Test_WithSetPipFields_WithMissingPIPLimit() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_MedicalExpenseBenefits,
					ptypes.SubCoverageType_SubCoverageType_FuneralExpenseBenefits,
					ptypes.SubCoverageType_SubCoverageType_WorkLossBenefits,
					ptypes.SubCoverageType_SubCoverageType_EssentialServiceExpenses},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	got, err := NewCreatorFn(SetPIPFields)(s.ctx, s.programContext, input)
	s.Require().Error(err)
	s.Require().Regexp("failed to obtain PIP limit", err.Error())
	s.Require().Nil(got)
}

func (s *NewFnTestSuite) Test_WithSetPipFields_WithSuccess() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_MedicalExpenseBenefits,
					ptypes.SubCoverageType_SubCoverageType_FuneralExpenseBenefits,
					ptypes.SubCoverageType_SubCoverageType_WorkLossBenefits,
					ptypes.SubCoverageType_SubCoverageType_EssentialServiceExpenses},
				LimitSpecs: []*ptypes.LimitSpec{
					{
						SubCoverageGroup: business_auto.PIPSubCoverageGroup,
						Cadence:          ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
						Amount:           15000,
					},
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	expected := &entities.Coverage{
		Id:                                "coverage",
		PolicyEffectiveDate:               20230615,
		HasPIPCoverage:                    true,
		LiabScheduleMod:                   1.0,
		LiabLossFreeMod:                   1.0,
		LiabExperienceRatingMod:           1.0,
		PdScheduleMod:                     1.0,
		PdLossFreeMod:                     1.0,
		PdExperienceRatingMod:             1.0,
		LiabLimit:                         "1",
		MedPayLimit:                       "2",
		UmbiLimit:                         "3",
		UimbiLimit:                        "4",
		HiredAutoPDDeductible:             "5",
		UmLimit:                           "6",
		UmuimLimit:                        "7",
		MedicalExpenseBenefitsLimit:       "8",
		WorkLossBenefitsLimit:             "9",
		FuneralExpenseBenefitsLimit:       "10",
		AccidentalDeathBenefitsLimit:      "11",
		ExtraordinaryMedicalBenefitsLimit: "12",
		PipLimit:                          "15000",
		UmuimDeductible:                   "14",
	}

	got, err := NewCreatorFn(SetPIPFields)(s.ctx, s.programContext, input)
	s.Require().NoError(err)
	s.Require().Equal(expected, got)
}

func (s *NewFnTestSuite) Test_WithSetUMUIMLimitsAddedOnFlag_WithAddedOnTrue() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
					ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
					ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage,
					ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristPropertyDamage,
				},
				LimitSpecs: []*ptypes.LimitSpec{
					{
						SubCoverageGroup: business_auto.UMUIMSubCoverageGroup,
						Cadence:          ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
						Amount:           300000,
						AddedOn:          true,
					},
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	expected := &entities.Coverage{
		Id:                                "coverage",
		PolicyEffectiveDate:               20230615,
		LiabScheduleMod:                   1.0,
		LiabLossFreeMod:                   1.0,
		LiabExperienceRatingMod:           1.0,
		PdScheduleMod:                     1.0,
		PdLossFreeMod:                     1.0,
		PdExperienceRatingMod:             1.0,
		LiabLimit:                         "1",
		MedPayLimit:                       "2",
		UmbiLimit:                         "3",
		UimbiLimit:                        "4",
		HiredAutoPDDeductible:             "5",
		UmLimit:                           "6",
		UmuimLimit:                        "7",
		MedicalExpenseBenefitsLimit:       "8",
		WorkLossBenefitsLimit:             "9",
		FuneralExpenseBenefitsLimit:       "10",
		AccidentalDeathBenefitsLimit:      "11",
		ExtraordinaryMedicalBenefitsLimit: "12",
		PipLimit:                          "13",
		UmuimDeductible:                   "14",
		AreUMUIMLimitsAddedOn:             true,
	}

	got, err := NewCreatorFn(SetUMUIMLimitsAddedOnFlag)(s.ctx, s.programContext, input)
	s.Require().NoError(err)
	s.Require().Equal(expected, got)
}

func (s *NewFnTestSuite) Test_WithSetUMUIMLimitsAddedOnFlag_WithAddedOnFalse() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
					ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
					ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage,
					ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristPropertyDamage,
				},
				LimitSpecs: []*ptypes.LimitSpec{
					{
						SubCoverageGroup: business_auto.UMUIMSubCoverageGroup,
						Cadence:          ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
						Amount:           250000,
						AddedOn:          false,
					},
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	expected := &entities.Coverage{
		Id:                                "coverage",
		PolicyEffectiveDate:               20230615,
		LiabScheduleMod:                   1.0,
		LiabLossFreeMod:                   1.0,
		LiabExperienceRatingMod:           1.0,
		PdScheduleMod:                     1.0,
		PdLossFreeMod:                     1.0,
		PdExperienceRatingMod:             1.0,
		LiabLimit:                         "1",
		MedPayLimit:                       "2",
		UmbiLimit:                         "3",
		UimbiLimit:                        "4",
		HiredAutoPDDeductible:             "5",
		UmLimit:                           "6",
		UmuimLimit:                        "7",
		MedicalExpenseBenefitsLimit:       "8",
		WorkLossBenefitsLimit:             "9",
		FuneralExpenseBenefitsLimit:       "10",
		AccidentalDeathBenefitsLimit:      "11",
		ExtraordinaryMedicalBenefitsLimit: "12",
		PipLimit:                          "13",
		UmuimDeductible:                   "14",
		AreUMUIMLimitsAddedOn:             false,
	}

	got, err := NewCreatorFn(SetUMUIMLimitsAddedOnFlag)(s.ctx, s.programContext, input)
	s.Require().NoError(err)
	s.Require().Equal(expected, got)
}

func (s *NewFnTestSuite) Test_WithSetUMUIMLimitsAddedOnFlag_WithoutUMUIMCoverage() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	expected := &entities.Coverage{
		Id:                                "coverage",
		PolicyEffectiveDate:               20230615,
		LiabScheduleMod:                   1.0,
		LiabLossFreeMod:                   1.0,
		LiabExperienceRatingMod:           1.0,
		PdScheduleMod:                     1.0,
		PdLossFreeMod:                     1.0,
		PdExperienceRatingMod:             1.0,
		LiabLimit:                         "1",
		MedPayLimit:                       "2",
		UmbiLimit:                         "3",
		UimbiLimit:                        "4",
		HiredAutoPDDeductible:             "5",
		UmLimit:                           "6",
		UmuimLimit:                        "7",
		MedicalExpenseBenefitsLimit:       "8",
		WorkLossBenefitsLimit:             "9",
		FuneralExpenseBenefitsLimit:       "10",
		AccidentalDeathBenefitsLimit:      "11",
		ExtraordinaryMedicalBenefitsLimit: "12",
		PipLimit:                          "13",
		UmuimDeductible:                   "14",
	}

	got, err := NewCreatorFn(SetUMUIMLimitsAddedOnFlag)(s.ctx, s.programContext, input)
	s.Require().NoError(err)
	s.Require().Equal(expected, got)
}

func (s *NewFnTestSuite) Test_WithSetUMUIMLimitsAddedOnFlag_WithMissingLimitSpec() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
					ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
					ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage,
					ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristPropertyDamage,
				},
				// No LimitSpecs provided
				LimitSpecs: []*ptypes.LimitSpec{},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	got, err := NewCreatorFn(SetUMUIMLimitsAddedOnFlag)(s.ctx, s.programContext, input)
	s.Require().Error(err)
	s.Require().Regexp("failed to obtain UMUIM limit for AddedOn flag", err.Error())
	s.Require().Nil(got)
}

func (s *NewFnTestSuite) Test_WithSetUMUIMDeductible_WithoutUMUIMCoverage() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	expected := &entities.Coverage{
		Id:                                "coverage",
		PolicyEffectiveDate:               20230615,
		LiabScheduleMod:                   1.0,
		LiabLossFreeMod:                   1.0,
		LiabExperienceRatingMod:           1.0,
		PdScheduleMod:                     1.0,
		PdLossFreeMod:                     1.0,
		PdExperienceRatingMod:             1.0,
		LiabLimit:                         "1",
		MedPayLimit:                       "2",
		UmbiLimit:                         "3",
		UimbiLimit:                        "4",
		HiredAutoPDDeductible:             "5",
		UmLimit:                           "6",
		UmuimLimit:                        "7",
		MedicalExpenseBenefitsLimit:       "8",
		WorkLossBenefitsLimit:             "9",
		FuneralExpenseBenefitsLimit:       "10",
		AccidentalDeathBenefitsLimit:      "11",
		ExtraordinaryMedicalBenefitsLimit: "12",
		PipLimit:                          "13",
		UmuimDeductible:                   "14",
	}

	got, err := NewCreatorFn(SetUMUIMDeductible)(s.ctx, s.programContext, input)
	s.Require().NoError(err)
	s.Require().Equal(expected, got)
}

func (s *NewFnTestSuite) Test_WithSetUMUIMDeductible_WithMissingDeductibleSpec() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
					ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
					ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage,
					ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristPropertyDamage,
				},
				DeductibleSpecs: []*ptypes.DeductibleSpec{},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	got, err := NewCreatorFn(SetUMUIMDeductible)(s.ctx, s.programContext, input)
	s.Require().Error(err)
	s.Require().Regexp("failed to obtain UMUIM deductible", err.Error())
	s.Require().Nil(got)
}

func (s *NewFnTestSuite) Test_WithSetUMUIMDeductible_WithSuccess() {
	policyEffectiveDate := time.Date(2023, 6, 15, 0, 0, 0, 0, time.UTC)

	input := &creator_functions.Input{
		Input: adaptors.Input{
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
					ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
					ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage,
					ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristPropertyDamage,
				},
				DeductibleSpecs: []*ptypes.DeductibleSpec{
					{
						SubCoverageGroup: business_auto.UMUIMSubCoverageGroup,
						Amount:           500,
					},
				},
				Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
					BusinessAutoPolicyChunkSpecData: &ptypes.BusinessAuto_PolicyChunkSpecData{
						Company: &ptypes.BusinessAuto_Company{},
					},
				},
			},
			PolicyOriginalDates: &proto.Interval{
				Start: timestamppb.New(policyEffectiveDate),
				End:   timestamppb.New(policyEffectiveDate.AddDate(1, 0, 0)),
			},
		},
	}

	expected := &entities.Coverage{
		Id:                                "coverage",
		PolicyEffectiveDate:               20230615,
		LiabScheduleMod:                   1.0,
		LiabLossFreeMod:                   1.0,
		LiabExperienceRatingMod:           1.0,
		PdScheduleMod:                     1.0,
		PdLossFreeMod:                     1.0,
		PdExperienceRatingMod:             1.0,
		LiabLimit:                         "1",
		MedPayLimit:                       "2",
		UmbiLimit:                         "3",
		UimbiLimit:                        "4",
		HiredAutoPDDeductible:             "5",
		UmLimit:                           "6",
		UmuimLimit:                        "7",
		MedicalExpenseBenefitsLimit:       "8",
		WorkLossBenefitsLimit:             "9",
		FuneralExpenseBenefitsLimit:       "10",
		AccidentalDeathBenefitsLimit:      "11",
		ExtraordinaryMedicalBenefitsLimit: "12",
		PipLimit:                          "13",
		UmuimDeductible:                   "500",
	}

	got, err := NewCreatorFn(SetUMUIMDeductible)(s.ctx, s.programContext, input)
	s.Require().NoError(err)
	s.Require().Equal(expected, got)
}
