package entities

import (
	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/rating/adaptors/common"
)

type OutputsQuote struct {
	Id string

	QuoteData string `rml:"reference,QuoteData"`

	// Output bindings
	// FleetPowerUnitCount holds the number of power units for the fleet.
	FleetPowerUnitCount float64 `rml:"float,number.decimal,output"`

	// Tiv refers to the Total Insured Value, or in other words, the sum of the
	// stated values of all the equipment units uploaded.
	Tiv float64 `rml:"float,number.decimal,output"`

	// SafetyDiscountPercentDisplay exposes the Nirvana Safety discount for
	// this application. Positive values equal savings; eg 0.05 = 5% savings,
	// -0.05 = 5% surcharge.
	SafetyDiscountPercentDisplay float64 `rml:"float,number.decimal,output"`

	TotalSurchargePremium float64  `rml:"float,number.decimal,output"`
	ByPuSurchargePremium  *float64 `rml:"float,number.decimal,output"`

	NegotiatedRateFlag       bool    `rml:"bool,boolean,output"`
	Rule15ExemptionFlag      bool    `rml:"bool,boolean,output"`
	Rule15ExemptionThreshold float64 `rml:"float,number.decimal,output"`
	Rule15ExemptionPremium   float64 `rml:"float,number.decimal,output"`

	TotalSurchargePremiumMi *float64 `rml:"float,number.decimal,output"`

	TotalByCovSurchargePremium *float64 `rml:"float,number.decimal,output"`

	CoverageModPremiumUmpdSurcharge float64 `rml:"float,number.decimal,output"`

	PerEndtPremiumBipdSpecifiedAdditionalInsured    float64 `rml:"float,number.decimal,output"`
	PerEndtPremiumBipdSpecifiedPNCAdditionalInsured float64 `rml:"float,number.decimal,output"`
	PerEndtPremiumBipdSpecifiedWaiverOfSubrogation  float64 `rml:"float,number.decimal,output"`
	PolicyPremiumBipdBlanketAdditionalInsured       float64 `rml:"float,number.decimal,output"`
	PolicyPremiumBipdBlanketPNCAdditionalInsured    float64 `rml:"float,number.decimal,output"`
	PolicyPremiumBipdBlanketWaiverOfSubrogation     float64 `rml:"float,number.decimal,output"`

	PerEndtPremiumGlSpecifiedAdditionalInsured    float64 `rml:"float,number.decimal,output"`
	PerEndtPremiumGlSpecifiedPNCAdditionalInsured float64 `rml:"float,number.decimal,output"`
	PerEndtPremiumGlSpecifiedWaiverOfSubrogation  float64 `rml:"float,number.decimal,output"`
	PolicyPremiumGLBlanketAdditionalInsured       float64 `rml:"float,number.decimal,output"`
	PolicyPremiumGLBlanketPNCAdditionalInsured    float64 `rml:"float,number.decimal,output"`
	PolicyPremiumGLBlanketWaiverOfSubrogation     float64 `rml:"float,number.decimal,output"`

	PerEndtPremiumCargoSpecifiedAdditionalInsured   float64 `rml:"float,number.decimal,output"`
	PerEndtPremiumCargoSpecifiedWaiverOfSubrogation float64 `rml:"float,number.decimal,output"`
	PolicyPremiumCargoBlanketAdditionalInsured      float64 `rml:"float,number.decimal,output"`
	PolicyPremiumCargoBlanketWaiverOfSubrogation    float64 `rml:"float,number.decimal,output"`

	PolicyPremiumLiabBase           float64 `rml:"float,number.decimal,output"`
	PolicyPremiumMedPay             float64 `rml:"float,number.decimal,output"`
	PolicyPremiumPip                float64 `rml:"float,number.decimal,output"`
	PolicyPremiumGuestPip           float64 `rml:"float,number.decimal,output"`
	PolicyPremiumPipEac             float64 `rml:"float,number.decimal,output"`
	PolicyPremiumPpi                float64 `rml:"float,number.decimal,output"`
	PolicyPremiumBroadenedPollution float64 `rml:"float,number.decimal,output"`
	PolicyPremiumUm                 float64 `rml:"float,number.decimal,output"`
	PolicyPremiumUim                float64 `rml:"float,number.decimal,output"`
	PolicyPremiumUmUim              float64 `rml:"float,number.decimal,output"`
	PolicyPremiumUmpd               float64 `rml:"float,number.decimal,output"`

	PolicyPremiumCollBase           float64 `rml:"float,number.decimal,output"`
	PolicyPremiumCompBase           float64 `rml:"float,number.decimal,output"`
	PolicyPremiumTrailerInterchange float64 `rml:"float,number.decimal,output"`
	PolicyPremiumCompleteApdPackage float64 `rml:"float,number.decimal,output"`
	PolicyPremiumStandardApdPackage float64 `rml:"float,number.decimal,output"`

	PolicyPremiumGlBase    float64 `rml:"float,number.decimal,output"`
	PolicyPremiumGlStopGap float64 `rml:"float,number.decimal,output"`

	PolicyPremiumMtcBase                       float64 `rml:"float,number.decimal,output"`
	PolicyPremiumReeferBreakdown               float64 `rml:"float,number.decimal,output"`
	PolicyPremiumReeferBreakdownWithHumanError float64 `rml:"float,number.decimal,output"`
	PolicyPremiumCargoAtTerminal               float64 `rml:"float,number.decimal,output"`
	PolicyPremiumMtcTrailerInterchange         float64 `rml:"float,number.decimal,output"`

	// Complete Package
	CoverageFinalModPremiumCollComplete         *float64 `rml:"float,number.decimal,output"`
	CoverageFinalModPremiumCompComplete         *float64 `rml:"float,number.decimal,output"`
	TotalCompleteSurchargePremium               *float64 `rml:"float,number.decimal,output"`
	TotalCompleteSurchargePremiumKy             *float64 `rml:"float,number.decimal,output"`
	TotalCompleteSurpTaxandStampFee             *float64 `rml:"float,number.decimal,output"`
	TotalCompleteInsurancePremSurcharge         *float64 `rml:"float,number.decimal,output"`
	AutoCompleteInsurancePremSurcharge          *float64 `rml:"float,number.decimal,output"`
	GlCompleteInsurancePremSurcharge            *float64 `rml:"float,number.decimal,output"`
	MtcCompleteInsurancePremSurcharge           *float64 `rml:"float,number.decimal,output"`
	AutoCompleteLGPTSurcharge                   *float64 `rml:"float,number.decimal,output"`
	GlCompleteLGPTSurcharge                     *float64 `rml:"float,number.decimal,output"`
	MtcCompleteLGPTSurcharge                    *float64 `rml:"float,number.decimal,output"`
	LiabCompletePolicySurplusLinesTax           *float64 `rml:"float,number.decimal,output"`
	LiabCompletePolicyStampingFee               *float64 `rml:"float,number.decimal,output"`
	PhysCompletePolicySurplusLinesTax           *float64 `rml:"float,number.decimal,output"`
	PhysCompletePolicyStampingFee               *float64 `rml:"float,number.decimal,output"`
	GlCompletePolicySurplusLinesTax             *float64 `rml:"float,number.decimal,output"`
	GlCompletePolicyStampingFee                 *float64 `rml:"float,number.decimal,output"`
	MtcCompletePolicySurplusLinesTax            *float64 `rml:"float,number.decimal,output"`
	MtcCompletePolicyStampingFee                *float64 `rml:"float,number.decimal,output"`
	TotalCompletePolicyPremiumUnmodified        *float64 `rml:"float,number.decimal,output"`
	LiabCompletePolicyPremiumUnmodified         *float64 `rml:"float,number.decimal,output"`
	PhysCompletePolicyPremiumUnmodified         *float64 `rml:"float,number.decimal,output"`
	GlCompletePolicyPremiumUnmodified           *float64 `rml:"float,number.decimal,output"`
	MtcCompletePolicyPremiumUnmodified          *float64 `rml:"float,number.decimal,output"`
	SafetyDiscountPremiumCompletePackage        float64  `rml:"float,number.decimal,output"`
	TotalCompletePolicyPremium                  float64  `rml:"float,number.decimal,output"`
	LiabCompletePolicyPremium                   float64  `rml:"float,number.decimal,output"`
	LiabCompletePolicyPremiumPpu                float64  `rml:"float,number.decimal,output"`
	PhysCompletePolicyPremium                   float64  `rml:"float,number.decimal,output"`
	PhysCompletePolicyPremiumPtiv               float64  `rml:"float,number.decimal,output"`
	GlCompletePolicyPremium                     float64  `rml:"float,number.decimal,output"`
	MtcCompletePolicyPremium                    float64  `rml:"float,number.decimal,output"`
	MtcCompletePolicyPremiumPpu                 float64  `rml:"float,number.decimal,output"`
	FlatCompletePolicyPremium                   float64  `rml:"float,number.decimal,output"`
	NonFlatLiabCompletePolicyPremium            float64  `rml:"float,number.decimal,output"`
	NonFlatLiabCompletePolicyPremiumPpu         float64  `rml:"float,number.decimal,output"`
	NonFlatPhysCompletePolicyPremium            float64  `rml:"float,number.decimal,output"`
	NonFlatPhysCompletePolicyPremiumPtiv        float64  `rml:"float,number.decimal,output"`
	NegotiatedNonFlatLiabCompletePolicyPremium  float64  `rml:"float,number.decimal,output"`
	NegotiatedNonFlatPhysCompletePolicyPremium  float64  `rml:"float,number.decimal,output"`
	TraditionalNonFlatLiabCompletePolicyPremium float64  `rml:"float,number.decimal,output"`
	TraditionalNonFlatPhysCompletePolicyPremium float64  `rml:"float,number.decimal,output"`
	TotalALCompletePolicyPremium                float64  `rml:"float,number.decimal,output"`
	LiabALCompletePolicyPremiumPpu              float64  `rml:"float,number.decimal,output"`
	AlSafetyDiscountCompletePackage             float64  `rml:"float,number.decimal,output"`
	TotalALPDCompletePolicyPremium              float64  `rml:"float,number.decimal,output"`
	LiabALPDCompletePolicyPremium               float64  `rml:"float,number.decimal,output"`
	PhysALPDCompletePolicyPremium               float64  `rml:"float,number.decimal,output"`
	LiabALPDCompletePolicyPremiumPpu            float64  `rml:"float,number.decimal,output"`
	PhysALPDCompletePolicyPremiumPptiv          float64  `rml:"float,number.decimal,output"`
	AlpdSafetyDiscountCompletePackage           float64  `rml:"float,number.decimal,output"`
	NonFlatALCompletePolicyPremium              float64  `rml:"float,number.decimal,output"`
	NonFlatALCompletePolicyPremiumPpu           float64  `rml:"float,number.decimal,output"`
	FlatALCompletePolicyPremium                 float64  `rml:"float,number.decimal,output"`
	NonFlatLiabAlpdCompletePolicyPremium        float64  `rml:"float,number.decimal,output"`
	NonFlatLiabAlpdCompletePolicyPremiumPpu     float64  `rml:"float,number.decimal,output"`
	NonFlatPhysAlpdCompletePolicyPremium        float64  `rml:"float,number.decimal,output"`
	NonFlatPhysAlpdCompletePolicyPremiumPtiv    float64  `rml:"float,number.decimal,output"`
	FlatAlpdCompletePolicyPremium               float64  `rml:"float,number.decimal,output"`
	LiabALCompletePolicyPremium                 float64  `rml:"float,number.decimal,output"`
	TotalGlCompletePolicyPremium                float64  `rml:"float,number.decimal,output"`

	// Standard Package
	CoverageFinalModPremiumCollStandard         *float64 `rml:"float,number.decimal,output"`
	CoverageFinalModPremiumCompStandard         *float64 `rml:"float,number.decimal,output"`
	TotalStandardSurchargePremium               *float64 `rml:"float,number.decimal,output"`
	TotalStandardSurchargePremiumKy             *float64 `rml:"float,number.decimal,output"`
	TotalStandardSurpTaxandStampFee             *float64 `rml:"float,number.decimal,output"`
	TotalStandardInsurancePremSurcharge         *float64 `rml:"float,number.decimal,output"`
	AutoStandardInsurancePremSurcharge          *float64 `rml:"float,number.decimal,output"`
	GlStandardInsurancePremSurcharge            *float64 `rml:"float,number.decimal,output"`
	MtcStandardInsurancePremSurcharge           *float64 `rml:"float,number.decimal,output"`
	AutoStandardLGPTSurcharge                   *float64 `rml:"float,number.decimal,output"`
	GlStandardLGPTSurcharge                     *float64 `rml:"float,number.decimal,output"`
	MtcStandardLGPTSurcharge                    *float64 `rml:"float,number.decimal,output"`
	LiabStandardPolicySurplusLinesTax           *float64 `rml:"float,number.decimal,output"`
	LiabStandardPolicyStampingFee               *float64 `rml:"float,number.decimal,output"`
	PhysStandardPolicySurplusLinesTax           *float64 `rml:"float,number.decimal,output"`
	PhysStandardPolicyStampingFee               *float64 `rml:"float,number.decimal,output"`
	GlStandardPolicySurplusLinesTax             *float64 `rml:"float,number.decimal,output"`
	GlStandardPolicyStampingFee                 *float64 `rml:"float,number.decimal,output"`
	MtcStandardPolicySurplusLinesTax            *float64 `rml:"float,number.decimal,output"`
	MtcStandardPolicyStampingFee                *float64 `rml:"float,number.decimal,output"`
	TotalStandardPolicyPremiumUnmodified        *float64 `rml:"float,number.decimal,output"`
	LiabStandardPolicyPremiumUnmodified         *float64 `rml:"float,number.decimal,output"`
	PhysStandardPolicyPremiumUnmodified         *float64 `rml:"float,number.decimal,output"`
	GlStandardPolicyPremiumUnmodified           *float64 `rml:"float,number.decimal,output"`
	MtcStandardPolicyPremiumUnmodified          *float64 `rml:"float,number.decimal,output"`
	SafetyDiscountPremiumStandardPackage        float64  `rml:"float,number.decimal,output"`
	TotalStandardPolicyPremium                  float64  `rml:"float,number.decimal,output"`
	LiabStandardPolicyPremium                   float64  `rml:"float,number.decimal,output"`
	LiabStandardPolicyPremiumPpu                float64  `rml:"float,number.decimal,output"`
	PhysStandardPolicyPremium                   float64  `rml:"float,number.decimal,output"`
	PhysStandardPolicyPremiumPtiv               float64  `rml:"float,number.decimal,output"`
	GlStandardPolicyPremium                     float64  `rml:"float,number.decimal,output"`
	MtcStandardPolicyPremium                    float64  `rml:"float,number.decimal,output"`
	MtcStandardPolicyPremiumPpu                 float64  `rml:"float,number.decimal,output"`
	FlatStandardPolicyPremium                   float64  `rml:"float,number.decimal,output"`
	NonFlatLiabStandardPolicyPremium            float64  `rml:"float,number.decimal,output"`
	NonFlatLiabStandardPolicyPremiumPpu         float64  `rml:"float,number.decimal,output"`
	NonFlatPhysStandardPolicyPremium            float64  `rml:"float,number.decimal,output"`
	NonFlatPhysStandardPolicyPremiumPtiv        float64  `rml:"float,number.decimal,output"`
	NegotiatedNonFlatLiabStandardPolicyPremium  float64  `rml:"float,number.decimal,output"`
	NegotiatedNonFlatPhysStandardPolicyPremium  float64  `rml:"float,number.decimal,output"`
	TraditionalNonFlatLiabStandardPolicyPremium float64  `rml:"float,number.decimal,output"`
	TraditionalNonFlatPhysStandardPolicyPremium float64  `rml:"float,number.decimal,output"`
	TotalALStandardPolicyPremium                float64  `rml:"float,number.decimal,output"`
	LiabALStandardPolicyPremiumPpu              float64  `rml:"float,number.decimal,output"`
	AlSafetyDiscountStandardPackage             float64  `rml:"float,number.decimal,output"`
	TotalALPDStandardPolicyPremium              float64  `rml:"float,number.decimal,output"`
	LiabALPDStandardPolicyPremium               float64  `rml:"float,number.decimal,output"`
	PhysALPDStandardPolicyPremium               float64  `rml:"float,number.decimal,output"`
	LiabALPDStandardPolicyPremiumPpu            float64  `rml:"float,number.decimal,output"`
	PhysALPDStandardPolicyPremiumPptiv          float64  `rml:"float,number.decimal,output"`
	AlpdSafetyDiscountStandardPackage           float64  `rml:"float,number.decimal,output"`
	NonFlatALStandardPolicyPremium              float64  `rml:"float,number.decimal,output"`
	NonFlatALStandardPolicyPremiumPpu           float64  `rml:"float,number.decimal,output"`
	FlatALStandardPolicyPremium                 float64  `rml:"float,number.decimal,output"`
	NonFlatLiabAlpdStandardPolicyPremium        float64  `rml:"float,number.decimal,output"`
	NonFlatLiabAlpdStandardPolicyPremiumPpu     float64  `rml:"float,number.decimal,output"`
	NonFlatPhysAlpdStandardPolicyPremium        float64  `rml:"float,number.decimal,output"`
	NonFlatPhysAlpdStandardPolicyPremiumPtiv    float64  `rml:"float,number.decimal,output"`
	FlatAlpdStandardPolicyPremium               float64  `rml:"float,number.decimal,output"`
	LiabALStandardPolicyPremium                 float64  `rml:"float,number.decimal,output"`
	TotalGlStandardPolicyPremium                float64  `rml:"float,number.decimal,output"`

	// Basic Package
	SafetyDiscountPremiumBasicPackage        float64  `rml:"float,number.decimal,output"`
	TotalBasicPolicyPremium                  float64  `rml:"float,number.decimal,output"`
	LiabBasicPolicyPremium                   float64  `rml:"float,number.decimal,output"`
	LiabBasicPolicyPremiumPpu                float64  `rml:"float,number.decimal,output"`
	PhysBasicPolicyPremium                   float64  `rml:"float,number.decimal,output"`
	PhysBasicPolicyPremiumPtiv               float64  `rml:"float,number.decimal,output"`
	GlBasicPolicyPremium                     float64  `rml:"float,number.decimal,output"`
	MtcBasicPolicyPremium                    float64  `rml:"float,number.decimal,output"`
	MtcBasicPolicyPremiumPpu                 float64  `rml:"float,number.decimal,output"`
	FlatBasicPolicyPremium                   float64  `rml:"float,number.decimal,output"`
	NonFlatLiabBasicPolicyPremium            float64  `rml:"float,number.decimal,output"`
	NonFlatLiabBasicPolicyPremiumPpu         float64  `rml:"float,number.decimal,output"`
	NonFlatPhysBasicPolicyPremium            float64  `rml:"float,number.decimal,output"`
	NonFlatPhysBasicPolicyPremiumPtiv        float64  `rml:"float,number.decimal,output"`
	NegotiatedNonFlatLiabBasicPolicyPremium  float64  `rml:"float,number.decimal,output"`
	NegotiatedNonFlatPhysBasicPolicyPremium  float64  `rml:"float,number.decimal,output"`
	TraditionalNonFlatLiabBasicPolicyPremium float64  `rml:"float,number.decimal,output"`
	TraditionalNonFlatPhysBasicPolicyPremium float64  `rml:"float,number.decimal,output"`
	TotalALBasicPolicyPremium                float64  `rml:"float,number.decimal,output"`
	LiabALBasicPolicyPremiumPpu              float64  `rml:"float,number.decimal,output"`
	AlSafetyDiscountBasicPackage             float64  `rml:"float,number.decimal,output"`
	TotalALPDBasicPolicyPremium              float64  `rml:"float,number.decimal,output"`
	LiabALPDBasicPolicyPremium               float64  `rml:"float,number.decimal,output"`
	PhysALPDBasicPolicyPremium               float64  `rml:"float,number.decimal,output"`
	LiabALPDBasicPolicyPremiumPpu            float64  `rml:"float,number.decimal,output"`
	PhysALPDBasicPolicyPremiumPptiv          float64  `rml:"float,number.decimal,output"`
	AlpdSafetyDiscountBasicPackage           float64  `rml:"float,number.decimal,output"`
	NonFlatALBasicPolicyPremium              float64  `rml:"float,number.decimal,output"`
	NonFlatALBasicPolicyPremiumPpu           float64  `rml:"float,number.decimal,output"`
	FlatALBasicPolicyPremium                 float64  `rml:"float,number.decimal,output"`
	NonFlatLiabAlpdBasicPolicyPremium        float64  `rml:"float,number.decimal,output"`
	NonFlatLiabAlpdBasicPolicyPremiumPpu     float64  `rml:"float,number.decimal,output"`
	NonFlatPhysAlpdBasicPolicyPremium        float64  `rml:"float,number.decimal,output"`
	NonFlatPhysAlpdBasicPolicyPremiumPtiv    float64  `rml:"float,number.decimal,output"`
	FlatAlpdBasicPolicyPremium               float64  `rml:"float,number.decimal,output"`
	LiabALBasicPolicyPremium                 float64  `rml:"float,number.decimal,output"`
	TotalGlBasicPolicyPremium                float64  `rml:"float,number.decimal,output"`
	TotalBasicSurchargePremium               *float64 `rml:"float,number.decimal,output"`
	LiabBasicPolicySurplusLinesTax           *float64 `rml:"float,number.decimal,output"`
	LiabBasicPolicyStampingFee               *float64 `rml:"float,number.decimal,output"`
	PhysBasicPolicySurplusLinesTax           *float64 `rml:"float,number.decimal,output"`
	PhysBasicPolicyStampingFee               *float64 `rml:"float,number.decimal,output"`
	GlBasicPolicySurplusLinesTax             *float64 `rml:"float,number.decimal,output"`
	GlBasicPolicyStampingFee                 *float64 `rml:"float,number.decimal,output"`
	MtcBasicPolicySurplusLinesTax            *float64 `rml:"float,number.decimal,output"`
	MtcBasicPolicyStampingFee                *float64 `rml:"float,number.decimal,output"`
	TotalBasicSurpTaxandStampFee             *float64 `rml:"float,number.decimal,output"`
	AutoBasicInsurancePremSurcharge          *float64 `rml:"float,number.decimal,output"`
	GlBasicInsurancePremSurcharge            *float64 `rml:"float,number.decimal,output"`
	MtcBasicInsurancePremSurcharge           *float64 `rml:"float,number.decimal,output"`
	AutoBasicLGPTSurcharge                   *float64 `rml:"float,number.decimal,output"`
	GlBasicLGPTSurcharge                     *float64 `rml:"float,number.decimal,output"`
	MtcBasicLGPTSurcharge                    *float64 `rml:"float,number.decimal,output"`
	TotalBasicInsurancePremSurcharge         *float64 `rml:"float,number.decimal,output"`
	TotalBasicSurchargePremiumKy             *float64 `rml:"float,number.decimal,output"`
	LiabBasicPolicyPremiumUnmodified         *float64 `rml:"float,number.decimal,output"`
	PhysBasicPolicyPremiumUnmodified         *float64 `rml:"float,number.decimal,output"`
	GlBasicPolicyPremiumUnmodified           *float64 `rml:"float,number.decimal,output"`
	MtcBasicPolicyPremiumUnmodified          *float64 `rml:"float,number.decimal,output"`
	CoverageFinalModPremiumCollBasic         *float64 `rml:"float,number.decimal,output"`
	CoverageFinalModPremiumCompBasic         *float64 `rml:"float,number.decimal,output"`
	TotalBasicPolicyPremiumUnmodified        *float64 `rml:"float,number.decimal,output"`
}

var _ common.Entity = (*OutputsQuote)(nil)

func (o *OutputsQuote) RatemlId() string {
	return o.Id
}

func (o *OutputsQuote) RatemlTypeName() common.EntityType {
	return common.EntityTypeOutputsQuote
}

func (o *OutputsQuote) HandleConnectedEntity(e common.Entity) error {
	// nolint:exhaustive
	switch e.RatemlTypeName() {
	case common.EntityTypeQuoteData:
		if o.QuoteData != "" {
			return errors.Newf("quotedata entity with id %s already connected", o.QuoteData)
		}
		o.QuoteData = e.RatemlId()
	default:
		return errors.Newf(
			"no linkage possible for %v(id %s) with %v",
			o.RatemlTypeName(), o.RatemlId(), e.RatemlTypeName(),
		)
	}
	return nil
}

func (*OutputsQuote) ConnectedEntityTypes() []common.EntityType {
	return []common.EntityType{
		common.EntityTypeQuoteData,
	}
}
