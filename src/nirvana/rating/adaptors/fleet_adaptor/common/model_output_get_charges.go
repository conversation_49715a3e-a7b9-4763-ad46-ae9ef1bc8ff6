package common

import (
	"github.com/cockroachdb/errors"
	"github.com/shopspring/decimal"

	"nirvanatech.com/nirvana/common-go/proto"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
	"nirvanatech.com/nirvana/rating/pricing/api/utils"
)

const ratePrecisionDecimalFigures = 8

type fullyEarnedPremiums struct {
	RegularAI float64
	PNCAI     float64
	WOS       float64
}

var bipdSubCovGroup = &ptypes.SubCoverageGroup{
	SubCoverages: []ptypes.SubCoverageType{
		ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
		ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
	},
}

var medPaySubCovGroup = &ptypes.SubCoverageGroup{
	SubCoverages: []ptypes.SubCoverageType{
		ptypes.SubCoverageType_SubCoverageType_MedicalPayments,
	},
}

var pipSubCovGroup = &ptypes.SubCoverageGroup{
	SubCoverages: []ptypes.SubCoverageType{
		ptypes.SubCoverageType_SubCoverageType_PersonalInjuryProtection,
	},
}

var guestPIPSubCovGroup = &ptypes.SubCoverageGroup{
	SubCoverages: []ptypes.SubCoverageType{
		ptypes.SubCoverageType_SubCoverageType_GuestPersonalInjuryProtection,
	},
}

var pipACSubCovGroup = &ptypes.SubCoverageGroup{
	SubCoverages: []ptypes.SubCoverageType{
		ptypes.SubCoverageType_SubCoverageType_PIPAttendantCare,
	},
}

var ppiSubCovGroup = &ptypes.SubCoverageGroup{
	SubCoverages: []ptypes.SubCoverageType{
		ptypes.SubCoverageType_SubCoverageType_PropertyProtectionInsurance,
	},
}

var broadenedPollutionSubCovGroup = &ptypes.SubCoverageGroup{
	SubCoverages: []ptypes.SubCoverageType{
		ptypes.SubCoverageType_SubCoverageType_BroadenedPollution,
	},
}

var umSubCovGroup = &ptypes.SubCoverageGroup{
	SubCoverages: []ptypes.SubCoverageType{
		ptypes.SubCoverageType_SubCoverageType_UninsuredMotorist,
	},
}

var uimSubCovGroup = &ptypes.SubCoverageGroup{
	SubCoverages: []ptypes.SubCoverageType{
		ptypes.SubCoverageType_SubCoverageType_UnderInsuredMotorist,
	},
}

var umUimSubCovGroup = &ptypes.SubCoverageGroup{
	SubCoverages: []ptypes.SubCoverageType{
		ptypes.SubCoverageType_SubCoverageType_UMUIM,
	},
}

var umpdSubCovGroup = &ptypes.SubCoverageGroup{
	SubCoverages: []ptypes.SubCoverageType{
		ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage,
	},
}

var collSubCovGroup = &ptypes.SubCoverageGroup{
	SubCoverages: []ptypes.SubCoverageType{
		ptypes.SubCoverageType_SubCoverageType_Collision,
	},
}

var compSubCovGroup = &ptypes.SubCoverageGroup{
	SubCoverages: []ptypes.SubCoverageType{
		ptypes.SubCoverageType_SubCoverageType_Comprehensive,
	},
}

var trailerInterchangeSubCovGroup = &ptypes.SubCoverageGroup{
	SubCoverages: []ptypes.SubCoverageType{
		ptypes.SubCoverageType_SubCoverageType_TrailerInterchange,
	},
}

var apdPackageSubCovGroup = &ptypes.SubCoverageGroup{
	SubCoverages: []ptypes.SubCoverageType{
		ptypes.SubCoverageType_SubCoverageType_Towing,
		ptypes.SubCoverageType_SubCoverageType_ElectronicEquipment,
		ptypes.SubCoverageType_SubCoverageType_MiscellaneousEquipment,
		ptypes.SubCoverageType_SubCoverageType_RentalReimbursement,
	},
}

var glSubCovGroup = &ptypes.SubCoverageGroup{
	SubCoverages: []ptypes.SubCoverageType{
		ptypes.SubCoverageType_SubCoverageType_GeneralLiability,
	},
}

var stopGapSubCovGroup = &ptypes.SubCoverageGroup{
	SubCoverages: []ptypes.SubCoverageType{
		ptypes.SubCoverageType_SubCoverageType_StopGap,
	},
}

var cargoSubCovGroup = &ptypes.SubCoverageGroup{
	SubCoverages: []ptypes.SubCoverageType{
		ptypes.SubCoverageType_SubCoverageType_Cargo,
	},
}

var reeferWithoutHumanErrorSubCovGroup = &ptypes.SubCoverageGroup{
	SubCoverages: []ptypes.SubCoverageType{
		ptypes.SubCoverageType_SubCoverageType_ReeferWithoutHumanError,
	},
}

var reeferWithHumanErrorSubCovGroup = &ptypes.SubCoverageGroup{
	SubCoverages: []ptypes.SubCoverageType{
		ptypes.SubCoverageType_SubCoverageType_ReeferWithHumanError,
	},
}

var cargoAtScheduledTerminalsSubCovGroup = &ptypes.SubCoverageGroup{
	SubCoverages: []ptypes.SubCoverageType{
		ptypes.SubCoverageType_SubCoverageType_CargoAtScheduledTerminals,
	},
}

// The GetCharges method is needed so the model output implements the engine.ChunkOutputCreatorI.
//
// It's important to be aware that a new ModelOutput implementations should be added if
// the logic to create charges changes in a non-backward compatible way. For instance:
//   - OK: add charges that would never be created for old model versions.
//   - NOT OK: change the way in which an existing charge's premium is calculated.
func (m *ModelOutput) GetCharges() ([]*ptypes.Charge, error) {
	policyNumber := m.Input.GetPolicyNumber()

	policyDates, err := m.Input.GetPolicyOriginalDates()
	if err != nil {
		return nil, errors.Wrap(err, "failed to get policy dates")
	}

	chunkDates, err := m.Input.GetChunkDates()
	if err != nil {
		return nil, errors.Wrap(err, "failed to get chunk dates")
	}

	charges := make([]*ptypes.Charge, 0)

	// We first create fully earned charges
	fullyEarnedCharges, err := m.createFullyEarnedCharges(policyNumber, chunkDates)
	if err != nil {
		return nil, err
	}

	charges = append(charges, fullyEarnedCharges...)

	// Then we create base charges
	baseCharges, err := m.createBaseCharges(policyDates, chunkDates)
	if err != nil {
		return nil, err
	}

	charges = append(charges, baseCharges...)

	// Finally, we filter out nil charges
	charges = slice_utils.Filter(charges, func(c *ptypes.Charge) bool {
		return c != nil
	})

	return charges, nil
}

func (m *ModelOutput) createFullyEarnedCharges(
	policyNumber string,
	chunkDates *proto.Interval,
) ([]*ptypes.Charge, error) {
	subCovGroupToPremiumsMap := map[*ptypes.SubCoverageGroup]struct {
		Blanket   fullyEarnedPremiums
		Specified fullyEarnedPremiums
	}{
		bipdSubCovGroup: {
			Blanket: fullyEarnedPremiums{
				RegularAI: m.PolicyPremiumBipdBlanketAdditionalInsured,
				PNCAI:     m.PolicyPremiumBipdBlanketPNCAdditionalInsured,
				WOS:       m.PolicyPremiumBipdBlanketWaiverOfSubrogation,
			},
			Specified: fullyEarnedPremiums{
				RegularAI: m.PerEndtPremiumBipdSpecifiedAdditionalInsured,
				PNCAI:     m.PerEndtPremiumBipdSpecifiedPNCAdditionalInsured,
				WOS:       m.PerEndtPremiumBipdSpecifiedWaiverOfSubrogation,
			},
		},
		glSubCovGroup: {
			Blanket: fullyEarnedPremiums{
				RegularAI: m.PolicyPremiumGLBlanketAdditionalInsured,
				PNCAI:     m.PolicyPremiumGLBlanketPNCAdditionalInsured,
				WOS:       m.PolicyPremiumGLBlanketWaiverOfSubrogation,
			},
			Specified: fullyEarnedPremiums{
				RegularAI: m.PerEndtPremiumGlSpecifiedAdditionalInsured,
				PNCAI:     m.PerEndtPremiumGlSpecifiedPNCAdditionalInsured,
				WOS:       m.PerEndtPremiumGlSpecifiedWaiverOfSubrogation,
			},
		},
		// Note that Cargo doesn't have PNC AI
		cargoSubCovGroup: {
			Blanket: fullyEarnedPremiums{
				RegularAI: m.PolicyPremiumCargoBlanketAdditionalInsured,
				WOS:       m.PolicyPremiumCargoBlanketWaiverOfSubrogation,
			},
			Specified: fullyEarnedPremiums{
				RegularAI: m.PerEndtPremiumCargoSpecifiedAdditionalInsured,
				WOS:       m.PerEndtPremiumCargoSpecifiedWaiverOfSubrogation,
			},
		},
	}

	charges := make([]*ptypes.Charge, 0)

	for subCovGroup, premiums := range subCovGroupToPremiumsMap {
		blanketPremiums := premiums.Blanket
		specifiedPremiums := premiums.Specified

		ch := m.createBRAICharge(policyNumber, chunkDates, subCovGroup, blanketPremiums.RegularAI)
		charges = append(charges, ch)

		ch = m.createBPNCAICharge(policyNumber, chunkDates, subCovGroup, blanketPremiums.PNCAI)
		charges = append(charges, ch)

		ch = m.createBWOSCharge(policyNumber, chunkDates, subCovGroup, blanketPremiums.WOS)
		charges = append(charges, ch)

		regularAIs, err := m.Input.GetSpecifiedRegularAIs(subCovGroup)
		if err != nil {
			return nil, err
		}

		pncAIs, err := m.Input.GetSpecifiedPNCAIs(subCovGroup)
		if err != nil {
			return nil, err
		}

		woss, err := m.Input.GetSpecifiedWOSs(subCovGroup)
		if err != nil {
			return nil, err
		}

		for _, ai := range regularAIs {
			ch = m.createSRAICharge(policyNumber, chunkDates, ai, specifiedPremiums.RegularAI)
			charges = append(charges, ch)
		}

		for _, ai := range pncAIs {
			ch = m.createSPNCAICharge(policyNumber, chunkDates, ai, specifiedPremiums.PNCAI)
			charges = append(charges, ch)
		}

		for _, wos := range woss {
			ch = m.createSWOSCharge(policyNumber, chunkDates, wos, specifiedPremiums.WOS)
			charges = append(charges, ch)
		}
	}

	return charges, nil
}

// createBAICharge creates a charge for the blanket regular additional insured.
func (m *ModelOutput) createBRAICharge(
	policyNumber string,
	chunkDates *proto.Interval,
	subCovGroup *ptypes.SubCoverageGroup,
	premium float64,
) *ptypes.Charge {
	chargeType := ptypes.NewChargeBaseChargeWithBlanketRegularAdditionalInsured(subCovGroup)
	chargedItem := ptypes.NewChargeChargedPolicy(policyNumber)
	return m.createChargeWithAmountBasedBillingDetails(
		chunkDates,
		chargeType,
		chargedItem,
		premium,
	)
}

// createBAICharge creates a charge for the blanket primary and non-contributory additional insured.
func (m *ModelOutput) createBPNCAICharge(
	policyNumber string,
	chunkDates *proto.Interval,
	subCovGroup *ptypes.SubCoverageGroup,
	premium float64,
) *ptypes.Charge {
	chargeType := ptypes.NewChargeBaseChargeWithBlanketPrimaryAndNonContributoryAdditionalInsured(subCovGroup)
	chargedItem := ptypes.NewChargeChargedPolicy(policyNumber)
	return m.createChargeWithAmountBasedBillingDetails(
		chunkDates,
		chargeType,
		chargedItem,
		premium,
	)
}

// createBWOSCharge creates a charge for the blanket waiver of subrogation.
func (m *ModelOutput) createBWOSCharge(
	policyNumber string,
	chunkDates *proto.Interval,
	subCovGroup *ptypes.SubCoverageGroup,
	premium float64,
) *ptypes.Charge {
	chargeType := ptypes.NewChargeBaseChargeWithBlanketWaiverOfSubrogation(subCovGroup)
	chargedItem := ptypes.NewChargeChargedPolicy(policyNumber)
	return m.createChargeWithAmountBasedBillingDetails(
		chunkDates,
		chargeType,
		chargedItem,
		premium,
	)
}

// createSRAICharge creates a charge for a specified regular additional insured.
func (m *ModelOutput) createSRAICharge(
	policyNumber string,
	chunkDates *proto.Interval,
	additionalInsured *ptypes.SpecifiedRegularAdditionalInsured,
	premium float64,
) *ptypes.Charge {
	chargeType := ptypes.NewChargeBaseChargeWithSpecifiedRegularAdditionalInsured(
		additionalInsured.SubCoverageGroup,
		additionalInsured.Id,
	)
	chargedItem := ptypes.NewChargeChargedPolicy(policyNumber)
	return m.createChargeWithAmountBasedBillingDetails(
		chunkDates,
		chargeType,
		chargedItem,
		premium,
	)
}

// createSPNCAICharge creates a charge for a specified primary and non-contributory additional insured.
func (m *ModelOutput) createSPNCAICharge(
	policyNumber string,
	chunkDates *proto.Interval,
	additionalInsured *ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured,
	premium float64,
) *ptypes.Charge {
	chargeType := ptypes.NewChargeBaseChargeWithSpecifiedPrimaryAndNonContributoryAdditionalInsured(
		additionalInsured.SubCoverageGroup,
		additionalInsured.Id,
	)
	chargedItem := ptypes.NewChargeChargedPolicy(policyNumber)
	return m.createChargeWithAmountBasedBillingDetails(
		chunkDates,
		chargeType,
		chargedItem,
		premium,
	)
}

// createSWOSCharge creates a charge for a specified third party with a waiver of subrogation.
func (m *ModelOutput) createSWOSCharge(
	policyNumber string,
	chunkDates *proto.Interval,
	wos *ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation,
	premium float64,
) *ptypes.Charge {
	chargeType := ptypes.NewChargeBaseChargeWithSpecifiedThirdPartyWithWaiverOfSubrogation(
		wos.SubCoverageGroup,
		wos.Id,
	)
	chargedItem := ptypes.NewChargeChargedPolicy(policyNumber)
	return m.createChargeWithAmountBasedBillingDetails(
		chunkDates,
		chargeType,
		chargedItem,
		premium,
	)
}

func (m *ModelOutput) createBaseCharges(
	policyDates *proto.Interval,
	chunkDates *proto.Interval,
) ([]*ptypes.Charge, error) {
	charges := make([]*ptypes.Charge, 0)

	alCharges, err := m.getALCharges(chunkDates)
	if err != nil {
		return nil, err
	}

	charges = append(charges, alCharges...)

	apdCharges, err := m.getAPDCharges(policyDates, chunkDates)
	if err != nil {
		return nil, err
	}

	charges = append(charges, apdCharges...)

	glCharges, err := m.getGLCharges(chunkDates)
	if err != nil {
		return nil, err
	}

	charges = append(charges, glCharges...)

	cargoCharges, err := m.getMTCCharges(chunkDates)
	if err != nil {
		return nil, err
	}

	charges = append(charges, cargoCharges...)

	return charges, nil
}

func (m *ModelOutput) getALCharges(chunkDates *proto.Interval) ([]*ptypes.Charge, error) {
	subCoverageGroupToBasePremiumMap := map[*ptypes.SubCoverageGroup]float64{
		bipdSubCovGroup:               m.PolicyPremiumLiabBase,
		medPaySubCovGroup:             m.PolicyPremiumMedPay,
		pipSubCovGroup:                m.PolicyPremiumPip,
		guestPIPSubCovGroup:           m.PolicyPremiumGuestPip,
		pipACSubCovGroup:              m.PolicyPremiumPipEac,
		ppiSubCovGroup:                m.PolicyPremiumPpi,
		broadenedPollutionSubCovGroup: m.PolicyPremiumBroadenedPollution,
		umSubCovGroup:                 m.PolicyPremiumUm,
		uimSubCovGroup:                m.PolicyPremiumUim,
		umUimSubCovGroup:              m.PolicyPremiumUmUim,
		umpdSubCovGroup:               m.PolicyPremiumUmpd,
	}

	charges := make([]*ptypes.Charge, 0)
	for subCovGroup, basePremium := range subCoverageGroupToBasePremiumMap {
		charges = append(charges, m.createBaseChargeWithMilesBasedBillingDetailsAndChargedSubCoverages(
			chunkDates,
			basePremium,
			subCovGroup,
		))
	}

	return charges, nil
}

func (m *ModelOutput) getAPDCharges(
	policyDates *proto.Interval,
	chunkDates *proto.Interval,
) ([]*ptypes.Charge, error) {
	subCoverageGroupToBasePremiumMap := map[*ptypes.SubCoverageGroup]float64{
		collSubCovGroup:               m.PolicyPremiumCollBase,
		compSubCovGroup:               m.PolicyPremiumCompBase,
		trailerInterchangeSubCovGroup: m.PolicyPremiumTrailerInterchange,
		apdPackageSubCovGroup:         m.GetPolicyPremiumApdPackage(),
	}

	charges := make([]*ptypes.Charge, 0)
	for subCovGroup, basePremium := range subCoverageGroupToBasePremiumMap {
		charges = append(charges, m.createBaseChargeWithTIVDaysBasedBillingDetailsAndChargedSubCoverages(
			policyDates,
			chunkDates,
			basePremium,
			subCovGroup,
		))
	}

	return charges, nil
}

func (m *ModelOutput) getGLCharges(chunkDates *proto.Interval) ([]*ptypes.Charge, error) {
	subCoverageGroupToBasePremiumMap := map[*ptypes.SubCoverageGroup]float64{
		glSubCovGroup:      m.PolicyPremiumGlBase,
		stopGapSubCovGroup: m.PolicyPremiumGlStopGap,
	}

	charges := make([]*ptypes.Charge, 0)
	for subCovGroup, basePremium := range subCoverageGroupToBasePremiumMap {
		charges = append(charges, m.createBaseChargeWithAmountBasedBillingDetailsAndChargedSubCoverages(
			chunkDates,
			basePremium,
			subCovGroup,
		))
	}

	return charges, nil
}

func (m *ModelOutput) getMTCCharges(chunkDates *proto.Interval) ([]*ptypes.Charge, error) {
	subCoverageGroupToBasePremiumMap := map[*ptypes.SubCoverageGroup]float64{
		cargoSubCovGroup:                     m.PolicyPremiumMtcBase,
		reeferWithoutHumanErrorSubCovGroup:   m.PolicyPremiumReeferBreakdown,
		reeferWithHumanErrorSubCovGroup:      m.PolicyPremiumReeferBreakdownWithHumanError,
		cargoAtScheduledTerminalsSubCovGroup: m.PolicyPremiumCargoAtTerminal,
		trailerInterchangeSubCovGroup:        m.PolicyPremiumMtcTrailerInterchange,
	}

	charges := make([]*ptypes.Charge, 0)
	for subCovGroup, basePremium := range subCoverageGroupToBasePremiumMap {
		charges = append(charges, m.createBaseChargeWithMilesBasedBillingDetailsAndChargedSubCoverages(
			chunkDates,
			basePremium,
			subCovGroup,
		))
	}

	return charges, nil
}

func (m *ModelOutput) createBaseChargeWithAmountBasedBillingDetailsAndChargedSubCoverages(
	chunkDates *proto.Interval,
	premium float64,
	subCoverageGroup *ptypes.SubCoverageGroup,
) *ptypes.Charge {
	chargeType := ptypes.NewChargeBaseChargeWithNoExtraInfo()
	chargedItem := ptypes.NewChargeChargedSubCoverageGroup(subCoverageGroup.SubCoverages...)

	return m.createChargeWithAmountBasedBillingDetails(
		chunkDates,
		chargeType,
		chargedItem,
		premium,
	)
}

func (m *ModelOutput) createChargeWithAmountBasedBillingDetails(
	chunkDates *proto.Interval,
	chargeType ptypes.ChargeTypeI,
	chargedItem ptypes.ChargedItemI,
	premium float64,
) *ptypes.Charge {
	if premium == 0 {
		return nil
	}

	premiumAsString := decimal.NewFromFloat(premium).String()

	// For charges with AmountBasedBillingDetails that originate from a chunk,
	// we assume that the charge date is the start date of the chunk. The
	// reason for this, is to ensure that the entire premium is charged in
	// a billing interval that includes the start date of the chunk (i.e.
	// that includes the endorsement that resulted in the chunk).
	chargeDate := chunkDates.Start

	return ptypes.NewChargeBuilder().
		WithChargeType(chargeType).
		WithChargedItem(chargedItem).
		WithAmountBasedBillingDetails(premiumAsString, chargeDate).
		Build()
}

func (m *ModelOutput) createBaseChargeWithMilesBasedBillingDetailsAndChargedSubCoverages(
	chunkDates *proto.Interval,
	premium float64,
	subCoverageGroup *ptypes.SubCoverageGroup,
) *ptypes.Charge {
	chargeType := ptypes.NewChargeBaseChargeWithNoExtraInfo()
	chargedItem := ptypes.NewChargeChargedSubCoverageGroup(subCoverageGroup.SubCoverages...)

	return m.createChargeWithRateBasedBillingDetails(
		chunkDates,
		chargeType,
		chargedItem,
		premium,
		decimal.NewFromInt(m.ProjectedMiles),
		ptypes.RateBasis_RateBasis_Miles,
	)
}

func (m *ModelOutput) createBaseChargeWithTIVDaysBasedBillingDetailsAndChargedSubCoverages(
	policyDates *proto.Interval,
	chunkDates *proto.Interval,
	premium float64,
	subCoverageGroup *ptypes.SubCoverageGroup,
) *ptypes.Charge {
	chargeType := ptypes.NewChargeBaseChargeWithNoExtraInfo()
	chargedItem := ptypes.NewChargeChargedSubCoverageGroup(subCoverageGroup.SubCoverages...)

	tiv := m.GetTIV()
	days := utils.CalculateDaysDuration(policyDates)
	tivDays := decimal.NewFromFloat(tiv).Mul(days)

	return m.createChargeWithRateBasedBillingDetails(
		chunkDates,
		chargeType,
		chargedItem,
		premium,
		tivDays,
		ptypes.RateBasis_RateBasis_TIV_Days,
	)
}

func (m *ModelOutput) createChargeWithRateBasedBillingDetails(
	chunkDates *proto.Interval,
	chargeType ptypes.ChargeTypeI,
	chargedItem ptypes.ChargedItemI,
	nominator float64,
	denominator decimal.Decimal,
	rateBasis ptypes.RateBasis,
) *ptypes.Charge {
	if nominator == 0 {
		return nil
	}

	rate := decimal.NewFromFloat(nominator).Div(denominator).Round(ratePrecisionDecimalFigures).String()

	return ptypes.NewChargeBuilder().
		WithChargeType(chargeType).
		WithChargedItem(chargedItem).
		WithRateBasedBillingDetails(rate, chunkDates, rateBasis).
		Build()
}
