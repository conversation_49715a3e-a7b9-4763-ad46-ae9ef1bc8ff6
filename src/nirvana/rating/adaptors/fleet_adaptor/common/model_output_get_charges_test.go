package common

import (
	"fmt"
	"testing"
	"time"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/map_utils"
	"nirvanatech.com/nirvana/common-go/proto"
	"nirvanatech.com/nirvana/common-go/time_utils"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/rating/adaptors/common"
	"nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/entities/creator_functions"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

func TestGetCharges_WithNoCharges(t *testing.T) {
	policyNumber := "policyNumber"
	policyDates := &proto.Interval{
		Start: timestamppb.Now(),
		End:   timestamppb.Now(),
	}

	chunkID := "chunkID"
	chunkDates := &proto.Interval{
		Start: timestamppb.Now(),
	}

	input := &creator_functions.Input{
		Input: common.Input{
			PolicyNumber:        policyNumber,
			PolicyOriginalDates: policyDates,
			BundleChunkSpec: &ptypes.BundleSpec_ChunkSpec{
				ChunkId: chunkID,
				Dates:   chunkDates,
			},
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{},
		},
	}

	oc := &ModelOutput{Input: input}

	// All premium amounts are zero by default
	charges, err := oc.GetCharges()
	require.NoError(t, err)
	require.Empty(t, charges)
}

func TestGetCharges_WithAllCharges(t *testing.T) {
	policyNumber := "policyNumber"

	// 10 days
	policyStarDate := time_utils.MustParseDate("02/01/2006", "01/01/2024").ToTime()
	policyEndDate := policyStarDate.Add(10 * 24 * time.Hour)
	policyDates := &proto.Interval{
		Start: timestamppb.New(policyStarDate),
		End:   timestamppb.New(policyEndDate),
	}

	chunkStartDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "02/01/2024").ToTime())
	chunkEndDate := timestamppb.New(time_utils.MustParseDate("02/01/2006", "03/01/2024").ToTime())
	chunkDates := &proto.Interval{Start: chunkStartDate, End: chunkEndDate}

	input := &creator_functions.Input{
		Input: common.Input{
			PolicyNumber:        policyNumber,
			PolicyOriginalDates: policyDates,
			BundleChunkSpec: &ptypes.BundleSpec_ChunkSpec{
				ChunkId: "chunkID",
				Dates:   chunkDates,
			},
			PolicyChunkSpec: &ptypes.PolicySpec_ChunkSpec{
				SpecifiedRegularAdditionalInsureds: []*ptypes.SpecifiedRegularAdditionalInsured{
					{
						Id:               "bipd-regular-ai-1",
						SubCoverageGroup: bipdSubCovGroup,
					},
					{
						Id:               "bipd-regular-ai-2",
						SubCoverageGroup: bipdSubCovGroup,
					},
					{
						Id:               "gl-regular-ai-1",
						SubCoverageGroup: glSubCovGroup,
					},
					{
						Id:               "gl-regular-ai-2",
						SubCoverageGroup: glSubCovGroup,
					},
					{
						Id:               "cargo-regular-ai-1",
						SubCoverageGroup: cargoSubCovGroup,
					},
					{
						Id:               "cargo-regular-ai-2",
						SubCoverageGroup: cargoSubCovGroup,
					},
				},
				SpecifiedPrimaryAndNonContributoryAdditionalInsureds: []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
					{
						Id:               "bipd-pnc-ai-1",
						SubCoverageGroup: bipdSubCovGroup,
					},
					{
						Id:               "bipd-pnc-ai-2",
						SubCoverageGroup: bipdSubCovGroup,
					},
					{
						Id:               "gl-pnc-ai-1",
						SubCoverageGroup: glSubCovGroup,
					},
					{
						Id:               "gl-pnc-ai-2",
						SubCoverageGroup: glSubCovGroup,
					},
				},
				SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
					{
						Id:               "bipd-wos-1",
						SubCoverageGroup: bipdSubCovGroup,
					},
					{
						Id:               "bipd-wos-2",
						SubCoverageGroup: bipdSubCovGroup,
					},
					{
						Id:               "gl-wos-1",
						SubCoverageGroup: glSubCovGroup,
					},
					{
						Id:               "gl-wos-2",
						SubCoverageGroup: glSubCovGroup,
					},
					{
						Id:               "cargo-wos-1",
						SubCoverageGroup: cargoSubCovGroup,
					},
					{
						Id:               "cargo-wos-2",
						SubCoverageGroup: cargoSubCovGroup,
					},
				},
			},
		},
	}

	premiums := make(map[string]float64)
	premiums["bipdBlanketRegularAdditionalInsured"] = 10.0
	premiums["bipdBlanketPNCAdditionalInsured"] = 15.0
	premiums["bipdBlanketWaiverOfSubrogation"] = 20.0
	premiums["bipdSpecifiedRegularAdditionalInsured"] = 25.0
	premiums["bipdSpecifiedPNCAdditionalInsured"] = 30.0
	premiums["bipdSpecifiedWaiverOfSubrogation"] = 35.0
	premiums["glBlanketRegularAdditionalInsured"] = 40.0
	premiums["glBlanketPNCAdditionalInsured"] = 45.0
	premiums["glBlanketWaiverOfSubrogation"] = 50.0
	premiums["glSpecifiedRegularAdditionalInsured"] = 55.0
	premiums["glSpecifiedPNCAdditionalInsured"] = 60.0
	premiums["glSpecifiedWaiverOfSubrogation"] = 65.0
	premiums["cargoBlanketRegularAdditionalInsured"] = 70.0
	premiums["cargoBlanketWaiverOfSubrogation"] = 75.0
	premiums["cargoSpecifiedRegularAdditionalInsured"] = 80.0
	premiums["cargoSpecifiedWaiverOfSubrogation"] = 85.0
	premiums["liabBase"] = 100.0
	premiums["medPay"] = 105.0
	premiums["pip"] = 110.0
	premiums["guestPip"] = 115.0
	premiums["pipAC"] = 120.0
	premiums["ppi"] = 125.0
	premiums["broadenedPollution"] = 130.0
	premiums["um"] = 135.0
	premiums["uim"] = 140.0
	premiums["umUim"] = 145.0
	premiums["umpd"] = 150.0
	premiums["coll"] = 155.0
	premiums["comp"] = 160.0
	premiums["trailerInterchange"] = 165.0
	premiums["completeApdPackage"] = 170.0
	premiums["standardApdPackage"] = 175.0
	premiums["gl"] = 180.0
	premiums["stopGap"] = 185.0
	premiums["cargo"] = 190.0
	premiums["reeferBreakdownWithoutHumanError"] = 195.0
	premiums["reeferBreakdownWithHumanError"] = 200.0
	premiums["cargoAtTerminal"] = 205.0
	premiums["mtcTrailerInterchange"] = 210.0

	packageTypes := []app_enums.IndicationOptionTag{
		app_enums.IndicationOptionTagBasic,
		app_enums.IndicationOptionTagStandard,
		app_enums.IndicationOptionTagComplete,
	}

	for _, packageType := range packageTypes {
		t.Run(fmt.Sprintf("With %s", packageType), func(tt *testing.T) {
			mo := ModelOutput{
				Input:       input,
				PackageType: packageType,
				PerEndtPremiumBipdSpecifiedAdditionalInsured:    premiums["bipdSpecifiedRegularAdditionalInsured"],
				PerEndtPremiumBipdSpecifiedPNCAdditionalInsured: premiums["bipdSpecifiedPNCAdditionalInsured"],
				PerEndtPremiumBipdSpecifiedWaiverOfSubrogation:  premiums["bipdSpecifiedWaiverOfSubrogation"],
				PolicyPremiumBipdBlanketAdditionalInsured:       premiums["bipdBlanketRegularAdditionalInsured"],
				PolicyPremiumBipdBlanketPNCAdditionalInsured:    premiums["bipdBlanketPNCAdditionalInsured"],
				PolicyPremiumBipdBlanketWaiverOfSubrogation:     premiums["bipdBlanketWaiverOfSubrogation"],
				PerEndtPremiumGlSpecifiedAdditionalInsured:      premiums["glSpecifiedRegularAdditionalInsured"],
				PerEndtPremiumGlSpecifiedPNCAdditionalInsured:   premiums["glSpecifiedPNCAdditionalInsured"],
				PerEndtPremiumGlSpecifiedWaiverOfSubrogation:    premiums["glSpecifiedWaiverOfSubrogation"],
				PolicyPremiumGLBlanketAdditionalInsured:         premiums["glBlanketRegularAdditionalInsured"],
				PolicyPremiumGLBlanketPNCAdditionalInsured:      premiums["glBlanketPNCAdditionalInsured"],
				PolicyPremiumGLBlanketWaiverOfSubrogation:       premiums["glBlanketWaiverOfSubrogation"],
				PerEndtPremiumCargoSpecifiedAdditionalInsured:   premiums["cargoSpecifiedRegularAdditionalInsured"],
				PerEndtPremiumCargoSpecifiedWaiverOfSubrogation: premiums["cargoSpecifiedWaiverOfSubrogation"],
				PolicyPremiumCargoBlanketAdditionalInsured:      premiums["cargoBlanketRegularAdditionalInsured"],
				PolicyPremiumCargoBlanketWaiverOfSubrogation:    premiums["cargoBlanketWaiverOfSubrogation"],
				PolicyPremiumLiabBase:                           premiums["liabBase"],
				PolicyPremiumMedPay:                             premiums["medPay"],
				PolicyPremiumPip:                                premiums["pip"],
				PolicyPremiumGuestPip:                           premiums["guestPip"],
				PolicyPremiumPipEac:                             premiums["pipAC"],
				PolicyPremiumPpi:                                premiums["ppi"],
				PolicyPremiumBroadenedPollution:                 premiums["broadenedPollution"],
				PolicyPremiumUm:                                 premiums["um"],
				PolicyPremiumUim:                                premiums["uim"],
				PolicyPremiumUmUim:                              premiums["umUim"],
				PolicyPremiumUmpd:                               premiums["umpd"],
				PolicyPremiumCollBase:                           premiums["coll"],
				PolicyPremiumCompBase:                           premiums["comp"],
				PolicyPremiumTrailerInterchange:                 premiums["trailerInterchange"],
				PolicyPremiumCompleteApdPackage:                 premiums["completeApdPackage"],
				PolicyPremiumStandardApdPackage:                 premiums["standardApdPackage"],
				PolicyPremiumGlBase:                             premiums["gl"],
				PolicyPremiumGlStopGap:                          premiums["stopGap"],
				PolicyPremiumMtcBase:                            premiums["cargo"],
				PolicyPremiumReeferBreakdown:                    premiums["reeferBreakdownWithoutHumanError"],
				PolicyPremiumReeferBreakdownWithHumanError:      premiums["reeferBreakdownWithHumanError"],
				PolicyPremiumCargoAtTerminal:                    premiums["cargoAtTerminal"],
				PolicyPremiumMtcTrailerInterchange:              premiums["mtcTrailerInterchange"],
				ProjectedMiles:                                  1000,
				Tiv:                                             10,
			}

			expectedPremiumsOrRates := map_utils.ShallowCopy(premiums)

			expectedPremiumsOrRates["liabBase"] = 0.100
			expectedPremiumsOrRates["medPay"] = 0.105
			expectedPremiumsOrRates["pip"] = 0.110
			expectedPremiumsOrRates["guestPip"] = 0.115
			expectedPremiumsOrRates["pipAC"] = 0.120
			expectedPremiumsOrRates["ppi"] = 0.125
			expectedPremiumsOrRates["broadenedPollution"] = 0.130
			expectedPremiumsOrRates["um"] = 0.135
			expectedPremiumsOrRates["uim"] = 0.140
			expectedPremiumsOrRates["umUim"] = 0.145
			expectedPremiumsOrRates["umpd"] = 0.150
			expectedPremiumsOrRates["coll"] = 1.55
			expectedPremiumsOrRates["comp"] = 1.60
			expectedPremiumsOrRates["trailerInterchange"] = 1.65
			expectedPremiumsOrRates["completeApdPackage"] = 1.70
			expectedPremiumsOrRates["standardApdPackage"] = 1.75
			expectedPremiumsOrRates["cargo"] = 0.190
			expectedPremiumsOrRates["reeferBreakdownWithoutHumanError"] = 0.195
			expectedPremiumsOrRates["reeferBreakdownWithHumanError"] = 0.200
			expectedPremiumsOrRates["cargoAtTerminal"] = 0.205
			expectedPremiumsOrRates["mtcTrailerInterchange"] = 0.210

			if packageType == app_enums.IndicationOptionTagBasic {
				expectedPremiumsOrRates["apdPackage"] = 0.0
			} else if packageType == app_enums.IndicationOptionTagStandard {
				expectedPremiumsOrRates["apdPackage"] = expectedPremiumsOrRates["standardApdPackage"]
			} else if packageType == app_enums.IndicationOptionTagComplete {
				expectedPremiumsOrRates["apdPackage"] = expectedPremiumsOrRates["completeApdPackage"]
			} else {
				require.Fail(tt, "Unexpected package type: %s", packageType)
			}

			charges, err := mo.GetCharges()
			require.NoError(t, err)

			expectedCharges := getExpectedCharges(
				policyNumber,
				chunkDates,
				expectedPremiumsOrRates,
				packageType,
			)
			require.ElementsMatch(t, expectedCharges, charges)
		})
	}
}

func getExpectedCharges(
	policyNumber string,
	chunkDates *proto.Interval,
	expectedPremiumsOrRates map[string]float64,
	packageType app_enums.IndicationOptionTag,
) []*ptypes.Charge {
	charges := make([]*ptypes.Charge, 0)
	charges = append(charges, getExpectedFullyEarnedCharges(policyNumber, chunkDates.Start, expectedPremiumsOrRates)...)
	charges = append(charges, getExpectedALCharges(chunkDates, expectedPremiumsOrRates)...)
	charges = append(charges, getExpectedAPDCharges(chunkDates, expectedPremiumsOrRates, packageType)...)
	charges = append(charges, getGLCharges(chunkDates.Start, expectedPremiumsOrRates)...)
	charges = append(charges, getMTCCharges(chunkDates, expectedPremiumsOrRates)...)
	return charges
}

func getExpectedFullyEarnedCharges(
	policyNumber string,
	chunkStartDate *timestamppb.Timestamp,
	expectedPremiumsOrRates map[string]float64,
) []*ptypes.Charge {
	return []*ptypes.Charge{
		ptypes.NewChargeBuilder().
			WithChargeablePolicy(policyNumber).
			WithBaseChargeTypeWithBlanketRegularAdditionalInsured(bipdSubCovGroup).
			WithAmountBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "bipdBlanketRegularAdditionalInsured"),
				chunkStartDate,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeablePolicy(policyNumber).
			WithBaseChargeTypeWithBlanketPrimaryAndNonContributoryAdditionalInsured(bipdSubCovGroup).
			WithAmountBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "bipdBlanketPNCAdditionalInsured"),
				chunkStartDate,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeablePolicy(policyNumber).
			WithBaseChargeTypeWithBlanketWaiverOfSubrogation(bipdSubCovGroup).
			WithAmountBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "bipdBlanketWaiverOfSubrogation"),
				chunkStartDate,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeablePolicy(policyNumber).
			WithBaseChargeTypeWithSpecifiedRegularAdditionalInsured(
				bipdSubCovGroup,
				"bipd-regular-ai-1",
			).
			WithAmountBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "bipdSpecifiedRegularAdditionalInsured"),
				chunkStartDate,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeablePolicy(policyNumber).
			WithBaseChargeTypeWithSpecifiedRegularAdditionalInsured(
				bipdSubCovGroup,
				"bipd-regular-ai-2",
			).
			WithAmountBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "bipdSpecifiedRegularAdditionalInsured"),
				chunkStartDate,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeablePolicy(policyNumber).
			WithBaseChargeTypeWithSpecifiedPrimaryAndNonContributoryAdditionalInsured(
				bipdSubCovGroup,
				"bipd-pnc-ai-1",
			).
			WithAmountBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "bipdSpecifiedPNCAdditionalInsured"),
				chunkStartDate,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeablePolicy(policyNumber).
			WithBaseChargeTypeWithSpecifiedPrimaryAndNonContributoryAdditionalInsured(
				bipdSubCovGroup,
				"bipd-pnc-ai-2",
			).
			WithAmountBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "bipdSpecifiedPNCAdditionalInsured"),
				chunkStartDate,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeablePolicy(policyNumber).
			WithBaseChargeTypeWithSpecifiedThirdPartyWithWaiverOfSubrogation(
				bipdSubCovGroup,
				"bipd-wos-1",
			).
			WithAmountBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "bipdSpecifiedWaiverOfSubrogation"),
				chunkStartDate,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeablePolicy(policyNumber).
			WithBaseChargeTypeWithSpecifiedThirdPartyWithWaiverOfSubrogation(
				bipdSubCovGroup,
				"bipd-wos-2",
			).
			WithAmountBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "bipdSpecifiedWaiverOfSubrogation"),
				chunkStartDate,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeablePolicy(policyNumber).
			WithBaseChargeTypeWithBlanketRegularAdditionalInsured(glSubCovGroup).
			WithAmountBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "glBlanketRegularAdditionalInsured"),
				chunkStartDate,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeablePolicy(policyNumber).
			WithBaseChargeTypeWithBlanketPrimaryAndNonContributoryAdditionalInsured(glSubCovGroup).
			WithAmountBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "glBlanketPNCAdditionalInsured"),
				chunkStartDate,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeablePolicy(policyNumber).
			WithBaseChargeTypeWithBlanketWaiverOfSubrogation(glSubCovGroup).
			WithAmountBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "glBlanketWaiverOfSubrogation"),
				chunkStartDate,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeablePolicy(policyNumber).
			WithBaseChargeTypeWithSpecifiedRegularAdditionalInsured(
				glSubCovGroup,
				"gl-regular-ai-1",
			).
			WithAmountBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "glSpecifiedRegularAdditionalInsured"),
				chunkStartDate,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeablePolicy(policyNumber).
			WithBaseChargeTypeWithSpecifiedRegularAdditionalInsured(
				glSubCovGroup,
				"gl-regular-ai-2",
			).
			WithAmountBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "glSpecifiedRegularAdditionalInsured"),
				chunkStartDate,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeablePolicy(policyNumber).
			WithBaseChargeTypeWithSpecifiedPrimaryAndNonContributoryAdditionalInsured(
				glSubCovGroup,
				"gl-pnc-ai-1",
			).
			WithAmountBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "glSpecifiedPNCAdditionalInsured"),
				chunkStartDate,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeablePolicy(policyNumber).
			WithBaseChargeTypeWithSpecifiedPrimaryAndNonContributoryAdditionalInsured(
				glSubCovGroup,
				"gl-pnc-ai-2",
			).
			WithAmountBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "glSpecifiedPNCAdditionalInsured"),
				chunkStartDate,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeablePolicy(policyNumber).
			WithBaseChargeTypeWithSpecifiedThirdPartyWithWaiverOfSubrogation(
				glSubCovGroup,
				"gl-wos-1",
			).
			WithAmountBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "glSpecifiedWaiverOfSubrogation"),
				chunkStartDate,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeablePolicy(policyNumber).
			WithBaseChargeTypeWithSpecifiedThirdPartyWithWaiverOfSubrogation(
				glSubCovGroup,
				"gl-wos-2",
			).
			WithAmountBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "glSpecifiedWaiverOfSubrogation"),
				chunkStartDate,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeablePolicy(policyNumber).
			WithBaseChargeTypeWithBlanketRegularAdditionalInsured(cargoSubCovGroup).
			WithAmountBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "cargoBlanketRegularAdditionalInsured"),
				chunkStartDate,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeablePolicy(policyNumber).
			WithBaseChargeTypeWithBlanketWaiverOfSubrogation(cargoSubCovGroup).
			WithAmountBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "cargoBlanketWaiverOfSubrogation"),
				chunkStartDate,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeablePolicy(policyNumber).
			WithBaseChargeTypeWithSpecifiedRegularAdditionalInsured(
				cargoSubCovGroup,
				"cargo-regular-ai-1",
			).
			WithAmountBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "cargoSpecifiedRegularAdditionalInsured"),
				chunkStartDate,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeablePolicy(policyNumber).
			WithBaseChargeTypeWithSpecifiedRegularAdditionalInsured(
				cargoSubCovGroup,
				"cargo-regular-ai-2",
			).
			WithAmountBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "cargoSpecifiedRegularAdditionalInsured"),
				chunkStartDate,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeablePolicy(policyNumber).
			WithBaseChargeTypeWithSpecifiedThirdPartyWithWaiverOfSubrogation(
				cargoSubCovGroup,
				"cargo-wos-1",
			).
			WithAmountBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "cargoSpecifiedWaiverOfSubrogation"),
				chunkStartDate,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeablePolicy(policyNumber).
			WithBaseChargeTypeWithSpecifiedThirdPartyWithWaiverOfSubrogation(
				cargoSubCovGroup,
				"cargo-wos-2",
			).
			WithAmountBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "cargoSpecifiedWaiverOfSubrogation"),
				chunkStartDate,
			).
			Build(),
	}
}

func getExpectedALCharges(
	chunkDates *proto.Interval,
	expectedPremiumsOrRates map[string]float64,
) []*ptypes.Charge {
	return []*ptypes.Charge{
		ptypes.NewChargeBuilder().
			WithChargeableSubCoverageGroup(
				ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
			).
			WithBaseChargeTypeWithoutExtraInfo().
			WithRateBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "liabBase"),
				chunkDates,
				ptypes.RateBasis_RateBasis_Miles,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeableSubCoverageGroup(
				ptypes.SubCoverageType_SubCoverageType_MedicalPayments,
			).
			WithBaseChargeTypeWithoutExtraInfo().
			WithRateBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "medPay"),
				chunkDates,
				ptypes.RateBasis_RateBasis_Miles,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeableSubCoverageGroup(
				ptypes.SubCoverageType_SubCoverageType_PersonalInjuryProtection,
			).
			WithBaseChargeTypeWithoutExtraInfo().
			WithRateBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "pip"),
				chunkDates,
				ptypes.RateBasis_RateBasis_Miles,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeableSubCoverageGroup(
				ptypes.SubCoverageType_SubCoverageType_GuestPersonalInjuryProtection,
			).
			WithBaseChargeTypeWithoutExtraInfo().
			WithRateBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "guestPip"),
				chunkDates,
				ptypes.RateBasis_RateBasis_Miles,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeableSubCoverageGroup(
				ptypes.SubCoverageType_SubCoverageType_PIPAttendantCare,
			).
			WithBaseChargeTypeWithoutExtraInfo().
			WithRateBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "pipAC"),
				chunkDates,
				ptypes.RateBasis_RateBasis_Miles,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeableSubCoverageGroup(
				ptypes.SubCoverageType_SubCoverageType_PropertyProtectionInsurance,
			).
			WithBaseChargeTypeWithoutExtraInfo().
			WithRateBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "ppi"),
				chunkDates,
				ptypes.RateBasis_RateBasis_Miles,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeableSubCoverageGroup(
				ptypes.SubCoverageType_SubCoverageType_BroadenedPollution,
			).
			WithBaseChargeTypeWithoutExtraInfo().
			WithRateBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "broadenedPollution"),
				chunkDates,
				ptypes.RateBasis_RateBasis_Miles,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeableSubCoverageGroup(
				ptypes.SubCoverageType_SubCoverageType_UninsuredMotorist,
			).
			WithBaseChargeTypeWithoutExtraInfo().
			WithRateBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "um"),
				chunkDates,
				ptypes.RateBasis_RateBasis_Miles,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeableSubCoverageGroup(
				ptypes.SubCoverageType_SubCoverageType_UnderInsuredMotorist,
			).
			WithBaseChargeTypeWithoutExtraInfo().
			WithRateBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "uim"),
				chunkDates,
				ptypes.RateBasis_RateBasis_Miles,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeableSubCoverageGroup(
				ptypes.SubCoverageType_SubCoverageType_UMUIM,
			).
			WithBaseChargeTypeWithoutExtraInfo().
			WithRateBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "umUim"),
				chunkDates,
				ptypes.RateBasis_RateBasis_Miles,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeableSubCoverageGroup(
				ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage,
			).
			WithBaseChargeTypeWithoutExtraInfo().
			WithRateBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "umpd"),
				chunkDates,
				ptypes.RateBasis_RateBasis_Miles,
			).
			Build(),
	}
}

func getExpectedAPDCharges(
	chunkDates *proto.Interval,
	expectedPremiumsOrRates map[string]float64,
	packageType app_enums.IndicationOptionTag,
) []*ptypes.Charge {
	charges := []*ptypes.Charge{
		ptypes.NewChargeBuilder().
			WithChargeableSubCoverageGroup(
				ptypes.SubCoverageType_SubCoverageType_Collision,
			).
			WithBaseChargeTypeWithoutExtraInfo().
			WithRateBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "coll"),
				chunkDates,
				ptypes.RateBasis_RateBasis_TIV_Days,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeableSubCoverageGroup(
				ptypes.SubCoverageType_SubCoverageType_Comprehensive,
			).
			WithBaseChargeTypeWithoutExtraInfo().
			WithRateBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "comp"),
				chunkDates,
				ptypes.RateBasis_RateBasis_TIV_Days,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeableSubCoverageGroup(
				ptypes.SubCoverageType_SubCoverageType_TrailerInterchange,
			).WithBaseChargeTypeWithoutExtraInfo().
			WithRateBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "trailerInterchange"),
				chunkDates,
				ptypes.RateBasis_RateBasis_TIV_Days,
			).
			Build(),
	}

	if packageType != app_enums.IndicationOptionTagBasic {
		charges = append(charges, ptypes.NewChargeBuilder().
			WithChargeableSubCoverageGroup(
				ptypes.SubCoverageType_SubCoverageType_Towing,
				ptypes.SubCoverageType_SubCoverageType_ElectronicEquipment,
				ptypes.SubCoverageType_SubCoverageType_MiscellaneousEquipment,
				ptypes.SubCoverageType_SubCoverageType_RentalReimbursement,
			).
			WithBaseChargeTypeWithoutExtraInfo().
			WithRateBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "apdPackage"),
				chunkDates,
				ptypes.RateBasis_RateBasis_TIV_Days,
			).
			Build(),
		)
	}

	return charges
}

func getGLCharges(
	chunkStartDate *timestamppb.Timestamp,
	expectedPremiumsOrRates map[string]float64,
) []*ptypes.Charge {
	return []*ptypes.Charge{
		ptypes.NewChargeBuilder().
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_GeneralLiability).
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "gl"),
				chunkStartDate,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_StopGap).
			WithBaseChargeTypeWithoutExtraInfo().
			WithAmountBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "stopGap"),
				chunkStartDate,
			).
			Build(),
	}
}

func getMTCCharges(
	chunkDates *proto.Interval,
	expectedPremiumsOrRates map[string]float64,
) []*ptypes.Charge {
	return []*ptypes.Charge{
		ptypes.NewChargeBuilder().
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_Cargo).
			WithBaseChargeTypeWithoutExtraInfo().
			WithRateBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "cargo"),
				chunkDates,
				ptypes.RateBasis_RateBasis_Miles,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_ReeferWithoutHumanError).
			WithBaseChargeTypeWithoutExtraInfo().
			WithRateBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "reeferBreakdownWithoutHumanError"),
				chunkDates,
				ptypes.RateBasis_RateBasis_Miles,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_ReeferWithHumanError).
			WithBaseChargeTypeWithoutExtraInfo().
			WithRateBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "reeferBreakdownWithHumanError"),
				chunkDates,
				ptypes.RateBasis_RateBasis_Miles,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_CargoAtScheduledTerminals).
			WithBaseChargeTypeWithoutExtraInfo().
			WithRateBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "cargoAtTerminal"),
				chunkDates,
				ptypes.RateBasis_RateBasis_Miles,
			).
			Build(),
		ptypes.NewChargeBuilder().
			WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_TrailerInterchange).
			WithBaseChargeTypeWithoutExtraInfo().
			WithRateBasedBillingDetails(
				premiumOrRateToString(expectedPremiumsOrRates, "mtcTrailerInterchange"),
				chunkDates,
				ptypes.RateBasis_RateBasis_Miles,
			).
			Build(),
	}
}

func premiumOrRateToString(expectedPremiums map[string]float64, key string) string {
	return decimal.NewFromFloat(expectedPremiums[key]).String()
}
