package engine

import (
	"context"

	"github.com/cockroachdb/errors"
	"go.uber.org/fx"
	"google.golang.org/protobuf/proto"

	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/common"
	"nirvanatech.com/nirvana/rating/pricing/api/parsers"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
	"nirvanatech.com/nirvana/rating/pricing/api/validators"
)

// EngineI is an interface we created to hide the concrete
// implementations that are used in the pricing server.
//
//go:generate go run go.uber.org/mock/mockgen -destination=engine_mock.go -mock_names=EngineI=MockEngine -package=engine nirvanatech.com/nirvana/rating/pricing/api/engine EngineI
//goland:noinspection GoNameStartsWithPackageName
type EngineI interface {
	Run(ctx context.Context, request *ptypes.Request) (*ptypes.Response, error)
}

type Deps struct {
	fx.In

	RequestValidator validators.RequestValidatorI

	ResponseBuilder ResponseBuilderI

	ModelExecutorFactory ModelExecutorFactory

	ModelKeyParser parsers.ModelKeyParserI

	PluginsHelper plugins.HelperI
}

type Engine struct {
	deps Deps
}

var _ EngineI = (*Engine)(nil)

func newEngine(deps Deps) EngineI {
	return &Engine{deps: deps}
}

// Run calls rating models and apply plugins accordingly,
// to get a "price output" for each policy and chunk.
//
// Note that it runs a different plugin chain for each policy.
func (e *Engine) Run(
	ctx context.Context,
	request *ptypes.Request,
) (*ptypes.Response, error) {
	err := e.deps.RequestValidator.Validate(ctx, request)
	if err != nil {
		return nil, err
	}

	// The key is the policy number
	policyOutputs := make(map[string]common.PluginChainOutput)
	for _, policySpec := range request.PolicySpecs {
		policyNumber := policySpec.PolicyNumber

		pluginChainOutput, err := e.runPluginChainForPolicy(ctx, request, policySpec)
		if err != nil {
			return nil, errors.Wrapf(err, "failed for policy %s", policyNumber)
		}

		policyOutputs[policyNumber] = pluginChainOutput
	}

	response, err := e.deps.ResponseBuilder.Build(
		request.PolicySpecs,
		policyOutputs,
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to build response")
	}

	return response, nil
}

func (e *Engine) runPluginChainForPolicy(
	ctx context.Context,
	request *ptypes.Request,
	policySpec *ptypes.PolicySpec,
) (common.PluginChainOutput, error) {
	policyNumber := policySpec.PolicyNumber

	modelExecutor := e.deps.ModelExecutorFactory()

	pluginIDs, err := e.getPluginIDs(policySpec)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get plugins for policy %s", policyNumber)
	}

	executePluginChainFn, err := e.buildPluginChain(modelExecutor, pluginIDs)
	if err != nil {
		return nil, errors.Wrap(err, "failed to build plugin chain")
	}

	// We create a copy of the request so that each policy is priced completely
	// independently. This is important because plugins may modify the request
	// without restoring it to the original request.
	requestCopy := proto.Clone(request).(*ptypes.Request)

	pluginChainOutput, err := executePluginChainFn(ctx, &common.PluginChainInput{
		Request:      requestCopy,
		PolicyNumber: policyNumber,
	})
	if err != nil {
		return nil, errors.Wrap(err, "error executing plugin chain")
	}

	return pluginChainOutput, nil
}

func (e *Engine) getPluginIDs(policySpec *ptypes.PolicySpec) ([]ptypes.PluginID, error) {
	modelKey, err := e.deps.ModelKeyParser.Parse(policySpec.ModelSpec)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to parse model key for policy")
	}

	mandatoryPlugins := e.deps.PluginsHelper.GetMandatoryPlugins(*modelKey)
	optionalPlugins := policySpec.OptionalPlugins
	allPlugins := slice_utils.Dedup(append(mandatoryPlugins, optionalPlugins...))

	e.deps.PluginsHelper.SortPlugins(allPlugins)

	err = e.deps.PluginsHelper.ValidatePlugins(allPlugins)
	if err != nil {
		return nil, errors.Wrap(err, "failed to validate plugins for policy")
	}

	return allPlugins, nil
}

func (e *Engine) buildPluginChain(
	executor ModelExecutorI,
	pluginIDs []ptypes.PluginID,
) (common.PluginApplyFn, error) {
	if len(pluginIDs) == 0 {
		return executor.Execute, nil
	}

	// We need to reverse the plugin IDs before creating the plugin chain,
	// because of how function composition works.
	slice_utils.Reverse(pluginIDs)

	ps := make([]common.PluginI, 0)
	for _, pluginID := range pluginIDs {
		pluginFactory := e.deps.PluginsHelper.GetPluginFactory(pluginID)
		if pluginFactory == nil {
			return nil, errors.Newf("plugin factory is nil for plugin ID: %s", pluginID)
		}

		plugin, err := pluginFactory()
		if err != nil {
			return nil, errors.Wrapf(err, "failed to create plugin with ID: %s", pluginID)
		}

		ps = append(ps, plugin)
	}

	// For the first plugin in the slice (last in chain),
	// we set the next plugin apply function to the helper
	// function that executes the RateML model for a given
	// request.
	err := ps[0].SetNextPluginApplyFn(executor.Execute)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to set apply function for plugin %s", pluginIDs[0])
	}

	// For the rest of the plugins, we set the next plugin apply
	// function equal to the function of the previous plugin in
	// the slice (which is the next plugin in the chain).
	for i := 1; i < len(ps); i++ {
		err = ps[i].SetNextPluginApplyFn(ps[i-1].Apply)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to set apply function for plugin %s", pluginIDs[i])
		}
	}

	// We return the apply function of the last plugin in the slice,
	// which is the first plugin in the chain.
	return ps[len(ps)-1].Apply, nil
}
