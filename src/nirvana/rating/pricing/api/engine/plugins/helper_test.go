package plugins

import (
	"context"
	"testing"

	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"go.uber.org/mock/gomock"

	"nirvanatech.com/nirvana/common-go/us_states"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/common"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
	"nirvanatech.com/nirvana/rating/rtypes"
)

type engineTestEnv struct {
	fx.In

	Helper HelperI
}

type EngineTestSuite struct {
	suite.Suite

	env   engineTestEnv
	fxApp *fxtest.App
	ctx   context.Context
}

func TestEngine(t *testing.T) {
	suite.Run(t, new(EngineTestSuite))
}

func (s *EngineTestSuite) SetupSuite() {
	s.ctx = context.Background()
}

func (s *EngineTestSuite) startFxApp(ops ...fx.Option) {
	ops = append(
		ops,
	)

	s.fxApp = testloader.RequireStart(
		s.T(),
		&s.env,
		testloader.Use(ops...),
	)
}

func (s *EngineTestSuite) TearDownSuite() {
	s.fxApp.RequireStop()
}

func (s *EngineTestSuite) Test_GetPluginsMethods() {
	mk1 := rtypes.NewModelKey(rtypes.ProviderProgressive, us_states.CA, rtypes.Version011)
	mk2 := rtypes.NewModelKey(rtypes.ProviderSentry, us_states.IL, rtypes.Version101)
	mk3 := rtypes.NewModelKey(rtypes.ProviderSentryMST, us_states.SC, rtypes.Version003)

	mockOptionalPlugins1 := []ptypes.PluginID{
		ptypes.PluginID_PluginID_MockPlugin_V2,
		ptypes.PluginID_PluginID_MockPlugin_V3,
	}
	mockMandatoryPlugins1 := []ptypes.PluginID{
		ptypes.PluginID_PluginID_MockPlugin_V1,
		ptypes.PluginID_PluginID_MockPlugin_V2,
	}

	mockOptionalPlugins2 := []ptypes.PluginID{
		ptypes.PluginID_PluginID_MockPlugin_V1,
		ptypes.PluginID_PluginID_MockPlugin_V4,
	}
	mockMandatoryPlugins2 := []ptypes.PluginID{
		ptypes.PluginID_PluginID_MockPlugin_V3,
		ptypes.PluginID_PluginID_MockPlugin_V5,
	}

	mockSupportedMap := SupportedMap{
		mk1: {
			optionalPlugins:  mockOptionalPlugins1,
			mandatoryPlugins: mockMandatoryPlugins1,
		},
		mk2: {
			optionalPlugins:  mockOptionalPlugins2,
			mandatoryPlugins: mockMandatoryPlugins2,
		},
	}

	s.startFxApp(fx.Decorate(func() SupportedMap {
		return mockSupportedMap
	}))

	optionalPlugins1 := s.env.Helper.GetOptionalPlugins(mk1)
	s.Require().Equal(mockOptionalPlugins1, optionalPlugins1)
	mandatoryPlugins1 := s.env.Helper.GetMandatoryPlugins(mk1)
	s.Require().Equal(mockMandatoryPlugins1, mandatoryPlugins1)

	optionalPlugins2 := s.env.Helper.GetOptionalPlugins(mk2)
	s.Require().Equal(mockOptionalPlugins2, optionalPlugins2)
	mandatoryPlugins2 := s.env.Helper.GetMandatoryPlugins(mk2)
	s.Require().Equal(mockMandatoryPlugins2, mandatoryPlugins2)

	optionalPlugins3 := s.env.Helper.GetOptionalPlugins(mk3)
	s.Require().Nil(optionalPlugins3)
	mandatoryPlugins3 := s.env.Helper.GetMandatoryPlugins(mk3)
	s.Require().Nil(mandatoryPlugins3)
}

func (s *EngineTestSuite) Test_GetPluginFactory() {
	ctrl := gomock.NewController(s.T())

	mockPlugin1 := common.NewMockPlugin(ctrl)
	mockPlugin2 := common.NewMockPlugin(ctrl)

	mockFactory1 := func() (common.PluginI, error) {
		return mockPlugin1, nil
	}
	mockFactory2 := func() (common.PluginI, error) {
		return mockPlugin2, nil
	}

	mockFactoriesMap := FactoriesMap{
		ptypes.PluginID_PluginID_MockPlugin_V1: mockFactory1,
		ptypes.PluginID_PluginID_MockPlugin_V2: mockFactory2,
	}

	s.startFxApp(fx.Decorate(func() FactoriesMap {
		return mockFactoriesMap
	}))

	factory1 := s.env.Helper.GetPluginFactory(ptypes.PluginID_PluginID_MockPlugin_V1)
	plugin1, err := factory1()
	s.Require().NoError(err)
	s.Require().Equal(mockPlugin1, plugin1)

	factory2 := s.env.Helper.GetPluginFactory(ptypes.PluginID_PluginID_MockPlugin_V2)
	plugin2, err := factory2()
	s.Require().NoError(err)
	s.Require().Equal(mockPlugin2, plugin2)

	factory3 := s.env.Helper.GetPluginFactory(ptypes.PluginID_PluginID_MockPlugin_V3)
	s.Require().Nil(factory3)
}

func (s *EngineTestSuite) Test_SortPlugins() {
	mockOrderList := OrderList{
		ptypes.PluginID_PluginID_MockPlugin_V4,
		ptypes.PluginID_PluginID_MockPlugin_V1,
		ptypes.PluginID_PluginID_MockPlugin_V3,
		ptypes.PluginID_PluginID_MockPlugin_V5,
		ptypes.PluginID_PluginID_MockPlugin_V2,
	}

	s.startFxApp(fx.Decorate(func() OrderList {
		return mockOrderList
	}))

	// With some plugins (case 1)
	plugins := []ptypes.PluginID{
		ptypes.PluginID_PluginID_MockPlugin_V1,
		ptypes.PluginID_PluginID_MockPlugin_V2,
		ptypes.PluginID_PluginID_MockPlugin_V3,
	}
	expectedPlugins := []ptypes.PluginID{
		ptypes.PluginID_PluginID_MockPlugin_V1,
		ptypes.PluginID_PluginID_MockPlugin_V3,
		ptypes.PluginID_PluginID_MockPlugin_V2,
	}
	s.env.Helper.SortPlugins(plugins)
	s.Require().Equal(expectedPlugins, plugins)

	// With some plugins (case 2)
	plugins = []ptypes.PluginID{
		ptypes.PluginID_PluginID_MockPlugin_V3,
		ptypes.PluginID_PluginID_MockPlugin_V4,
		ptypes.PluginID_PluginID_MockPlugin_V5,
	}
	expectedPlugins = []ptypes.PluginID{
		ptypes.PluginID_PluginID_MockPlugin_V4,
		ptypes.PluginID_PluginID_MockPlugin_V3,
		ptypes.PluginID_PluginID_MockPlugin_V5,
	}
	s.env.Helper.SortPlugins(plugins)
	s.Require().Equal(expectedPlugins, plugins)

	// With all plugins
	plugins = []ptypes.PluginID{
		ptypes.PluginID_PluginID_MockPlugin_V1,
		ptypes.PluginID_PluginID_MockPlugin_V2,
		ptypes.PluginID_PluginID_MockPlugin_V3,
		ptypes.PluginID_PluginID_MockPlugin_V4,
		ptypes.PluginID_PluginID_MockPlugin_V5,
	}
	s.env.Helper.SortPlugins(plugins)
	s.Require().Equal([]ptypes.PluginID(mockOrderList), plugins)
}

func (s *EngineTestSuite) Test_ValidatePlugins() {
	mockExclusivenessMap := ExclusivenessMap{
		ptypes.PluginID_PluginID_MockPlugin_V1: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MockPlugin_V2,
			ptypes.PluginID_PluginID_MockPlugin_V3,
		},
		ptypes.PluginID_PluginID_MockPlugin_V2: []ptypes.PluginID{
			ptypes.PluginID_PluginID_MockPlugin_V4,
		},
	}

	s.startFxApp(fx.Decorate(func() ExclusivenessMap {
		return mockExclusivenessMap
	}))

	plugins1 := []ptypes.PluginID{
		ptypes.PluginID_PluginID_MockPlugin_V1,
		ptypes.PluginID_PluginID_MockPlugin_V4,
		ptypes.PluginID_PluginID_MockPlugin_V5,
	}
	err := s.env.Helper.ValidatePlugins(plugins1)
	s.Require().NoError(err)

	plugins2 := append(plugins1, ptypes.PluginID_PluginID_MockPlugin_V2)
	err = s.env.Helper.ValidatePlugins(plugins2)
	s.Require().ErrorIs(err, ConflictingPluginsError)

	plugins3 := append(plugins1, ptypes.PluginID_PluginID_MockPlugin_V3)
	err = s.env.Helper.ValidatePlugins(plugins3)
	s.Require().ErrorIs(err, ConflictingPluginsError)

	plugins4 := []ptypes.PluginID{
		ptypes.PluginID_PluginID_MockPlugin_V2,
		ptypes.PluginID_PluginID_MockPlugin_V3,
		ptypes.PluginID_PluginID_MockPlugin_V5,
	}
	err = s.env.Helper.ValidatePlugins(plugins4)
	s.Require().NoError(err)

	plugins5 := append(plugins4, ptypes.PluginID_PluginID_MockPlugin_V4)
	err = s.env.Helper.ValidatePlugins(plugins5)
	s.Require().ErrorIs(err, ConflictingPluginsError)

	plugins6 := append(plugins4, ptypes.PluginID_PluginID_MockPlugin_V1)
	err = s.env.Helper.ValidatePlugins(plugins6)
	s.Require().ErrorIs(err, ConflictingPluginsError)
}
