load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "charges_proration_v1",
    srcs = [
        "charges_prorater.go",
        "charges_prorater_mock.go",
        "fx.go",
        "plugin.go",
    ],
    importpath = "nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/common/charges_proration_v1",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/crypto_utils",
        "//nirvana/common-go/map_utils",
        "//nirvana/common-go/proto",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/rating/pricing/api/engine/plugins/common",
        "//nirvana/rating/pricing/api/ptypes",
        "//nirvana/rating/pricing/api/utils",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_shopspring_decimal//:decimal",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_mock//gomock",
    ],
)

go_test(
    name = "charges_proration_v1_test",
    srcs = [
        "charges_prorater_test.go",
        "plugin_test.go",
    ],
    embed = [":charges_proration_v1"],
    deps = [
        "//nirvana/common-go/proto",
        "//nirvana/common-go/time_utils",
        "//nirvana/infra/fx/testloader",
        "//nirvana/rating/pricing/api/engine/plugins/common",
        "//nirvana/rating/pricing/api/ptypes",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_stretchr_testify//suite",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
        "@org_uber_go_mock//gomock",
    ],
)
