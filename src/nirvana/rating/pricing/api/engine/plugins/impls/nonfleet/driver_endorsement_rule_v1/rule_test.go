package driver_endorsement_rule_v1

import (
	"context"
	"regexp"
	"strconv"
	"testing"

	"github.com/cockroachdb/errors"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"go.uber.org/mock/gomock"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	common_proto "nirvanatech.com/nirvana/common-go/proto"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	"nirvanatech.com/nirvana/nonfleet/model"
	plugins_common "nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/common"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

type ruleTestEnv struct {
	fx.In

	Deps Deps
}

type ruleTestSuite struct {
	suite.Suite

	ctx  context.Context
	ctrl *gomock.Controller

	env ruleTestEnv

	fxapp *fxtest.App

	rule *Rule

	mockChargeDate *timestamppb.Timestamp
}

func TestRule(t *testing.T) {
	suite.Run(t, new(ruleTestSuite))
}

func (s *ruleTestSuite) SetupSuite() {
	s.ctx = context.Background()
	s.ctrl = gomock.NewController(s.T())

	// We use the same date for all charges, in order to reduce the complexity of the tests.
	// We rely on different amounts to distinguish between charges.
	s.mockChargeDate = timestamppb.Now()

	s.fxapp = testloader.RequireStart(
		s.T(),
		&s.env,
	)
}

func (s *ruleTestSuite) SetupTest() {
	s.rule = NewRule(s.env.Deps)
}

func (s *ruleTestSuite) TearDownSuite() {
	s.fxapp.RequireStop()
}

func (s *ruleTestSuite) TestApply_WithNilInput() {
	output, err := s.rule.Apply(s.ctx, nil)
	s.Require().Error(err)
	s.Require().Regexp("input can't be nil", err.Error())
	s.Require().Nil(output)
}

func (s *ruleTestSuite) TestApply_WithNilNextPluginFn() {
	input := &plugins_common.PluginChainInput{}

	output, err := s.rule.Apply(s.ctx, input)
	s.Require().Error(err)
	s.Require().Regexp("nextPluginApplyFn can't be nil", err.Error())
	s.Require().Nil(output)
}

func (s *ruleTestSuite) TestApply_WithNilRequest() {
	input := &plugins_common.PluginChainInput{
		PolicyNumber: "NFMCK0142",
	}

	nextPluginApplyFn := func(_ context.Context, _ *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
		return nil, nil
	}
	err := s.rule.SetNextPluginApplyFn(nextPluginApplyFn)
	s.Require().NoError(err)

	output, err := s.rule.Apply(s.ctx, input)
	s.Require().Error(err)
	s.Require().Regexp("request can't be nil", err.Error())
	s.Require().Nil(output)
}

func (s *ruleTestSuite) TestApply_WithEmptyPolicyNumber() {
	input := &plugins_common.PluginChainInput{
		Request: &ptypes.Request{
			BundleSpec: &ptypes.BundleSpec{},
		},
	}

	nextPluginApplyFn := func(_ context.Context, _ *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
		return nil, nil
	}
	err := s.rule.SetNextPluginApplyFn(nextPluginApplyFn)
	s.Require().NoError(err)

	output, err := s.rule.Apply(s.ctx, input)
	s.Require().Error(err)
	s.Require().Regexp("PolicyNumber can't be empty", err.Error())
	s.Require().Nil(output)
}

func (s *ruleTestSuite) TestApply_WhenPolicySpecNotPresentForPolicyNumber() {
	policyNumber := "NFMCK0142"

	request := &ptypes.Request{
		BundleSpec: &ptypes.BundleSpec{},
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: "NFMCK0141",
			},
			{
				PolicyNumber: "NFMCK0143",
			},
		},
	}

	input := &plugins_common.PluginChainInput{
		PolicyNumber: policyNumber,
		Request:      request,
	}

	nextPluginApplyFn := func(_ context.Context, _ *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
		return nil, nil
	}
	err := s.rule.SetNextPluginApplyFn(nextPluginApplyFn)
	s.Require().NoError(err)

	output, err := s.rule.Apply(s.ctx, input)
	s.Require().Error(err)
	s.Require().Regexp("policy spec not found", err.Error())
	s.Require().Nil(output)
}

func (s *ruleTestSuite) TestApply_WithNilBundleSpec() {
	policyNumber := "NFMCK0142"

	request := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: policyNumber,
			},
		},
	}

	input := &plugins_common.PluginChainInput{
		PolicyNumber: policyNumber,
		Request:      request,
	}

	nextPluginApplyFn := func(_ context.Context, _ *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
		return nil, nil
	}
	err := s.rule.SetNextPluginApplyFn(nextPluginApplyFn)
	s.Require().NoError(err)

	output, err := s.rule.Apply(s.ctx, input)
	s.Require().Error(err)
	s.Require().Regexp("bundleSpec can't be nil", err.Error())
	s.Require().Nil(output)
}

func (s *ruleTestSuite) TestApply_WithNoPolicyChunks() {
	policyNumber := "NFMCK0142"

	request := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: policyNumber,
			},
		},
		BundleSpec: &ptypes.BundleSpec{},
	}

	input := &plugins_common.PluginChainInput{
		PolicyNumber: policyNumber,
		Request:      request,
	}

	nextPluginApplyFn := func(_ context.Context, _ *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
		return nil, nil
	}
	err := s.rule.SetNextPluginApplyFn(nextPluginApplyFn)
	s.Require().NoError(err)

	expected := make(plugins_common.PluginChainOutput)

	output, err := s.rule.Apply(s.ctx, input)
	s.Require().NoError(err)
	s.Require().Equal(expected, output)
}

func (s *ruleTestSuite) TestApply_WithNoNonFleetPolicyChunkData() {
	policyNumber := "NFMCK0142"

	request := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: policyNumber,
				ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
					{},
				},
			},
		},
		BundleSpec: &ptypes.BundleSpec{},
	}

	input := &plugins_common.PluginChainInput{
		PolicyNumber: policyNumber,
		Request:      request,
	}

	nextPluginApplyFn := func(_ context.Context, _ *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
		return nil, nil
	}
	err := s.rule.SetNextPluginApplyFn(nextPluginApplyFn)
	s.Require().NoError(err)

	output, err := s.rule.Apply(s.ctx, input)
	s.Require().Error(err)
	s.Require().Regexp("NF policy chunk spec data not found", err.Error())
	s.Require().Nil(output)
}

func (s *ruleTestSuite) TestApply_WithOneChunk_WithPolicyChunkNotMatchingAnyBundleChunk() {
	policyNumber := "NFMCK0142"

	policyChunk := &ptypes.PolicySpec_ChunkSpec{
		ChunkId: "ID123",
		Data: &ptypes.PolicySpec_ChunkSpec_NonFleetPolicyChunkSpecData{
			NonFleetPolicyChunkSpecData: &ptypes.NonFleet_PolicyChunkSpecData{},
		},
	}
	bundleChunk := &ptypes.BundleSpec_ChunkSpec{
		ChunkId: "ID456",
	}
	request := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: policyNumber,
				ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
					policyChunk,
				},
			},
		},
		BundleSpec: &ptypes.BundleSpec{
			ChunkSpecs: []*ptypes.BundleSpec_ChunkSpec{
				bundleChunk,
			},
		},
	}

	input := &plugins_common.PluginChainInput{
		PolicyNumber: policyNumber,
		Request:      request,
	}

	nextPluginApplyFn := func(_ context.Context, _ *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
		return nil, nil
	}
	err := s.rule.SetNextPluginApplyFn(nextPluginApplyFn)
	s.Require().NoError(err)

	output, err := s.rule.Apply(s.ctx, input)
	s.Require().Error(err)
	s.Require().Regexp("bundle chunk spec not found", err.Error())
	s.Require().Nil(output)
}

func (s *ruleTestSuite) TestApply_WithNoNonFleetBundleChunkSpecData() {
	policyNumber := "NFMCK0142"
	chunkID := "ID123"

	policyChunk := &ptypes.PolicySpec_ChunkSpec{
		ChunkId: chunkID,
		Data: &ptypes.PolicySpec_ChunkSpec_NonFleetPolicyChunkSpecData{
			NonFleetPolicyChunkSpecData: &ptypes.NonFleet_PolicyChunkSpecData{},
		},
	}
	bundleChunk := &ptypes.BundleSpec_ChunkSpec{
		ChunkId: chunkID,
	}
	request := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: policyNumber,
				ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
					policyChunk,
				},
			},
		},
		BundleSpec: &ptypes.BundleSpec{
			ChunkSpecs: []*ptypes.BundleSpec_ChunkSpec{
				bundleChunk,
			},
		},
	}

	input := &plugins_common.PluginChainInput{
		PolicyNumber: policyNumber,
		Request:      request,
	}

	nextPluginApplyFn := func(_ context.Context, _ *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
		return nil, nil
	}
	err := s.rule.SetNextPluginApplyFn(nextPluginApplyFn)
	s.Require().NoError(err)

	output, err := s.rule.Apply(s.ctx, input)
	s.Require().Error(err)
	s.Require().Regexp("NF bundle chunk spec data not found", err.Error())
	s.Require().Nil(output)
}

func (s *ruleTestSuite) TestApply_WithOneChunk_WithErrorCallingNextPlugin() {
	policyNumber := "NFMCK0142"

	chunkID := "ID123"
	policyChunk := &ptypes.PolicySpec_ChunkSpec{
		ChunkId: chunkID,
		Data: &ptypes.PolicySpec_ChunkSpec_NonFleetPolicyChunkSpecData{
			NonFleetPolicyChunkSpecData: &ptypes.NonFleet_PolicyChunkSpecData{},
		},
	}
	bundleChunk := &ptypes.BundleSpec_ChunkSpec{
		ChunkId: chunkID,
		Data: &ptypes.BundleSpec_ChunkSpec_NonFleetBundleChunkSpecData{
			NonFleetBundleChunkSpecData: &ptypes.NonFleet_BundleChunkSpecData{},
		},
	}
	request := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: policyNumber,
				ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
					policyChunk,
				},
			},
		},
		BundleSpec: &ptypes.BundleSpec{
			ChunkSpecs: []*ptypes.BundleSpec_ChunkSpec{
				bundleChunk,
			},
		},
	}

	input := &plugins_common.PluginChainInput{
		PolicyNumber: policyNumber,
		Request:      request,
	}

	nextPluginApplyFnErr := errors.New("some error in pricing fn")
	nextPluginApplyFn := func(_ context.Context, _ *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
		return nil, nextPluginApplyFnErr
	}
	err := s.rule.SetNextPluginApplyFn(nextPluginApplyFn)
	s.Require().NoError(err)

	output, err := s.rule.Apply(s.ctx, input)
	s.Require().Error(err)
	s.Require().ErrorIs(err, nextPluginApplyFnErr)
	s.Require().Regexp(regexp.QuoteMeta("failed to get output (chunkID=ID123, policyNumber=NFMCK0142)"), err.Error())
	s.Require().Regexp("failed to call next plugin for chunk ID123", err.Error())
	s.Require().Nil(output)
}

func (s *ruleTestSuite) TestApply_WithOneChunk_WithSuccess() {
	policyNumber := "NFMCK0142"

	chunkID := "ID123"
	policyChunk := &ptypes.PolicySpec_ChunkSpec{
		ChunkId: chunkID,
		Data: &ptypes.PolicySpec_ChunkSpec_NonFleetPolicyChunkSpecData{
			NonFleetPolicyChunkSpecData: &ptypes.NonFleet_PolicyChunkSpecData{},
		},
	}
	bundleChunk := &ptypes.BundleSpec_ChunkSpec{
		ChunkId: chunkID,
		Data: &ptypes.BundleSpec_ChunkSpec_NonFleetBundleChunkSpecData{
			NonFleetBundleChunkSpecData: &ptypes.NonFleet_BundleChunkSpecData{},
		},
	}
	request := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: policyNumber,
				ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
					policyChunk,
				},
			},
		},
		BundleSpec: &ptypes.BundleSpec{
			ChunkSpecs: []*ptypes.BundleSpec_ChunkSpec{
				bundleChunk,
			},
		},
	}

	input := &plugins_common.PluginChainInput{
		PolicyNumber: policyNumber,
		Request:      request,
	}

	// We mock metadata to make sure that the plugin is not changing it
	mockChunkOutput := &plugins_common.ChunkOutput{
		Charges: []*ptypes.Charge{
			s.createNonFeeNonSurchargeCharge(123.34),
		},
		Metadata: &ptypes.ChunkOutput_Metadata{
			ProgramSpecificMetadata: ptypes.NewChunkOutputProgramSpecificMetadataMock(s.ctrl),
		},
	}
	nextPluginApplyFn := func(_ context.Context, inp *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
		s.Require().Equal(input, inp)
		return plugins_common.PluginChainOutput{chunkID: mockChunkOutput}, nil
	}
	err := s.rule.SetNextPluginApplyFn(nextPluginApplyFn)
	s.Require().NoError(err)

	output, err := s.rule.Apply(s.ctx, input)
	s.Require().NoError(err)
	s.Require().NotNil(output)
	s.Require().Len(output, 1)
	s.Require().Equal(mockChunkOutput, output[chunkID])
}

func (s *ruleTestSuite) TestApply_WithTwoChunks_WithNoChangesBetweenChunks() {
	// We create two chunks that are "equal", but data is in a different order.
	policyNumber := "NFMCK0142"

	chunkID1 := "ID123"
	chunkID2 := "ID456"

	startDate1 := time_utils.MustParseDate("02/01/2006", "01/01/2025")
	endDate1 := startDate1.AddDate(0, 6, 0)
	startDate2 := endDate1
	endDate2 := startDate2.AddDate(0, 6, 0)
	dates1 := &common_proto.Interval{
		Start: timestamppb.New(startDate1.ToTime()),
		End:   timestamppb.New(endDate1.ToTime()),
	}
	dates2 := &common_proto.Interval{
		Start: timestamppb.New(startDate2.ToTime()),
		End:   timestamppb.New(endDate2.ToTime()),
	}

	bundleChunk1 := &ptypes.BundleSpec_ChunkSpec{
		ChunkId: chunkID1,
		Dates:   dates1,
		Data: &ptypes.BundleSpec_ChunkSpec_NonFleetBundleChunkSpecData{
			NonFleetBundleChunkSpecData: &ptypes.NonFleet_BundleChunkSpecData{
				CombinedDeductibleSpecs: []*ptypes.CombinedDeductibleSpec{
					{
						SubCoverageGroups: []*ptypes.SubCoverageGroup{
							{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Cargo,
								},
							},
							{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_PIPAttendantCare,
								},
							},
						},
					},
					{
						SubCoverageGroups: []*ptypes.SubCoverageGroup{
							{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_UninsuredMotorist,
									ptypes.SubCoverageType_SubCoverageType_MedicalPayments,
								},
							},
							{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
									ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
								},
							},
						},
					},
				},
			},
		},
	}
	bundleChunk2 := &ptypes.BundleSpec_ChunkSpec{
		ChunkId: chunkID2,
		Dates:   dates2,
		Data: &ptypes.BundleSpec_ChunkSpec_NonFleetBundleChunkSpecData{
			NonFleetBundleChunkSpecData: &ptypes.NonFleet_BundleChunkSpecData{
				CombinedDeductibleSpecs: []*ptypes.CombinedDeductibleSpec{
					{
						SubCoverageGroups: []*ptypes.SubCoverageGroup{
							{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
									ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
								},
							},
							{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_MedicalPayments,
									ptypes.SubCoverageType_SubCoverageType_UninsuredMotorist,
								},
							},
						},
					},
					{
						SubCoverageGroups: []*ptypes.SubCoverageGroup{
							{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_PIPAttendantCare,
								},
							},
							{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Cargo,
								},
							},
						},
					},
				},
			},
		},
	}

	policyChunk1 := &ptypes.PolicySpec_ChunkSpec{
		ChunkId: chunkID1,
		Data: &ptypes.PolicySpec_ChunkSpec_NonFleetPolicyChunkSpecData{
			NonFleetPolicyChunkSpecData: &ptypes.NonFleet_PolicyChunkSpecData{
				Drivers: []*ptypes.NonFleet_Driver{
					{
						Id: "123",
						ViolationsInfo: &ptypes.NonFleet_DriverViolationsInfo{
							DriverId: "abc",
						},
					},
					{
						Id: "456",
						ViolationsInfo: &ptypes.NonFleet_DriverViolationsInfo{
							DriverId: "fgh",
						},
					},
				},
				CommoditiesInfo: &ptypes.NonFleet_CommoditiesInfo{
					Records: []*ptypes.NonFleet_CommodityRecord{
						{
							CommodityCategory: model.CommodityCategory_COMMODITY_CATEGORY_AIRCRAFT_ENGINES,
						},
						{
							CommodityCategory: model.CommodityCategory_COMMODITY_CATEGORY_PLASTIC_PRODUCTS,
						},
					},
				},
				UnderwriterInput: &ptypes.NonFleet_UnderwriterInput{
					ScheduleModifications: []*ptypes.ScheduleModification{
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Collision,
									ptypes.SubCoverageType_SubCoverageType_Comprehensive,
								},
							},
						},
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_MedicalPayments,
									ptypes.SubCoverageType_SubCoverageType_UninsuredMotorist,
								},
							},
						},
					},
				},
				Vehicles: []*ptypes.NonFleet_Vehicle{
					{
						Vin: "abc123",
					},
					{
						Vin: "def456",
					},
				},
			},
		},
		SpecifiedRegularAdditionalInsureds: []*ptypes.SpecifiedRegularAdditionalInsured{
			{
				Id: "SRAI-1",
			},
			{
				Id: "SRAI-2",
			},
		},
		SpecifiedPrimaryAndNonContributoryAdditionalInsureds: []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
			{
				Id: "SPNCAI-1",
			},
			{
				Id: "SPNCAI-2",
			},
		},
		SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
			{
				Id: "TP-1",
			},
			{
				Id: "TP-2",
			},
		},
		SubCoverages: []ptypes.SubCoverageType{
			ptypes.SubCoverageType_SubCoverageType_Collision,
			ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
			ptypes.SubCoverageType_SubCoverageType_Comprehensive,
			ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
		},
		LimitSpecs: []*ptypes.LimitSpec{
			{
				SubCoverageGroup: &ptypes.SubCoverageGroup{
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
					},
				},
				Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				Amount:  456.78,
			},
			{
				SubCoverageGroup: &ptypes.SubCoverageGroup{
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_Collision,
						ptypes.SubCoverageType_SubCoverageType_Comprehensive,
					},
				},
				Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				Amount:  45.6,
			},
			{
				SubCoverageGroup: &ptypes.SubCoverageGroup{
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_Collision,
						ptypes.SubCoverageType_SubCoverageType_Comprehensive,
					},
				},
				Cadence: ptypes.LimitCadenceType_LimitCadenceType_Aggregate,
				Amount:  98.7,
			},
		},
		DeductibleSpecs: []*ptypes.DeductibleSpec{
			{
				SubCoverageGroup: &ptypes.SubCoverageGroup{
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
					},
				},
				Amount: 78.9,
			},
			{
				SubCoverageGroup: &ptypes.SubCoverageGroup{
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_Collision,
						ptypes.SubCoverageType_SubCoverageType_Comprehensive,
					},
				},
				Amount: 101.23,
			},
		},
	}
	policyChunk2 := &ptypes.PolicySpec_ChunkSpec{
		ChunkId: chunkID2,
		Data: &ptypes.PolicySpec_ChunkSpec_NonFleetPolicyChunkSpecData{
			NonFleetPolicyChunkSpecData: &ptypes.NonFleet_PolicyChunkSpecData{
				Drivers: []*ptypes.NonFleet_Driver{
					{
						Id: "456",
						ViolationsInfo: &ptypes.NonFleet_DriverViolationsInfo{
							DriverId: "fgh",
						},
					},
					{
						Id: "123",
						ViolationsInfo: &ptypes.NonFleet_DriverViolationsInfo{
							DriverId: "abc",
						},
					},
				},
				CommoditiesInfo: &ptypes.NonFleet_CommoditiesInfo{
					Records: []*ptypes.NonFleet_CommodityRecord{
						{
							CommodityCategory: model.CommodityCategory_COMMODITY_CATEGORY_PLASTIC_PRODUCTS,
						},
						{
							CommodityCategory: model.CommodityCategory_COMMODITY_CATEGORY_AIRCRAFT_ENGINES,
						},
					},
				},
				UnderwriterInput: &ptypes.NonFleet_UnderwriterInput{
					ScheduleModifications: []*ptypes.ScheduleModification{
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_UninsuredMotorist,
									ptypes.SubCoverageType_SubCoverageType_MedicalPayments,
								},
							},
						},
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Comprehensive,
									ptypes.SubCoverageType_SubCoverageType_Collision,
								},
							},
						},
					},
				},
				Vehicles: []*ptypes.NonFleet_Vehicle{
					{
						Vin: "def456",
					},
					{
						Vin: "abc123",
					},
				},
			},
		},
		SpecifiedRegularAdditionalInsureds: []*ptypes.SpecifiedRegularAdditionalInsured{
			{
				Id: "SRAI-2",
			},
			{
				Id: "SRAI-1",
			},
		},
		SpecifiedPrimaryAndNonContributoryAdditionalInsureds: []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
			{
				Id: "SPNCAI-2",
			},
			{
				Id: "SPNCAI-1",
			},
		},
		SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
			{
				Id: "TP-2",
			},
			{
				Id: "TP-1",
			},
		},
		SubCoverages: []ptypes.SubCoverageType{
			ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
			ptypes.SubCoverageType_SubCoverageType_Comprehensive,
			ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
			ptypes.SubCoverageType_SubCoverageType_Collision,
		},
		LimitSpecs: []*ptypes.LimitSpec{
			{
				SubCoverageGroup: &ptypes.SubCoverageGroup{
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_Comprehensive,
						ptypes.SubCoverageType_SubCoverageType_Collision,
					},
				},
				Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				Amount:  45.6,
			},
			{
				SubCoverageGroup: &ptypes.SubCoverageGroup{
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
					},
				},
				Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				Amount:  456.78,
			},
			{
				SubCoverageGroup: &ptypes.SubCoverageGroup{
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_Comprehensive,
						ptypes.SubCoverageType_SubCoverageType_Collision,
					},
				},
				Cadence: ptypes.LimitCadenceType_LimitCadenceType_Aggregate,
				Amount:  98.7,
			},
		},
		DeductibleSpecs: []*ptypes.DeductibleSpec{
			{
				SubCoverageGroup: &ptypes.SubCoverageGroup{
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_Comprehensive,
						ptypes.SubCoverageType_SubCoverageType_Collision,
					},
				},
				Amount: 101.23,
			},
			{
				SubCoverageGroup: &ptypes.SubCoverageGroup{
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
					},
				},
				Amount: 78.9,
			},
		},
	}

	request := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: policyNumber,
				ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
					policyChunk1,
					policyChunk2,
				},
			},
		},
		BundleSpec: &ptypes.BundleSpec{
			ChunkSpecs: []*ptypes.BundleSpec_ChunkSpec{
				bundleChunk1,
				bundleChunk2,
			},
		},
	}

	input := &plugins_common.PluginChainInput{
		PolicyNumber: policyNumber,
		Request:      request,
	}

	// We validate that this function is only called once (for chunk 1).
	mockPricingFnCalled := false
	mockChunkOutput := &plugins_common.ChunkOutput{
		Charges: []*ptypes.Charge{
			s.createNonFeeNonSurchargeCharge(123.45),
		},
	}
	nextPluginApplyFn := func(_ context.Context, inp *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
		s.Require().False(mockPricingFnCalled)
		mockPricingFnCalled = true
		req, err := inp.GetPriceRequest()
		s.Require().NoError(err)
		s.Require().NotNil(req)
		s.Require().Len(req.PolicySpecs, 1)
		s.Require().Len(req.PolicySpecs[0].ChunkSpecs, 1)
		s.Require().Equal(policyChunk1, req.PolicySpecs[0].ChunkSpecs[0])
		return plugins_common.PluginChainOutput{chunkID1: mockChunkOutput}, nil
	}
	err := s.rule.SetNextPluginApplyFn(nextPluginApplyFn)
	s.Require().NoError(err)

	output, err := s.rule.Apply(s.ctx, input)
	s.Require().NoError(err)
	s.Require().NotNil(output)
	s.Require().Len(output, 2)
	s.Require().Equal(mockChunkOutput, output[chunkID1])
	s.Require().Equal(mockChunkOutput, output[chunkID2])
}

func (s *ruleTestSuite) TestApply_WithTwoChunks_WithNonDriversChanges() {
	// There are many types of non-driver related changes.
	// To simplify things, we only test with changes to vehicles.
	policyNumber := "NFMCK0142"

	chunkID1 := "ID123"
	chunkID2 := "ID456"

	startDate1 := time_utils.MustParseDate("02/01/2006", "01/01/2025")
	endDate1 := startDate1.AddDate(0, 6, 0)
	startDate2 := endDate1
	endDate2 := startDate2.AddDate(0, 6, 0)
	dates1 := &common_proto.Interval{
		Start: timestamppb.New(startDate1.ToTime()),
		End:   timestamppb.New(endDate1.ToTime()),
	}
	dates2 := &common_proto.Interval{
		Start: timestamppb.New(startDate2.ToTime()),
		End:   timestamppb.New(endDate2.ToTime()),
	}

	emptyBundleChunkData := &ptypes.BundleSpec_ChunkSpec_NonFleetBundleChunkSpecData{
		NonFleetBundleChunkSpecData: &ptypes.NonFleet_BundleChunkSpecData{},
	}

	bundleChunk1 := &ptypes.BundleSpec_ChunkSpec{
		ChunkId: chunkID1,
		Dates:   dates1,
		Data:    emptyBundleChunkData,
	}
	bundleChunk2 := &ptypes.BundleSpec_ChunkSpec{
		ChunkId: chunkID2,
		Dates:   dates2,
		Data:    emptyBundleChunkData,
	}

	policyChunk1 := &ptypes.PolicySpec_ChunkSpec{
		ChunkId: chunkID1,
		Data: &ptypes.PolicySpec_ChunkSpec_NonFleetPolicyChunkSpecData{
			NonFleetPolicyChunkSpecData: &ptypes.NonFleet_PolicyChunkSpecData{
				Vehicles: []*ptypes.NonFleet_Vehicle{
					{
						Vin: "abc123",
					},
				},
			},
		},
	}
	policyChunk2 := &ptypes.PolicySpec_ChunkSpec{
		ChunkId: chunkID2,
		Data: &ptypes.PolicySpec_ChunkSpec_NonFleetPolicyChunkSpecData{
			NonFleetPolicyChunkSpecData: &ptypes.NonFleet_PolicyChunkSpecData{
				Vehicles: []*ptypes.NonFleet_Vehicle{
					{
						Vin: "abc123",
					},
					{
						Vin: "def456",
					},
				},
			},
		},
	}

	request := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: policyNumber,
				ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
					policyChunk1,
					policyChunk2,
				},
			},
		},
		BundleSpec: &ptypes.BundleSpec{
			ChunkSpecs: []*ptypes.BundleSpec_ChunkSpec{
				bundleChunk1,
				bundleChunk2,
			},
		},
	}

	input := &plugins_common.PluginChainInput{
		PolicyNumber: policyNumber,
		Request:      request,
	}

	type testCase struct {
		name                 string
		mockSubTotalPremium1 float64
		mockSubTotalPremium2 float64
	}

	testCases := []testCase{
		{
			name:                 "WithImpactBelowThreshold",
			mockSubTotalPremium1: 100.00,
			mockSubTotalPremium2: 119.00,
		},
		{
			name:                 "WithImpactAboveThreshold",
			mockSubTotalPremium1: 100.00,
			mockSubTotalPremium2: 121.00,
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			chunkOutput1 := &plugins_common.ChunkOutput{
				Charges: []*ptypes.Charge{
					s.createNonFeeNonSurchargeCharge(tc.mockSubTotalPremium1),
				},
			}
			chunkOutput2 := &plugins_common.ChunkOutput{
				Charges: []*ptypes.Charge{
					s.createNonFeeNonSurchargeCharge(tc.mockSubTotalPremium2),
				},
			}

			// We validate that this function is called once per chunk,
			// and simulate a price impact below or above the threshold.
			nextPluginApplyFn := func(_ context.Context, inp *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
				req, err := inp.GetPriceRequest()
				s.Require().NoError(err)
				s.Require().NotNil(req)
				s.Require().Len(req.PolicySpecs, 1)
				s.Require().Len(req.PolicySpecs[0].ChunkSpecs, 1)

				chunk := req.PolicySpecs[0].ChunkSpecs[0]
				chunkID := chunk.ChunkId

				switch chunkID {
				case chunkID1:
					return plugins_common.PluginChainOutput{chunkID: chunkOutput1}, nil
				case chunkID2:
					return plugins_common.PluginChainOutput{chunkID: chunkOutput2}, nil
				default:
					return nil, errors.Newf("unexpected chunk with ID %s", chunk.ChunkId)
				}
			}
			err := s.rule.SetNextPluginApplyFn(nextPluginApplyFn)
			s.Require().NoError(err)

			output, err := s.rule.Apply(s.ctx, input)
			s.Require().NoError(err)
			s.Require().NotNil(output)
			s.Require().Len(output, 2)
			s.Require().Equal(chunkOutput1, output[chunkID1])
			s.Require().Equal(chunkOutput2, output[chunkID2])
		})
	}
}

func (s *ruleTestSuite) TestApply_WithTwoChunks_WithDriversChanges() {
	type testCase struct {
		name                               string
		mockSubTotalPremium1a              float64
		mockSubTotalPremium1b              float64
		mockSubTotalPremium2a              float64
		mockSubTotalPremium2b              float64
		startDate1                         time_utils.Date
		startDate2                         time_utils.Date
		endDate1                           time_utils.Date
		endDate2                           time_utils.Date
		expectedSubTotalPremium1a          float64
		expectedSubTotalPremium1b          float64
		expectedSubTotalPremium2a          float64
		expectedSubTotalPremium2b          float64
		shouldExpectMetadata1              bool
		shouldExpectMetadata2              bool
		expectedBasePremiumInMetadata1     float64
		expectedBasePremiumInMetadata2     float64
		expectedProposedPremiumInMetadata1 float64
		expectedProposedPremiumInMetadata2 float64
	}

	testCases := []testCase{
		{
			name:                               "WithImpactBelowThresholdAndContiguousChunks",
			mockSubTotalPremium1a:              60.00,
			mockSubTotalPremium1b:              40.00,
			mockSubTotalPremium2a:              70.00,
			mockSubTotalPremium2b:              49.00,
			startDate1:                         time_utils.MustParseDate("02/01/2006", "01/01/2025"),
			endDate1:                           time_utils.MustParseDate("02/01/2006", "01/02/2025"),
			startDate2:                         time_utils.MustParseDate("02/01/2006", "01/02/2025"),
			endDate2:                           time_utils.MustParseDate("02/01/2006", "01/03/2025"),
			expectedSubTotalPremium1a:          60.00,
			expectedSubTotalPremium1b:          40.00,
			expectedSubTotalPremium2a:          60.00,
			expectedSubTotalPremium2b:          40.00,
			shouldExpectMetadata2:              true,
			expectedBasePremiumInMetadata2:     100.0,
			expectedProposedPremiumInMetadata2: 119.0,
		},
		{
			name:                               "WithImpactAboveThresholdAndContiguousChunks",
			mockSubTotalPremium1a:              60.00,
			mockSubTotalPremium1b:              40.00,
			mockSubTotalPremium2a:              70.00,
			mockSubTotalPremium2b:              51.00,
			startDate1:                         time_utils.MustParseDate("02/01/2006", "01/01/2025"),
			endDate1:                           time_utils.MustParseDate("02/01/2006", "01/02/2025"),
			startDate2:                         time_utils.MustParseDate("02/01/2006", "01/02/2025"),
			endDate2:                           time_utils.MustParseDate("02/01/2006", "01/03/2025"),
			expectedSubTotalPremium1a:          60.00,
			expectedSubTotalPremium1b:          40.00,
			expectedSubTotalPremium2a:          70.00,
			expectedSubTotalPremium2b:          51.00,
			shouldExpectMetadata2:              true,
			expectedBasePremiumInMetadata2:     100.0,
			expectedProposedPremiumInMetadata2: 121.0,
		},
		{
			name:                      "WithImpactBelowThresholdAndGapBetweenChunks",
			mockSubTotalPremium1a:     60.00,
			mockSubTotalPremium1b:     40.00,
			mockSubTotalPremium2a:     60.00,
			mockSubTotalPremium2b:     49.00,
			startDate1:                time_utils.MustParseDate("02/01/2006", "01/01/2025"),
			endDate1:                  time_utils.MustParseDate("02/01/2006", "01/02/2025"),
			startDate2:                time_utils.MustParseDate("02/01/2006", "02/02/2025"),
			endDate2:                  time_utils.MustParseDate("02/01/2006", "01/03/2025"),
			expectedSubTotalPremium1a: 60.00,
			expectedSubTotalPremium1b: 40.00,
			expectedSubTotalPremium2a: 60.00,
			expectedSubTotalPremium2b: 49.00,
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			policyNumber := "NFMCK0142"

			chunkID1 := "chunkID-1"
			chunkID2 := "chunkID-2"

			policyChunk1 := &ptypes.PolicySpec_ChunkSpec{
				ChunkId: chunkID1,
				Data: &ptypes.PolicySpec_ChunkSpec_NonFleetPolicyChunkSpecData{
					NonFleetPolicyChunkSpecData: &ptypes.NonFleet_PolicyChunkSpecData{
						Drivers: []*ptypes.NonFleet_Driver{
							{
								Id: "123",
								ViolationsInfo: &ptypes.NonFleet_DriverViolationsInfo{
									DriverId: "abc",
								},
							},
						},
					},
				},
			}
			policyChunk2 := &ptypes.PolicySpec_ChunkSpec{
				ChunkId: chunkID2,
				Data: &ptypes.PolicySpec_ChunkSpec_NonFleetPolicyChunkSpecData{
					NonFleetPolicyChunkSpecData: &ptypes.NonFleet_PolicyChunkSpecData{
						Drivers: []*ptypes.NonFleet_Driver{
							{
								Id: "123",
								ViolationsInfo: &ptypes.NonFleet_DriverViolationsInfo{
									DriverId: "abc",
								},
							},
							{
								Id: "456",
								ViolationsInfo: &ptypes.NonFleet_DriverViolationsInfo{
									DriverId: "fgh",
								},
							},
						},
					},
				},
			}

			dates1 := &common_proto.Interval{
				Start: timestamppb.New(tc.startDate1.ToTime()),
				End:   timestamppb.New(tc.endDate1.ToTime()),
			}
			dates2 := &common_proto.Interval{
				Start: timestamppb.New(tc.startDate2.ToTime()),
				End:   timestamppb.New(tc.endDate2.ToTime()),
			}

			emptyBundleChunkData := &ptypes.BundleSpec_ChunkSpec_NonFleetBundleChunkSpecData{
				NonFleetBundleChunkSpecData: &ptypes.NonFleet_BundleChunkSpecData{},
			}

			bundleChunk1 := &ptypes.BundleSpec_ChunkSpec{
				ChunkId: chunkID1,
				Dates:   dates1,
				Data:    emptyBundleChunkData,
			}
			bundleChunk2 := &ptypes.BundleSpec_ChunkSpec{
				ChunkId: chunkID2,
				Dates:   dates2,
				Data:    emptyBundleChunkData,
			}

			request := &ptypes.Request{
				PolicySpecs: []*ptypes.PolicySpec{
					{
						PolicyNumber: policyNumber,
						ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
							policyChunk1,
							policyChunk2,
						},
					},
				},
				BundleSpec: &ptypes.BundleSpec{
					ChunkSpecs: []*ptypes.BundleSpec_ChunkSpec{
						bundleChunk1,
						bundleChunk2,
					},
				},
			}

			input := &plugins_common.PluginChainInput{
				PolicyNumber: policyNumber,
				Request:      request,
			}

			chunkOutput1 := &plugins_common.ChunkOutput{
				Charges: []*ptypes.Charge{
					s.createNonFeeNonSurchargeCharge(tc.mockSubTotalPremium1a),
					s.createNonFeeNonSurchargeCharge(tc.mockSubTotalPremium1b),
				},
			}
			chunkOutput2 := &plugins_common.ChunkOutput{
				Charges: []*ptypes.Charge{
					s.createNonFeeNonSurchargeCharge(tc.mockSubTotalPremium2a),
					s.createNonFeeNonSurchargeCharge(tc.mockSubTotalPremium2b),
				},
			}

			// We validate that this function is called once per chunk,
			// and simulate a price impact below or above the threshold.
			nextPluginApplyFn := func(_ context.Context, inp *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
				req, err := inp.GetPriceRequest()
				s.Require().NoError(err)
				s.Require().NotNil(req)
				s.Require().Len(req.PolicySpecs, 1)
				s.Require().Len(req.PolicySpecs[0].ChunkSpecs, 1)

				chunk := req.PolicySpecs[0].ChunkSpecs[0]
				drivers := chunk.GetNonFleetPolicyChunkSpecData().GetDrivers()
				switch {
				case driversAreEqual(drivers, policyChunk1.GetNonFleetPolicyChunkSpecData().GetDrivers()):
					chunkOutputCopy, err := chunkOutput1.Copy()
					s.Require().NoError(err)
					return plugins_common.PluginChainOutput{chunk.ChunkId: chunkOutputCopy}, nil
				case driversAreEqual(drivers, policyChunk2.GetNonFleetPolicyChunkSpecData().GetDrivers()):
					chunkOutputCopy, err := chunkOutput2.Copy()
					s.Require().NoError(err)
					return plugins_common.PluginChainOutput{chunk.ChunkId: chunkOutputCopy}, nil
				default:
					return nil, errors.Newf("unexpected chunk with ID %s", chunk.ChunkId)
				}
			}
			err := s.rule.SetNextPluginApplyFn(nextPluginApplyFn)
			s.Require().NoError(err)

			output, err := s.rule.Apply(s.ctx, input)
			s.Require().NoError(err)
			s.Require().NotNil(output)
			s.Require().Len(output, 2)

			var expectedMetadata1 *ptypes.ChunkOutput_Metadata
			var expectedMetadata2 *ptypes.ChunkOutput_Metadata

			if tc.shouldExpectMetadata1 {
				expectedMetadata1 = &ptypes.ChunkOutput_Metadata{
					PluginsMetadata: &ptypes.PluginsMetadata{
						DriverEndorsementRuleV1Metadata: &ptypes.DriverEndorsementRuleV1Metadata{
							Threshold:               0.2,
							BaseSubTotalPremium:     tc.expectedBasePremiumInMetadata1,
							ProposedSubTotalPremium: tc.expectedProposedPremiumInMetadata1,
						},
					},
				}
			}

			if tc.shouldExpectMetadata2 {
				expectedMetadata2 = &ptypes.ChunkOutput_Metadata{
					PluginsMetadata: &ptypes.PluginsMetadata{
						DriverEndorsementRuleV1Metadata: &ptypes.DriverEndorsementRuleV1Metadata{
							Threshold:               0.2,
							BaseSubTotalPremium:     tc.expectedBasePremiumInMetadata2,
							ProposedSubTotalPremium: tc.expectedProposedPremiumInMetadata2,
						},
					},
				}
			}

			expectedChunkOutput1 := &plugins_common.ChunkOutput{
				Charges: []*ptypes.Charge{
					s.createNonFeeNonSurchargeCharge(tc.expectedSubTotalPremium1a),
					s.createNonFeeNonSurchargeCharge(tc.expectedSubTotalPremium1b),
				},
				Metadata: expectedMetadata1,
			}
			expectedChunkOutput2 := &plugins_common.ChunkOutput{
				Charges: []*ptypes.Charge{
					s.createNonFeeNonSurchargeCharge(tc.expectedSubTotalPremium2a),
					s.createNonFeeNonSurchargeCharge(tc.expectedSubTotalPremium2b),
				},
				Metadata: expectedMetadata2,
			}
			s.Require().EqualExportedValuesf(expectedChunkOutput1, output[chunkID1], "chunkID: %s", chunkID1)
			s.Require().EqualExportedValuesf(expectedChunkOutput2, output[chunkID2], "chunkID: %s", chunkID2)
		})
	}
}

func (s *ruleTestSuite) TestApply_WithMultipleChunks() {
	/*
		To simplify things for testing, we only consider that chunks have two things:
		drivers and vehicles. In reality there are other things that can change, but it's
		too complex to test every possible combination. So in this test we use vehicle
		changes to represent any non-driver related changes.

		This is the scenario to be tested:
			- Chunk 1: base chunk. Has one driver and one vehicle.
			- Chunk 2: only changes drivers (adds a new driver). We mock a price impact that
			is less than 20%, so the last-rated drivers list should not be updated. Price
			for chunk 2 should be the same as for chunk 1, because this is an endorsement
			including driver-related changes and the 20% rule is not triggered (i.e., price
			impact is less than 20%). The rule's calculation compares prices using last-rated
			drivers (from chunk 1) vs. using drivers from chunk 2. For both of these prices,
			everything else should come from chunk 2 (for the purpose of this test
			"everything else" means "vehicles").
			- Chunk 3: adds a vehicle (drivers remain the same). We mock a price impact that
			is greater than 20%, but because it's not a driver endorsement, the last-rated
			drivers list should not be updated. Price for chunk 3 should not be equal to
			chunk 2's price. Price for chunk 3 should be obtained using last-rated drivers
			(from chunk 1).
			- Chunk 4: adds a driver and a vehicle. We mock an impact on price that is more
			than 20%. Because this is an endorsement containing driver-related changes, and
			the rule was triggered, the last-rated drivers list should be updated. Because
			of the same reason, price for chunk 4 should be different from chunk 3's price.
			The calculation requires comparing prices using last-rated drivers (from chunk 1)
			vs. using drivers from chunk 4. For both of these prices, everything else should
			come from chunk 4. The price for chunk 4 should correspond to the price obtained
			by using drivers from chunk 4.
			- Chunk 5: adds a vehicle. We mock an impact on price that is less than 20%, but
			because this is not a driver-related endorsement, price should still change. Also,
			last-rated drivers list should not be updated.
			- Chunk 6: adds a violation to an existing driver. We mock an impact on price that
			is less than 20%, so the last-rated drivers list should not be updated. Price for
			chunk 6 should be the same as for chunk 5, because this is an endorsement including
			only driver-related changes and the 20% rule is not triggered. The rule's calculation
			compares prices using last-rated drivers (from chunk 4) vs. using drivers from chunk 6.
			For both of these prices, everything else should come from chunk 6.
			- Chunk 7: adds more violations to an existing driver. We mock an impact on price that
			is more than 20%. Because this is an endorsement containing driver-related changes,
			and the rule was triggered, the last-rated drivers list should be updated. Because of
			the same reason, price for chunk 7 should be different from chunk 6's price. The
			calculation requires comparing prices using last-rated drivers (from chunk 4) vs.
			using drivers from chunk 7. For both of these prices, everything else should come
			from chunk 7. The price for chunk 7 should correspond to the price obtained by using
			drivers from chunk 7.
			- Chunk 8: adds a vehicle. We mock an impact on price that is less than 20%, but
			because this is not a driver-related endorsement, price should still change. Also,
			last-rated drivers list should not be updated.
	*/
	policyNumber := "NFMCK0142"

	chunkID1 := "ID-1"
	chunkID2 := "ID-2"
	chunkID3 := "ID-3"
	chunkID4 := "ID-4"
	chunkID5 := "ID-5"
	chunkID6 := "ID-6"
	chunkID7 := "ID-7"
	chunkID8 := "ID-8"

	startDate1 := time_utils.MustParseDate("02/01/2006", "01/01/2025")
	endDate1 := startDate1.AddDate(0, 1, 0)
	startDate2 := endDate1
	endDate2 := startDate2.AddDate(0, 1, 0)
	startDate3 := endDate2
	endDate3 := startDate3.AddDate(0, 1, 0)
	startDate4 := endDate3
	endDate4 := startDate4.AddDate(0, 1, 0)
	startDate5 := endDate4
	endDate5 := startDate5.AddDate(0, 1, 0)
	startDate6 := endDate5
	endDate6 := startDate6.AddDate(0, 1, 0)
	startDate7 := endDate6
	endDate7 := startDate7.AddDate(0, 1, 0)
	startDate8 := endDate7
	endDate8 := startDate8.AddDate(0, 1, 0)

	dates1 := &common_proto.Interval{
		Start: timestamppb.New(startDate1.ToTime()),
		End:   timestamppb.New(endDate1.ToTime()),
	}
	dates2 := &common_proto.Interval{
		Start: timestamppb.New(startDate2.ToTime()),
		End:   timestamppb.New(endDate2.ToTime()),
	}
	dates3 := &common_proto.Interval{
		Start: timestamppb.New(startDate3.ToTime()),
		End:   timestamppb.New(endDate3.ToTime()),
	}
	dates4 := &common_proto.Interval{
		Start: timestamppb.New(startDate4.ToTime()),
		End:   timestamppb.New(endDate4.ToTime()),
	}
	dates5 := &common_proto.Interval{
		Start: timestamppb.New(startDate5.ToTime()),
		End:   timestamppb.New(endDate5.ToTime()),
	}
	dates6 := &common_proto.Interval{
		Start: timestamppb.New(startDate6.ToTime()),
		End:   timestamppb.New(endDate6.ToTime()),
	}
	dates7 := &common_proto.Interval{
		Start: timestamppb.New(startDate7.ToTime()),
		End:   timestamppb.New(endDate7.ToTime()),
	}
	dates8 := &common_proto.Interval{
		Start: timestamppb.New(startDate8.ToTime()),
		End:   timestamppb.New(endDate8.ToTime()),
	}

	emptyBundleChunkData := &ptypes.BundleSpec_ChunkSpec_NonFleetBundleChunkSpecData{
		NonFleetBundleChunkSpecData: &ptypes.NonFleet_BundleChunkSpecData{},
	}

	bundleChunk1 := &ptypes.BundleSpec_ChunkSpec{
		ChunkId: chunkID1,
		Dates:   dates1,
		Data:    emptyBundleChunkData,
	}
	bundleChunk2 := &ptypes.BundleSpec_ChunkSpec{
		ChunkId: chunkID2,
		Dates:   dates2,
		Data:    emptyBundleChunkData,
	}
	bundleChunk3 := &ptypes.BundleSpec_ChunkSpec{
		ChunkId: chunkID3,
		Dates:   dates3,
		Data:    emptyBundleChunkData,
	}
	bundleChunk4 := &ptypes.BundleSpec_ChunkSpec{
		ChunkId: chunkID4,
		Dates:   dates4,
		Data:    emptyBundleChunkData,
	}
	bundleChunk5 := &ptypes.BundleSpec_ChunkSpec{
		ChunkId: chunkID5,
		Dates:   dates5,
		Data:    emptyBundleChunkData,
	}
	bundleChunk6 := &ptypes.BundleSpec_ChunkSpec{
		ChunkId: chunkID6,
		Dates:   dates6,
		Data:    emptyBundleChunkData,
	}
	bundleChunk7 := &ptypes.BundleSpec_ChunkSpec{
		ChunkId: chunkID7,
		Dates:   dates7,
		Data:    emptyBundleChunkData,
	}
	bundleChunk8 := &ptypes.BundleSpec_ChunkSpec{
		ChunkId: chunkID8,
		Dates:   dates8,
		Data:    emptyBundleChunkData,
	}

	driversChunk1 := []*ptypes.NonFleet_Driver{
		{
			Id: "123",
		},
	}
	vehiclesChunk1 := []*ptypes.NonFleet_Vehicle{
		{
			Vin: "abc123",
		},
	}
	policyChunk1 := &ptypes.PolicySpec_ChunkSpec{
		ChunkId: chunkID1,
		Data: &ptypes.PolicySpec_ChunkSpec_NonFleetPolicyChunkSpecData{
			NonFleetPolicyChunkSpecData: &ptypes.NonFleet_PolicyChunkSpecData{
				Drivers:  driversChunk1,
				Vehicles: vehiclesChunk1,
			},
		},
	}

	driversChunk2 := []*ptypes.NonFleet_Driver{
		{
			Id: "456",
		},
	}
	vehiclesChunk2 := vehiclesChunk1
	policyChunk2 := &ptypes.PolicySpec_ChunkSpec{
		ChunkId: chunkID2,
		Data: &ptypes.PolicySpec_ChunkSpec_NonFleetPolicyChunkSpecData{
			NonFleetPolicyChunkSpecData: &ptypes.NonFleet_PolicyChunkSpecData{
				Drivers:  driversChunk2,
				Vehicles: vehiclesChunk2,
			},
		},
	}

	driversChunk3 := driversChunk2
	vehiclesChunk3 := []*ptypes.NonFleet_Vehicle{
		{
			Vin: "abc123",
		},
		{
			Vin: "def456",
		},
	}
	policyChunk3 := &ptypes.PolicySpec_ChunkSpec{
		ChunkId: chunkID3,
		Data: &ptypes.PolicySpec_ChunkSpec_NonFleetPolicyChunkSpecData{
			NonFleetPolicyChunkSpecData: &ptypes.NonFleet_PolicyChunkSpecData{
				Drivers:  driversChunk3,
				Vehicles: vehiclesChunk3,
			},
		},
	}

	driversChunk4 := []*ptypes.NonFleet_Driver{
		{
			Id: "456",
		},
		{
			Id: "789",
		},
	}
	vehiclesChunk4 := []*ptypes.NonFleet_Vehicle{
		{
			Vin: "abc123",
		},
		{
			Vin: "def456",
		},
		{
			Vin: "ghi789",
		},
	}
	policyChunk4 := &ptypes.PolicySpec_ChunkSpec{
		ChunkId: chunkID4,
		Data: &ptypes.PolicySpec_ChunkSpec_NonFleetPolicyChunkSpecData{
			NonFleetPolicyChunkSpecData: &ptypes.NonFleet_PolicyChunkSpecData{
				Drivers:  driversChunk4,
				Vehicles: vehiclesChunk4,
			},
		},
	}

	driversChunk5 := driversChunk4
	vehiclesChunk5 := []*ptypes.NonFleet_Vehicle{
		{
			Vin: "abc123",
		},
		{
			Vin: "def456",
		},
		{
			Vin: "ghi789",
		},
		{
			Vin: "jkl0123",
		},
	}
	policyChunk5 := &ptypes.PolicySpec_ChunkSpec{
		ChunkId: chunkID5,
		Data: &ptypes.PolicySpec_ChunkSpec_NonFleetPolicyChunkSpecData{
			NonFleetPolicyChunkSpecData: &ptypes.NonFleet_PolicyChunkSpecData{
				Drivers:  driversChunk5,
				Vehicles: vehiclesChunk5,
			},
		},
	}

	driversChunk6 := []*ptypes.NonFleet_Driver{
		{
			Id: "456",
			ViolationsInfo: &ptypes.NonFleet_DriverViolationsInfo{
				DriverId: "456",
			},
		},
		{
			Id: "789",
		},
	}
	vehiclesChunk6 := vehiclesChunk5
	policyChunk6 := &ptypes.PolicySpec_ChunkSpec{
		ChunkId: chunkID6,
		Data: &ptypes.PolicySpec_ChunkSpec_NonFleetPolicyChunkSpecData{
			NonFleetPolicyChunkSpecData: &ptypes.NonFleet_PolicyChunkSpecData{
				Drivers:  driversChunk6,
				Vehicles: vehiclesChunk6,
			},
		},
	}

	driversChunk7 := []*ptypes.NonFleet_Driver{
		{
			Id: "456",
			ViolationsInfo: &ptypes.NonFleet_DriverViolationsInfo{
				DriverId: "456",
			},
		},
		{
			Id: "789",
			ViolationsInfo: &ptypes.NonFleet_DriverViolationsInfo{
				DriverId: "789",
			},
		},
	}
	vehiclesChunk7 := vehiclesChunk6
	policyChunk7 := &ptypes.PolicySpec_ChunkSpec{
		ChunkId: chunkID7,
		Data: &ptypes.PolicySpec_ChunkSpec_NonFleetPolicyChunkSpecData{
			NonFleetPolicyChunkSpecData: &ptypes.NonFleet_PolicyChunkSpecData{
				Drivers:  driversChunk7,
				Vehicles: vehiclesChunk7,
			},
		},
	}

	driversChunk8 := driversChunk7
	vehiclesChunk8 := []*ptypes.NonFleet_Vehicle{
		{
			Vin: "abc123",
		},
		{
			Vin: "def456",
		},
		{
			Vin: "ghi789",
		},
		{
			Vin: "jkl0123",
		},
		{
			Vin: "mno321",
		},
	}
	policyChunk8 := &ptypes.PolicySpec_ChunkSpec{
		ChunkId: chunkID8,
		Data: &ptypes.PolicySpec_ChunkSpec_NonFleetPolicyChunkSpecData{
			NonFleetPolicyChunkSpecData: &ptypes.NonFleet_PolicyChunkSpecData{
				Drivers:  driversChunk8,
				Vehicles: vehiclesChunk8,
			},
		},
	}

	request := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: policyNumber,
				ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
					policyChunk1,
					policyChunk2,
					policyChunk3,
					policyChunk4,
					policyChunk5,
					policyChunk6,
					policyChunk7,
					policyChunk8,
				},
			},
		},
		BundleSpec: &ptypes.BundleSpec{
			ChunkSpecs: []*ptypes.BundleSpec_ChunkSpec{
				bundleChunk1,
				bundleChunk2,
				bundleChunk3,
				bundleChunk4,
				bundleChunk5,
				bundleChunk6,
				bundleChunk7,
				bundleChunk8,
			},
		},
	}

	input := &plugins_common.PluginChainInput{
		PolicyNumber: policyNumber,
		Request:      request,
	}

	// Price for chunk 1: drivers and vehicles from chunk 1.
	mockSubTotalPremiumChunk1 := 100.0

	// Price for chunk 2a: last-rated drivers (from chunk 1) and vehicles from chunk 2 (same as chunk 1).
	mockSubTotalPremiumChunk2a := 100.0 // same as chunk 1 (uses same data).

	// Price for chunk 2b: drivers from chunk 2 and vehicles from chunk 2 (same as chunk 1).
	mockSubTotalPremiumChunk2b := 110.0 // increase due to changed driver -> mockSubTotalPremiumChunk2b/mockSubTotalPremiumChunk2a = 10% increase -> not enough to trigger price change

	// Price for chunk 3: last-rated drivers (from chunk 1) and vehicles from chunk 3.
	mockSubTotalPremiumChunk3 := 150.0 // increase only due to added vehicle (doesn't matter if it's < or > than 20% as this is not a driver-related endorsement)

	// Price for chunk 4a: last-rated driver (from chunk 1) and vehicles from chunk 4.
	mockSubTotalPremiumChunk4a := 180.0 // increase only due to added vehicle

	// Price for chunk 4b: drivers and vehicles from chunk 4.
	mockSubTotalPremiumChunk4b := 220.0 // increase due to adding a driver and a vehicle -> mockSubTotalPremiumChunk4b/mockSubTotalPremiumChunk4a = ~22% increase -> enough to trigger price change

	// Price for chunk 5: last-rated drivers (from chunk 4) and vehicles from chunk 5.
	mockSubTotalPremiumChunk5 := 250.0 // increase only due to added vehicle (doesn't matter if it's < or > than 20% as this is not a driver-related endorsement)

	// Price for chunk 6a: last-rated drivers (from chunk 4) and vehicle from chunk 6 (same as in chunk 5).
	mockSubTotalPremiumChunk6a := 250.0 // same as chunk 5 (uses same data).

	// Price for chunk 6b: drivers from chunk 6 and vehicle from chunk 6 (same as chunk 5).
	mockSubTotalPremiumChunk6b := 295.0 // increase due to added violation -> mockSubTotalPremiumChunk6b/mockSubTotalPremiumChunk6a = 18% increase -> not enough to trigger price change

	// Price for chunk 7a: last-rated drivers (from chunk 4) and vehicle from chunk 7 (same as in chunk 5/6).
	mockSubTotalPremiumChunk7a := 250.0 // same as chunk 5/6a (uses same data).

	// Price for chunk 7b: drivers from chunk 7 and vehicle from chunk 7 (same as chunk 5/6).
	mockSubTotalPremiumChunk7b := 305.0 // increase due to added violation -> mockSubTotalPremiumChunk7a/mockSubTotalPremiumChunk7a = 22% increase -> enough to trigger price change

	// Price for chunk 8: last-rated drivers (from chunk 7) and vehicles from chunk 8.
	mockSubTotalPremiumChunk8 := 320.0 // increase due to added vehicle only (doesn't matter if it's < or > than 20% as this is not a driver-related endorsement)

	chunkOutput1 := &plugins_common.ChunkOutput{
		Charges: []*ptypes.Charge{
			s.createNonFeeNonSurchargeCharge(mockSubTotalPremiumChunk1),
		},
	}
	chunkOutput2a := &plugins_common.ChunkOutput{
		Charges: []*ptypes.Charge{
			s.createNonFeeNonSurchargeCharge(mockSubTotalPremiumChunk2a),
		},
	}
	chunkOutput2b := &plugins_common.ChunkOutput{
		Charges: []*ptypes.Charge{
			s.createNonFeeNonSurchargeCharge(mockSubTotalPremiumChunk2b),
		},
	}
	chunkOutput3 := &plugins_common.ChunkOutput{
		Charges: []*ptypes.Charge{
			s.createNonFeeNonSurchargeCharge(mockSubTotalPremiumChunk3),
		},
	}
	chunkOutput4a := &plugins_common.ChunkOutput{
		Charges: []*ptypes.Charge{
			s.createNonFeeNonSurchargeCharge(mockSubTotalPremiumChunk4a),
		},
	}
	chunkOutput4b := &plugins_common.ChunkOutput{
		Charges: []*ptypes.Charge{
			s.createNonFeeNonSurchargeCharge(mockSubTotalPremiumChunk4b),
		},
	}
	chunkOutput5 := &plugins_common.ChunkOutput{
		Charges: []*ptypes.Charge{
			s.createNonFeeNonSurchargeCharge(mockSubTotalPremiumChunk5),
		},
	}
	chunkOutput6a := &plugins_common.ChunkOutput{
		Charges: []*ptypes.Charge{
			s.createNonFeeNonSurchargeCharge(mockSubTotalPremiumChunk6a),
		},
	}
	chunkOutput6b := &plugins_common.ChunkOutput{
		Charges: []*ptypes.Charge{
			s.createNonFeeNonSurchargeCharge(mockSubTotalPremiumChunk6b),
		},
	}
	chunkOutput7a := &plugins_common.ChunkOutput{
		Charges: []*ptypes.Charge{
			s.createNonFeeNonSurchargeCharge(mockSubTotalPremiumChunk7a),
		},
	}
	chunkOutput7b := &plugins_common.ChunkOutput{
		Charges: []*ptypes.Charge{
			s.createNonFeeNonSurchargeCharge(mockSubTotalPremiumChunk7b),
		},
	}
	chunkOutput8 := &plugins_common.ChunkOutput{
		Charges: []*ptypes.Charge{
			s.createNonFeeNonSurchargeCharge(mockSubTotalPremiumChunk8),
		},
	}

	nextPluginApplyFn := func(_ context.Context, inp *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
		req, err := inp.GetPriceRequest()
		s.Require().NoError(err)
		s.Require().NotNil(req)
		s.Require().Len(req.PolicySpecs, 1)
		s.Require().Len(req.PolicySpecs[0].ChunkSpecs, 1)

		chunk := req.PolicySpecs[0].ChunkSpecs[0]

		chDrivers := chunk.GetNonFleetPolicyChunkSpecData().Drivers
		chVehicles := chunk.GetNonFleetPolicyChunkSpecData().Vehicles

		matchesChunk1 := driversAreEqual(chDrivers, driversChunk1) && vehiclesAreEqual(chVehicles, vehiclesChunk1)

		matchesChunk2a := driversAreEqual(chDrivers, driversChunk1) && vehiclesAreEqual(chVehicles, vehiclesChunk2)

		matchesChunk2b := driversAreEqual(chDrivers, driversChunk2) && vehiclesAreEqual(chVehicles, vehiclesChunk2)

		matchesChunk3 := driversAreEqual(chDrivers, driversChunk1) && vehiclesAreEqual(chVehicles, vehiclesChunk3)

		matchesChunk4a := driversAreEqual(chDrivers, driversChunk1) && vehiclesAreEqual(chVehicles, vehiclesChunk4)

		matchesChunk4b := driversAreEqual(chDrivers, driversChunk4) && vehiclesAreEqual(chVehicles, vehiclesChunk4)

		matchesChunk5 := driversAreEqual(chDrivers, driversChunk5) && vehiclesAreEqual(chVehicles, vehiclesChunk5)

		matchesChunk6a := driversAreEqual(chDrivers, driversChunk4) && vehiclesAreEqual(chVehicles, vehiclesChunk6)

		matchesChunk6b := driversAreEqual(chDrivers, driversChunk6) && vehiclesAreEqual(chVehicles, vehiclesChunk6)

		matchesChunk7a := driversAreEqual(chDrivers, driversChunk4) && vehiclesAreEqual(chVehicles, vehiclesChunk7)

		matchesChunk7b := driversAreEqual(chDrivers, driversChunk7) && vehiclesAreEqual(chVehicles, vehiclesChunk7)

		matchesChunk8 := driversAreEqual(chDrivers, driversChunk7) && vehiclesAreEqual(chVehicles, vehiclesChunk8)

		var chunkOutput *plugins_common.ChunkOutput
		if matchesChunk1 {
			chunkOutput = chunkOutput1
		} else if matchesChunk2a {
			chunkOutput = chunkOutput2a
		} else if matchesChunk2b {
			chunkOutput = chunkOutput2b
		} else if matchesChunk3 {
			chunkOutput = chunkOutput3
		} else if matchesChunk4a {
			chunkOutput = chunkOutput4a
		} else if matchesChunk4b {
			chunkOutput = chunkOutput4b
		} else if matchesChunk5 {
			chunkOutput = chunkOutput5
		} else if matchesChunk6a {
			chunkOutput = chunkOutput6a
		} else if matchesChunk6b {
			chunkOutput = chunkOutput6b
		} else if matchesChunk7a {
			chunkOutput = chunkOutput7a
		} else if matchesChunk7b {
			chunkOutput = chunkOutput7b
		} else if matchesChunk8 {
			chunkOutput = chunkOutput8
		} else {
			return nil, errors.Newf("unexpected chunk with ID %s", chunk.ChunkId)
		}

		chunkOutputCopy, err := chunkOutput.Copy()
		s.Require().NoError(err)

		return plugins_common.PluginChainOutput{chunk.ChunkId: chunkOutputCopy}, nil
	}
	err := s.rule.SetNextPluginApplyFn(nextPluginApplyFn)
	s.Require().NoError(err)

	output, err := s.rule.Apply(s.ctx, input)
	s.Require().NoError(err)
	s.Require().NotNil(output)
	s.Require().Len(output, 8)

	expectedChunkOutput1, err := chunkOutput1.Copy()
	s.Require().NoError(err)

	expectedChunkOutput2, err := chunkOutput2a.Copy()
	s.Require().NoError(err)

	expectedChunkOutput3, err := chunkOutput3.Copy()
	s.Require().NoError(err)

	expectedChunkOutput4, err := chunkOutput4b.Copy()
	s.Require().NoError(err)

	expectedChunkOutput5, err := chunkOutput5.Copy()
	s.Require().NoError(err)

	expectedChunkOutput6, err := chunkOutput6a.Copy()
	s.Require().NoError(err)

	expectedChunkOutput7, err := chunkOutput7b.Copy()
	s.Require().NoError(err)

	expectedChunkOutput8, err := chunkOutput8.Copy()
	s.Require().NoError(err)

	expectedChunkOutput2.Metadata = &ptypes.ChunkOutput_Metadata{
		PluginsMetadata: &ptypes.PluginsMetadata{
			DriverEndorsementRuleV1Metadata: &ptypes.DriverEndorsementRuleV1Metadata{
				Threshold:               maxAllowedPremiumVariationForDriversChanges,
				BaseSubTotalPremium:     mockSubTotalPremiumChunk2a,
				ProposedSubTotalPremium: mockSubTotalPremiumChunk2b,
			},
		},
	}

	expectedChunkOutput4.Metadata = &ptypes.ChunkOutput_Metadata{
		PluginsMetadata: &ptypes.PluginsMetadata{
			DriverEndorsementRuleV1Metadata: &ptypes.DriverEndorsementRuleV1Metadata{
				Threshold:               maxAllowedPremiumVariationForDriversChanges,
				BaseSubTotalPremium:     mockSubTotalPremiumChunk4a,
				ProposedSubTotalPremium: mockSubTotalPremiumChunk4b,
			},
		},
	}

	expectedChunkOutput6.Metadata = &ptypes.ChunkOutput_Metadata{
		PluginsMetadata: &ptypes.PluginsMetadata{
			DriverEndorsementRuleV1Metadata: &ptypes.DriverEndorsementRuleV1Metadata{
				Threshold:               maxAllowedPremiumVariationForDriversChanges,
				BaseSubTotalPremium:     mockSubTotalPremiumChunk6a,
				ProposedSubTotalPremium: mockSubTotalPremiumChunk6b,
			},
		},
	}

	expectedChunkOutput7.Metadata = &ptypes.ChunkOutput_Metadata{
		PluginsMetadata: &ptypes.PluginsMetadata{
			DriverEndorsementRuleV1Metadata: &ptypes.DriverEndorsementRuleV1Metadata{
				Threshold:               maxAllowedPremiumVariationForDriversChanges,
				BaseSubTotalPremium:     mockSubTotalPremiumChunk7a,
				ProposedSubTotalPremium: mockSubTotalPremiumChunk7b,
			},
		},
	}

	expectedChunkOutputs := map[string]*plugins_common.ChunkOutput{
		chunkID1: expectedChunkOutput1,
		chunkID2: expectedChunkOutput2,
		chunkID3: expectedChunkOutput3,
		chunkID4: expectedChunkOutput4,
		chunkID5: expectedChunkOutput5,
		chunkID6: expectedChunkOutput6,
		chunkID7: expectedChunkOutput7,
		chunkID8: expectedChunkOutput8,
	}
	for chunkID, chunkOutput := range output {
		s.Require().EqualExportedValuesf(expectedChunkOutputs[chunkID], chunkOutput, "chunkID: %s", chunkID)
	}
}

func (s *ruleTestSuite) createNonFeeNonSurchargeCharge(premium float64) *ptypes.Charge {
	return ptypes.NewChargeBuilder().
		WithBaseChargeTypeWithoutExtraInfo().
		WithAmountBasedBillingDetails(strconv.FormatFloat(premium, 'f', -1, 64), s.mockChargeDate).
		Build()
}

// We could have chosen any type of fee charge here, and it would be overkill to have all types.
func (s *ruleTestSuite) createFeeCharge(premium float64) *ptypes.Charge {
	return ptypes.NewChargeBuilder().
		WithDefaultBaseChargeTypeWithBlanketWaiverOfSubrogation_TestOnly().
		WithAmountBasedBillingDetails(strconv.FormatFloat(premium, 'f', -1, 64), s.mockChargeDate).
		Build()
}

// We could have chosen any type of surcharge here, and it would be overkill to have all types.
func (s *ruleTestSuite) createSurcharge(premium float64) *ptypes.Charge {
	return ptypes.NewChargeBuilder().
		WithNCRFSurchargeType().
		WithAmountBasedBillingDetails(strconv.FormatFloat(premium, 'f', -1, 64), s.mockChargeDate).
		Build()
}

func driversAreEqual(s1, s2 []*ptypes.NonFleet_Driver) bool {
	auxFn := func(d *ptypes.NonFleet_Driver) proto.Message {
		return d
	}
	return protoSlicesAreEqual(slice_utils.Map(s1, auxFn), slice_utils.Map(s2, auxFn))
}

func vehiclesAreEqual(s1, s2 []*ptypes.NonFleet_Vehicle) bool {
	auxFn := func(v *ptypes.NonFleet_Vehicle) proto.Message {
		return v
	}
	return protoSlicesAreEqual(slice_utils.Map(s1, auxFn), slice_utils.Map(s2, auxFn))
}

func protoSlicesAreEqual(s1, s2 []proto.Message) bool {
	if len(s1) != len(s2) {
		return false
	}

	for i := range s1 {
		if !proto.Equal(s1[i], s2[i]) {
			return false
		}
	}

	return true
}
