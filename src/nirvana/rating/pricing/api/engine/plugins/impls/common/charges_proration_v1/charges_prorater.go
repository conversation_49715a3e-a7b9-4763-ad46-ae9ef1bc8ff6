package charges_proration_v1

import (
	"encoding/json"
	"slices"
	"sort"

	"github.com/cockroachdb/errors"
	"github.com/shopspring/decimal"

	"nirvanatech.com/nirvana/common-go/crypto_utils"
	"nirvanatech.com/nirvana/common-go/map_utils"
	common_proto "nirvanatech.com/nirvana/common-go/proto"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
	"nirvanatech.com/nirvana/rating/pricing/api/utils"
)

//go:generate go run go.uber.org/mock/mockgen -destination=charges_prorater_mock.go -mock_names=ChargesProraterI=MockChargesProrater -package=charges_proration_v1 nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/common/charges_proration_v1 ChargesProraterI
type ChargesProraterI interface {
	// ProrateCharges prorates charges based on the policy and chunks dates.
	//
	// The proration logic should only be applied to charges with amount based billing
	// details, and with a chargeable item that is not a fee.
	//
	// In other words, only charges that apply to a sub-coverage need to be prorated,
	// not the ones that apply to a blanket/specified AI/WOS.
	//
	// For the purpose of proration, it doesn't matter if the charge is a base
	// charge or a surcharge.
	//
	// The keys in the maps received as input should be the chunk IDs.
	//
	// The changes are applied in place to the charges received as input.
	//
	// Some additional notes about proration:
	//		- RateML models are filed for a given period. In other words, models can only
	// 		  be used to price policies for periods that they are allowed to by regulators
	//	 	  (e.g., yearly, semi-annually, quarterly, etc.). As of 29/10/2024, we only
	// 		  have filed models to price yearly policies.
	//		- The previous restriction means that, due to regulatory reasons, models can't
	// 		  expose premiums in a "per-day" fashion. They can only expose premiums for the
	//		  whole allowed period.
	// 		- As a consequence of this restriction, we don't price for leap days. Essentially,
	//	      two "yearly" policies with the same fields, but different durations (one for 365
	//	      days, the other for 366 days) will result in the same premium.
	// 		- When pricing chunks, we need to prorate the premiums outputted by the RateML
	//	      model, because the chunk duration might be different from the policy duration
	//	      (this happens after endorsements). The proration is done according to the formula:
	//	      `RateMLPremium * (chunkDuration / policyOriginalDuration)`.
	// 		- Policy's dates need to be the original ones (i.e., the ones used when the policy
	//		  was bound). Not the current ones, as dates might be changed by endorsements.
	//
	// For more information about this, refer to: https://nirvana-tech.slack.com/archives/C075LSF92G0/p1730144272146239.
	ProrateCharges(
		policyDates *common_proto.Interval,
		chunkDates map[string]*common_proto.Interval,
		chunkCharges map[string][]*ptypes.Charge,
	) (map[string][]*ptypes.Charge, error)
}

func newChargesProrater() ChargesProraterI {
	return &chargesProraterImpl{}
}

type chargesProraterImpl struct{}

// ProrateCharges applies proration logic to all charges across all
// chunks, and returns new lists of charges for each chunk (which can
// contain fewer charges than the original one).
//
// Not all charges need to be prorated. Currently only charges with
// amount based billing details and that aren't fee charges need to be
// prorated.
//
// The algorithm relies on the fact that charges with amount based billing
// details, that come from different chunks, can be grouped together
// based on certain similarities. More concretely, all charges within
// a group share the same fields, except for:
//  1. Their chunk ID.
//  2. Their distribution details.
//  3. Their premium.
//  4. Their date.
//
// The algorithm is:
//  1. Calculate the proration ratios for each chunk, based on the
//     policy's duration and the chunk duration (essentially apply
//     a rule-of-3).
//  2. Initialize the "accumulated cents" of each group to zero.
//  3. Initialize an empty list of charges, that will be returned
//     at the end.
//  4. For each chunk, and each charge within it, do:
//     a) If the charge doesn't need to be prorated: just add it to
//     the results list.
//     b) If the charge needs to be prorated:
//     i) Multiply the charge's premium by the proration ratio.
//     ii) Add the charge's prorated premium with the accumulated
//     cents of the charge's group.
//     iii) Round the premium from the previous step to the nearest
//     dollar.
//     iv) Update the accumulated cents of the charge's group, to the
//     difference between the premium pre-rounding and post-rounding.
//     Note that this value can be positive, negative or zero.
//     v) Filter out the charges for which the rounded premium is zero.
//
// Note #1: at the end of the algorithm (i.e. after processing the
// last chunk) we might end up with remaining cents for some charge
// groups. These remaining cents can be positive or negative. We are
// okay with this.
//
// Note #2: we assume that a charge's premium is always non-negative.
// For charges with negative premiums, we would need to apply the
// ceil and floor functions in the opposite manner (i.e. floor for
// the chunk with the highest ratio, and ceil for all the rest). But
// this wasn't implemented, as it's not a requirement at of 13/01/2025.
func (p *chargesProraterImpl) ProrateCharges(
	policyDates *common_proto.Interval,
	chunksDates map[string]*common_proto.Interval,
	chunksCharges map[string][]*ptypes.Charge,
) (map[string][]*ptypes.Charge, error) {
	policyDuration := utils.CalculateDaysDuration(policyDates)
	if policyDuration.IsZero() {
		return nil, errors.New("policy duration is zero")
	}

	// We pre-compute the chunks durations, as these will
	// be used multiple times (at least one per charge within
	// each chunk).
	chunksDurations := make(map[string]decimal.Decimal)
	for chunkID, chunkDates := range chunksDates {
		chunksDurations[chunkID] = utils.CalculateDaysDuration(chunkDates)
	}

	// We sort chunk IDs in the chunksCharges map
	// according to their corresponding dates.
	chunksIDs := map_utils.Keys(chunksCharges)
	sort.SliceStable(chunksIDs, func(i, j int) bool {
		dates1 := chunksDates[chunksIDs[i]]
		dates2 := chunksDates[chunksIDs[j]]
		start1 := dates1.Start.AsTime()
		start2 := dates2.Start.AsTime()
		end1 := dates1.End.AsTime()
		end2 := dates2.End.AsTime()
		if start1.Before(start2) {
			return true
		} else if start1.After(start2) {
			return false
		}
		return end1.Before(end2)
	})

	newChunksCharges := make(map[string][]*ptypes.Charge)

	// The keys of this map are the group "IDs".
	// Note that this is equivalent to initializing
	// all keys with a value of zero.
	remainders := make(map[string]decimal.Decimal)

	for _, chunkID := range chunksIDs {
		chunkDuration, ok := chunksDurations[chunkID]
		if !ok {
			return nil, errors.Newf("duration not found for chunk %s", chunkID)
		}

		newChunksCharges[chunkID] = make([]*ptypes.Charge, 0)
		for _, charge := range chunksCharges[chunkID] {
			chargeCopy := charge.Copy()

			if shouldProrateCharge(chargeCopy) {

				// We don't prorate charges if the chunk has zero duration.
				// The accumulated cents just carry to the next chunk with
				// a non-zero duration.
				if chunkDuration.IsZero() {
					continue
				}

				amountBasedBillingDetails := chargeCopy.GetAmountBasedBillingDetails()
				premium, err := decimal.NewFromString(amountBasedBillingDetails.Amount)
				if err != nil {
					return nil, errors.Wrapf(err, "failed to parse amount for charge %+v", chargeCopy)
				}

				if premium.IsNegative() {
					return nil, errors.Newf("negative premium for charge %+v", chargeCopy)
				}

				// Note that the multiplication needs to be executed
				// before the division, in order to minimize rounding errors.
				proratedPremium := (premium.Mul(chunkDuration)).Div(policyDuration)

				groupID, err := p.getChargeGroupID(chargeCopy)
				if err != nil {
					return nil, errors.Wrapf(err, "failed to get group ID for charge %+v", chargeCopy)
				}

				combinedPremium := proratedPremium.Add(remainders[groupID])

				// We round surcharges to nearest cent and base charges
				// to nearest dollar.
				var roundedPremium decimal.Decimal
				if chargeCopy.IsSurcharge() {
					roundedPremium = combinedPremium.Round(2)
				} else if chargeCopy.IsBaseCharge() {
					roundedPremium = combinedPremium.Round(0)
				} else {
					return nil, errors.Newf("charge %+v is neither a surcharge nor a base charge", chargeCopy)
				}

				// Note that if we rounded up, then the remainder value
				// will be negative. Which mean we should charge a bit less
				// on the next charge of the same group.
				//
				// Also, note that for:
				// 1. Surcharges: the absolute value of the remainder will
				// always be less than 0.005, because otherwise the rounding
				// would have been different.
				// 2. Base charges: it's the same, but with 0.5.
				remainders[groupID] = combinedPremium.Sub(roundedPremium)

				amountBasedBillingDetails.Amount = roundedPremium.String()

				// We filter out charges that end up with a premium of zero after
				// rounding.
				if roundedPremium.IsZero() {
					continue
				}
			}

			newChunksCharges[chunkID] = append(newChunksCharges[chunkID], chargeCopy)
		}
	}

	return newChunksCharges, nil
}

func shouldProrateCharge(charge *ptypes.Charge) bool {
	if !charge.HasAmountBasedBillingDetails() {
		return false
	}
	if charge.IsFullyEarnedCharge() {
		return false
	}
	if charge.IsSurcharge() {
		if charge.IsSurplusTaxSurchargeFromFullyEarnedPremium() {
			return false
		}
		if charge.IsStampingFeeSurchargeFromFullyEarnedPremium() {
			return false
		}
		if charge.IsMCCASurcharge() {
			return false
		}
	}
	return true
}

// getChargeGroupID returns a string that represents the ID of the charge
// group. It is created using a hashing function on top of the serializable
// form a charge.
//
// We create a copy of the charge, because we need to modify some fields
// which we don't care about when identifying the charge group.
//
// Note that this function only knows how to handle charges that need to be
// prorated.
func (p *chargesProraterImpl) getChargeGroupID(charge *ptypes.Charge) (string, error) {
	if !charge.HasAmountBasedBillingDetails() {
		return "", errors.Newf("charge %+v does not have amount based billing details", charge)
	}

	if charge.IsFullyEarnedCharge() {
		return "", errors.Newf("charge %+v is a fee charge", charge)
	}

	chargeCopy := charge.Copy()

	chargeCopy.Distributions = nil

	amountBasedBillingDetails := chargeCopy.GetAmountBasedBillingDetails()
	amountBasedBillingDetails.Date = nil
	amountBasedBillingDetails.Amount = ""

	if cscg := chargeCopy.GetChargedSubCoverageGroup(); cscg != nil {
		slices.Sort(cscg.GetGroup().GetSubCoverages())
	}

	bytes, err := json.Marshal(chargeCopy)
	if err != nil {
		return "", errors.Wrapf(err, "failed to marshal charge %+v", charge)
	}

	return crypto_utils.HashBytes(bytes), nil
}
