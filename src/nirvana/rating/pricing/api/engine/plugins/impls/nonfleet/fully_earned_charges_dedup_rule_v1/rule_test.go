package fully_earned_charges_dedup_rule_v1

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	common_proto "nirvanatech.com/nirvana/common-go/proto"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	plugins_common "nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/common"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

var (
	allPolicyNames = []ptypes.PolicyName{
		ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
		ptypes.PolicyName_PolicyName_GENERAL_LIABILITY,
		ptypes.PolicyName_PolicyName_MOTOR_TRUCK_CARGO,
	}

	motorCarrierSubCoverageGroup = &ptypes.SubCoverageGroup{
		SubCoverages: []ptypes.SubCoverageType{
			ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
			ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
		},
	}

	motorTruckCargoSubCoverageGroup = &ptypes.SubCoverageGroup{
		SubCoverages: []ptypes.SubCoverageType{
			ptypes.SubCoverageType_SubCoverageType_Cargo,
		},
	}

	generalLiabilitySubCoverageGroup = &ptypes.SubCoverageGroup{
		SubCoverages: []ptypes.SubCoverageType{
			ptypes.SubCoverageType_SubCoverageType_GeneralLiability,
		},
	}

	policySubCoverageGroups = map[ptypes.PolicyName]*ptypes.SubCoverageGroup{
		ptypes.PolicyName_PolicyName_MOTOR_CARRIER:     motorCarrierSubCoverageGroup,
		ptypes.PolicyName_PolicyName_MOTOR_TRUCK_CARGO: motorTruckCargoSubCoverageGroup,
		ptypes.PolicyName_PolicyName_GENERAL_LIABILITY: generalLiabilitySubCoverageGroup,
	}
)

type ruleTestEnv struct {
	fx.In
	Deps Deps
}

type ruleTestSuite struct {
	suite.Suite
	ctx   context.Context
	env   ruleTestEnv
	fxapp *fxtest.App
	rule  *Rule
}

func TestRule(t *testing.T) {
	suite.Run(t, new(ruleTestSuite))
}

func (s *ruleTestSuite) SetupSuite() {
	s.ctx = context.Background()

	s.fxapp = testloader.RequireStart(
		s.T(),
		&s.env,
	)
}

func (s *ruleTestSuite) SetupTest() {
	rule, err := NewRule(s.env.Deps)
	s.Require().NoError(err)
	s.rule = rule
}

func (s *ruleTestSuite) TearDownSuite() {
	s.fxapp.RequireStop()
}

func (s *ruleTestSuite) TestApply_WithNilNextPluginFn() {
	input := &plugins_common.PluginChainInput{}

	output, err := s.rule.Apply(s.ctx, input)
	s.Require().Error(err)
	s.Require().Regexp("nextPluginApplyFn can't be nil", err.Error())
	s.Require().Nil(output)
}

// TestApply_1:
//   - Two contiguous chunks.
//   - Only one policy.
//   - The policy is present in each chunk.
//   - The policy doesn't have fully earned charges (in either chunk).
func (s *ruleTestSuite) TestApply_1() {
	for _, policyName := range allPolicyNames {
		s.Run(fmt.Sprintf("With %s", policyName), func() {
			startDate1 := time.Now()
			endDate1 := startDate1.AddDate(0, 0, 1)
			startDate2 := endDate1
			endDate2 := startDate2.AddDate(0, 0, 1)

			req := &ptypes.Request{
				BundleSpec: &ptypes.BundleSpec{
					ChunkSpecs: []*ptypes.BundleSpec_ChunkSpec{
						{
							ChunkId: "chunk1",
							Dates: &common_proto.Interval{
								Start: timestamppb.New(startDate1),
								End:   timestamppb.New(endDate1),
							},
						},
						{
							ChunkId: "chunk2",
							Dates: &common_proto.Interval{
								Start: timestamppb.New(startDate2),
								End:   timestamppb.New(endDate2),
							},
						},
					},
				},
				PolicySpecs: []*ptypes.PolicySpec{
					{
						PolicyNumber: "policy1",
						PolicyName:   policyName,
						ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
							{
								ChunkId: "chunk1",
							},
							{
								ChunkId: "chunk2",
							},
						},
					},
				},
			}
			expectedRequest := proto.Clone(req).(*ptypes.Request)
			mockOutput := plugins_common.PluginChainOutput{"key": &plugins_common.ChunkOutput{}}
			mockNextPluginApplyFn := func(_ context.Context, inp *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
				actualRequest, err := inp.GetPriceRequest()
				s.Require().NoError(err)
				s.Require().EqualExportedValues(expectedRequest, actualRequest)
				return mockOutput, nil
			}
			err := s.rule.SetNextPluginApplyFn(mockNextPluginApplyFn)
			s.Require().NoError(err)

			output, err := s.rule.Apply(s.ctx, &plugins_common.PluginChainInput{Request: req})
			s.Require().NoError(err)
			s.Require().Equal(mockOutput, output)
		})
	}
}

// TestApply_2a:
//   - Two contiguous chunks.
//   - Only one policy.
//   - The policy is present in each chunk.
//   - The policy has fully earned charges only in the first chunk (no fully earned charges in the second chunk).
func (s *ruleTestSuite) TestApply_2a() {
	for _, policyName := range allPolicyNames {
		s.Run(fmt.Sprintf("With %s", policyName), func() {
			subCoverageGroup := policySubCoverageGroups[policyName]
			startDate1 := time.Now()
			endDate1 := startDate1.AddDate(0, 0, 1)
			startDate2 := endDate1
			endDate2 := startDate2.AddDate(0, 0, 1)

			req := &ptypes.Request{
				BundleSpec: &ptypes.BundleSpec{
					ChunkSpecs: []*ptypes.BundleSpec_ChunkSpec{
						{
							ChunkId: "chunk1",
							Dates: &common_proto.Interval{
								Start: timestamppb.New(startDate1),
								End:   timestamppb.New(endDate1),
							},
						},
						{
							ChunkId: "chunk2",
							Dates: &common_proto.Interval{
								Start: timestamppb.New(startDate2),
								End:   timestamppb.New(endDate2),
							},
						},
					},
				},
				PolicySpecs: []*ptypes.PolicySpec{
					{
						PolicyNumber: "policy1",
						PolicyName:   policyName,
						ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
							{
								ChunkId: "chunk1",
								RegularAdditionalInsuredBlankets: []*ptypes.BlanketRegularAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								PrimaryAndNonContributoryAdditionalInsuredBlankets: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								WaiverOfSubrogationBlankets: []*ptypes.BlanketWaiverOfSubrogation{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedRegularAdditionalInsureds: []*ptypes.SpecifiedRegularAdditionalInsured{
									{
										Id:               "RAI-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-2",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-3",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-4",
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedPrimaryAndNonContributoryAdditionalInsureds: []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
									{
										Id:               "PNCAI-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-2",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-3",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-4",
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
									{
										Id:               "WOS-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "WOS-2",
										SubCoverageGroup: subCoverageGroup,
									},
								},
							},
							{
								ChunkId: "chunk2",
							},
						},
					},
				},
			}
			expectedRequest := proto.Clone(req).(*ptypes.Request)
			mockOutput := plugins_common.PluginChainOutput{"key": &plugins_common.ChunkOutput{}}
			mockNextPluginApplyFn := func(_ context.Context, inp *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
				actualRequest, err := inp.GetPriceRequest()
				s.Require().NoError(err)
				s.Require().EqualExportedValues(expectedRequest, actualRequest)
				return mockOutput, nil
			}
			err := s.rule.SetNextPluginApplyFn(mockNextPluginApplyFn)
			s.Require().NoError(err)

			output, err := s.rule.Apply(s.ctx, &plugins_common.PluginChainInput{Request: req})
			s.Require().NoError(err)
			s.Require().Equal(mockOutput, output)
		})
	}
}

// TestApply_2b:
//   - Two contiguous chunks.
//   - Only one policy.
//   - The policy is present in each chunk.
//   - The policy has fully earned charges only in the second chunk (no fully earned charges in the first chunk).
func (s *ruleTestSuite) TestApply_2b() {
	for _, policyName := range allPolicyNames {
		s.Run(fmt.Sprintf("With %s", policyName), func() {
			subCoverageGroup := policySubCoverageGroups[policyName]
			startDate1 := time.Now()
			endDate1 := startDate1.AddDate(0, 0, 1)
			startDate2 := endDate1
			endDate2 := startDate2.AddDate(0, 0, 1)

			req := &ptypes.Request{
				BundleSpec: &ptypes.BundleSpec{
					ChunkSpecs: []*ptypes.BundleSpec_ChunkSpec{
						{
							ChunkId: "chunk1",
							Dates: &common_proto.Interval{
								Start: timestamppb.New(startDate1),
								End:   timestamppb.New(endDate1),
							},
						},
						{
							ChunkId: "chunk2",
							Dates: &common_proto.Interval{
								Start: timestamppb.New(startDate2),
								End:   timestamppb.New(endDate2),
							},
						},
					},
				},
				PolicySpecs: []*ptypes.PolicySpec{
					{
						PolicyNumber: "policy1",
						PolicyName:   policyName,
						ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
							{
								ChunkId: "chunk1",
							},
							{
								ChunkId: "chunk2",
								RegularAdditionalInsuredBlankets: []*ptypes.BlanketRegularAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								PrimaryAndNonContributoryAdditionalInsuredBlankets: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								WaiverOfSubrogationBlankets: []*ptypes.BlanketWaiverOfSubrogation{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedRegularAdditionalInsureds: []*ptypes.SpecifiedRegularAdditionalInsured{
									{
										Id:               "RAI-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-2",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-3",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-4",
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedPrimaryAndNonContributoryAdditionalInsureds: []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
									{
										Id:               "PNCAI-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-2",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-3",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-4",
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
									{
										Id:               "WOS-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "WOS-2",
										SubCoverageGroup: subCoverageGroup,
									},
								},
							},
						},
					},
				},
			}
			expectedRequest := proto.Clone(req).(*ptypes.Request)
			mockOutput := plugins_common.PluginChainOutput{"key": &plugins_common.ChunkOutput{}}
			mockNextPluginApplyFn := func(_ context.Context, inp *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
				actualRequest, err := inp.GetPriceRequest()
				s.Require().NoError(err)
				s.Require().EqualExportedValues(expectedRequest, actualRequest)
				return mockOutput, nil
			}
			err := s.rule.SetNextPluginApplyFn(mockNextPluginApplyFn)
			s.Require().NoError(err)

			output, err := s.rule.Apply(s.ctx, &plugins_common.PluginChainInput{Request: req})
			s.Require().NoError(err)
			s.Require().Equal(mockOutput, output)
		})
	}
}

// TestApply_3a:
//   - Two contiguous chunks.
//   - Only one policy.
//   - The policy is present in each chunk.
//   - The policy has fully earned charges in both chunks.
//   - Fully earned charges are the same in both chunks.
func (s *ruleTestSuite) TestApply_3a() {
	for _, policyName := range allPolicyNames {
		s.Run(fmt.Sprintf("With %s", policyName), func() {
			subCoverageGroup := policySubCoverageGroups[policyName]
			startDate1 := time.Now()
			endDate1 := startDate1.AddDate(0, 0, 1)
			startDate2 := endDate1
			endDate2 := startDate2.AddDate(0, 0, 1)

			req := &ptypes.Request{
				BundleSpec: &ptypes.BundleSpec{
					ChunkSpecs: []*ptypes.BundleSpec_ChunkSpec{
						{
							ChunkId: "chunk1",
							Dates: &common_proto.Interval{
								Start: timestamppb.New(startDate1),
								End:   timestamppb.New(endDate1),
							},
						},
						{
							ChunkId: "chunk2",
							Dates: &common_proto.Interval{
								Start: timestamppb.New(startDate2),
								End:   timestamppb.New(endDate2),
							},
						},
					},
				},
				PolicySpecs: []*ptypes.PolicySpec{
					{
						PolicyNumber: "policy1",
						PolicyName:   policyName,
						ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
							{
								ChunkId: "chunk1",
								RegularAdditionalInsuredBlankets: []*ptypes.BlanketRegularAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								PrimaryAndNonContributoryAdditionalInsuredBlankets: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								WaiverOfSubrogationBlankets: []*ptypes.BlanketWaiverOfSubrogation{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedRegularAdditionalInsureds: []*ptypes.SpecifiedRegularAdditionalInsured{
									{
										Id:               "RAI-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-2",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-3",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-4",
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedPrimaryAndNonContributoryAdditionalInsureds: []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
									{
										Id:               "RAI-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-2",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-3",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-4",
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
									{
										Id:               "WOS-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "WOS-2",
										SubCoverageGroup: subCoverageGroup,
									},
								},
							},
							{
								ChunkId: "chunk2",
								RegularAdditionalInsuredBlankets: []*ptypes.BlanketRegularAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								PrimaryAndNonContributoryAdditionalInsuredBlankets: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								WaiverOfSubrogationBlankets: []*ptypes.BlanketWaiverOfSubrogation{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedRegularAdditionalInsureds: []*ptypes.SpecifiedRegularAdditionalInsured{
									{
										Id:               "RAI-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-2",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-3",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-4",
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedPrimaryAndNonContributoryAdditionalInsureds: []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
									{
										Id:               "RAI-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-2",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-3",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-4",
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
									{
										Id:               "WOS-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "WOS-2",
										SubCoverageGroup: subCoverageGroup,
									},
								},
							},
						},
					},
				},
			}
			expectedRequest := proto.Clone(req).(*ptypes.Request)
			expectedModifiedChunk := expectedRequest.PolicySpecs[0].ChunkSpecs[1]
			expectedModifiedChunk.RegularAdditionalInsuredBlankets = nil
			expectedModifiedChunk.PrimaryAndNonContributoryAdditionalInsuredBlankets = nil
			expectedModifiedChunk.WaiverOfSubrogationBlankets = nil
			expectedModifiedChunk.SpecifiedRegularAdditionalInsureds = nil
			expectedModifiedChunk.SpecifiedPrimaryAndNonContributoryAdditionalInsureds = nil
			expectedModifiedChunk.SpecifiedThirdPartiesWithWOS = nil
			mockOutput := plugins_common.PluginChainOutput{"key": &plugins_common.ChunkOutput{}}
			mockNextPluginApplyFn := func(_ context.Context, inp *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
				actualRequest, err := inp.GetPriceRequest()
				s.Require().NoError(err)
				s.Require().EqualExportedValues(expectedRequest, actualRequest)
				return mockOutput, nil
			}
			err := s.rule.SetNextPluginApplyFn(mockNextPluginApplyFn)
			s.Require().NoError(err)

			output, err := s.rule.Apply(s.ctx, &plugins_common.PluginChainInput{Request: req})
			s.Require().NoError(err)
			s.Require().Equal(mockOutput, output)
		})
	}
}

// TestApply_3b:
//   - Two contiguous chunks.
//   - Only one policy.
//   - The policy is present in both chunks.
//   - The policy has fully earned charges in both chunks.
//   - Fully earned charges are different in each chunk.
//   - AI types change for Blanket and Specified AI charges.
func (s *ruleTestSuite) TestApply_3b() {
	for _, policyName := range allPolicyNames {
		subCoverageGroup := policySubCoverageGroups[policyName]
		for i := 0; i < 2; i++ {
			var aiType1 string

			var blanketRegularAI1 []*ptypes.BlanketRegularAdditionalInsured
			var blanketRegularAI2 []*ptypes.BlanketRegularAdditionalInsured

			var blanketPNCAI1 []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured
			var blanketPNCAI2 []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured

			var specifiedRegularAI1 []*ptypes.SpecifiedRegularAdditionalInsured
			var specifiedRegularAI2 []*ptypes.SpecifiedRegularAdditionalInsured

			var specifiedPNCAI1 []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured
			var specifiedPNCAI2 []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured

			if i == 0 {
				aiType1 = "Regular"
				blanketRegularAI1 = []*ptypes.BlanketRegularAdditionalInsured{
					{
						SubCoverageGroup: subCoverageGroup,
					},
				}
				blanketPNCAI1 = []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
					{
						SubCoverageGroup: subCoverageGroup,
					},
				}
				specifiedRegularAI1 = []*ptypes.SpecifiedRegularAdditionalInsured{
					{
						Id:               "AI-1",
						SubCoverageGroup: subCoverageGroup,
					},
					{
						Id:               "AI-2",
						SubCoverageGroup: subCoverageGroup,
					},
					{
						Id:               "AI-3",
						SubCoverageGroup: subCoverageGroup,
					},
					{
						Id:               "AI-4",
						SubCoverageGroup: subCoverageGroup,
					},
				}
				specifiedPNCAI2 = []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
					{
						Id:               "AI-1",
						SubCoverageGroup: subCoverageGroup,
					},
					{
						Id:               "AI-2",
						SubCoverageGroup: subCoverageGroup,
					},
					{
						Id:               "AI-3",
						SubCoverageGroup: subCoverageGroup,
					},
					{
						Id:               "AI-4",
						SubCoverageGroup: subCoverageGroup,
					},
				}
			} else {
				aiType1 = "PNC"
				blanketRegularAI2 = []*ptypes.BlanketRegularAdditionalInsured{
					{
						SubCoverageGroup: subCoverageGroup,
					},
				}
				blanketPNCAI2 = []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
					{
						SubCoverageGroup: subCoverageGroup,
					},
				}
				specifiedRegularAI2 = []*ptypes.SpecifiedRegularAdditionalInsured{
					{
						Id:               "AI-1",
						SubCoverageGroup: subCoverageGroup,
					},
					{
						Id:               "AI-2",
						SubCoverageGroup: subCoverageGroup,
					},
					{
						Id:               "AI-3",
						SubCoverageGroup: subCoverageGroup,
					},
					{
						Id:               "AI-4",
						SubCoverageGroup: subCoverageGroup,
					},
				}
				specifiedPNCAI1 = []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
					{
						Id:               "AI-1",
						SubCoverageGroup: subCoverageGroup,
					},
					{
						Id:               "AI-2",
						SubCoverageGroup: subCoverageGroup,
					},
					{
						Id:               "AI-3",
						SubCoverageGroup: subCoverageGroup,
					},
					{
						Id:               "AI-4",
						SubCoverageGroup: subCoverageGroup,
					},
				}
			}

			// We use this variable to mock a scenario where it
			// was present and removed, or where it was not present
			// and added.
			for _, blanketWOSFlag := range []bool{true, false} {
				s.Run(fmt.Sprintf("With %s, %s first, %t", policyName, aiType1, blanketWOSFlag), func() {
					startDate1 := time.Now()
					endDate1 := startDate1.AddDate(0, 0, 1)
					startDate2 := endDate1
					endDate2 := startDate2.AddDate(0, 0, 1)

					var blanketWOS1 []*ptypes.BlanketWaiverOfSubrogation
					var blanketWOS2 []*ptypes.BlanketWaiverOfSubrogation
					if blanketWOSFlag {
						blanketWOS1 = []*ptypes.BlanketWaiverOfSubrogation{
							{
								SubCoverageGroup: subCoverageGroup,
							},
						}
					} else {
						blanketWOS2 = []*ptypes.BlanketWaiverOfSubrogation{
							{
								SubCoverageGroup: subCoverageGroup,
							},
						}
					}

					req := &ptypes.Request{
						BundleSpec: &ptypes.BundleSpec{
							ChunkSpecs: []*ptypes.BundleSpec_ChunkSpec{
								{
									ChunkId: "chunk1",
									Dates: &common_proto.Interval{
										Start: timestamppb.New(startDate1),
										End:   timestamppb.New(endDate1),
									},
								},
								{
									ChunkId: "chunk2",
									Dates: &common_proto.Interval{
										Start: timestamppb.New(startDate2),
										End:   timestamppb.New(endDate2),
									},
								},
							},
						},
						PolicySpecs: []*ptypes.PolicySpec{
							{
								PolicyNumber: "policy1",
								PolicyName:   policyName,
								ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
									{
										ChunkId:                          "chunk1",
										RegularAdditionalInsuredBlankets: blanketRegularAI1,
										PrimaryAndNonContributoryAdditionalInsuredBlankets:   blanketPNCAI1,
										WaiverOfSubrogationBlankets:                          blanketWOS1,
										SpecifiedRegularAdditionalInsureds:                   specifiedRegularAI1,
										SpecifiedPrimaryAndNonContributoryAdditionalInsureds: specifiedPNCAI1,
										SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
											{
												Id:               "WOS-1",
												SubCoverageGroup: subCoverageGroup,
											},
											{
												Id:               "WOS-2",
												SubCoverageGroup: subCoverageGroup,
											},
										},
									},
									{
										ChunkId:                          "chunk2",
										RegularAdditionalInsuredBlankets: blanketRegularAI2,
										PrimaryAndNonContributoryAdditionalInsuredBlankets:   blanketPNCAI2,
										WaiverOfSubrogationBlankets:                          blanketWOS2,
										SpecifiedRegularAdditionalInsureds:                   specifiedRegularAI2,
										SpecifiedPrimaryAndNonContributoryAdditionalInsureds: specifiedPNCAI2,
										SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
											{
												Id:               "WOS-3",
												SubCoverageGroup: subCoverageGroup,
											},
											{
												Id:               "WOS-4",
												SubCoverageGroup: subCoverageGroup,
											},
										},
									},
								},
							},
						},
					}
					expectedRequest := proto.Clone(req).(*ptypes.Request)
					mockOutput := plugins_common.PluginChainOutput{"key": &plugins_common.ChunkOutput{}}
					mockNextPluginApplyFn := func(_ context.Context, inp *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
						actualRequest, err := inp.GetPriceRequest()
						s.Require().NoError(err)
						s.Require().EqualExportedValues(expectedRequest, actualRequest)
						return mockOutput, nil
					}
					err := s.rule.SetNextPluginApplyFn(mockNextPluginApplyFn)
					s.Require().NoError(err)

					output, err := s.rule.Apply(s.ctx, &plugins_common.PluginChainInput{Request: req})
					s.Require().NoError(err)
					s.Require().Equal(mockOutput, output)
				})
			}
		}
	}
}

// TestApply_3c:
//   - Two non-contiguous chunks (there's a gap between them).
//   - Only one policy.
//   - The policy is present in each chunk.
//   - The policy has fully earned charges in both chunks.
//   - Fully earned charges are the same in both chunks.
func (s *ruleTestSuite) TestApply_3c() {
	for _, policyName := range allPolicyNames {
		s.Run(fmt.Sprintf("With %s", policyName), func() {
			subCoverageGroup := policySubCoverageGroups[policyName]
			startDate1 := time.Now()
			endDate1 := startDate1.AddDate(0, 0, 1)
			startDate2 := endDate1.AddDate(0, 0, 1)
			endDate2 := startDate2.AddDate(0, 0, 1)

			req := &ptypes.Request{
				BundleSpec: &ptypes.BundleSpec{
					ChunkSpecs: []*ptypes.BundleSpec_ChunkSpec{
						{
							ChunkId: "chunk1",
							Dates: &common_proto.Interval{
								Start: timestamppb.New(startDate1),
								End:   timestamppb.New(endDate1),
							},
						},
						{
							ChunkId: "chunk2",
							Dates: &common_proto.Interval{
								Start: timestamppb.New(startDate2),
								End:   timestamppb.New(endDate2),
							},
						},
					},
				},
				PolicySpecs: []*ptypes.PolicySpec{
					{
						PolicyNumber: "policy1",
						PolicyName:   policyName,
						ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
							{
								ChunkId: "chunk1",
								RegularAdditionalInsuredBlankets: []*ptypes.BlanketRegularAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								PrimaryAndNonContributoryAdditionalInsuredBlankets: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								WaiverOfSubrogationBlankets: []*ptypes.BlanketWaiverOfSubrogation{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedRegularAdditionalInsureds: []*ptypes.SpecifiedRegularAdditionalInsured{
									{
										Id:               "RAI-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-2",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-3",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-4",
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedPrimaryAndNonContributoryAdditionalInsureds: []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
									{
										Id:               "PNCAI-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-2",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-3",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-4",
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
									{
										Id: "WOS-1",
									},
									{
										Id: "WOS-2",
									},
								},
							},
							{
								ChunkId: "chunk2",
								RegularAdditionalInsuredBlankets: []*ptypes.BlanketRegularAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								PrimaryAndNonContributoryAdditionalInsuredBlankets: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								WaiverOfSubrogationBlankets: []*ptypes.BlanketWaiverOfSubrogation{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedRegularAdditionalInsureds: []*ptypes.SpecifiedRegularAdditionalInsured{
									{
										Id:               "RAI-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-2",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-3",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-4",
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedPrimaryAndNonContributoryAdditionalInsureds: []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
									{
										Id:               "PNCAI-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-2",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-3",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-4",
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
									{
										Id:               "WOS-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "WOS-2",
										SubCoverageGroup: subCoverageGroup,
									},
								},
							},
						},
					},
				},
			}
			expectedRequest := proto.Clone(req).(*ptypes.Request)
			mockOutput := plugins_common.PluginChainOutput{"key": &plugins_common.ChunkOutput{}}
			mockNextPluginApplyFn := func(_ context.Context, inp *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
				actualRequest, err := inp.GetPriceRequest()
				s.Require().NoError(err)
				s.Require().EqualExportedValues(expectedRequest, actualRequest)
				return mockOutput, nil
			}
			err := s.rule.SetNextPluginApplyFn(mockNextPluginApplyFn)
			s.Require().NoError(err)

			output, err := s.rule.Apply(s.ctx, &plugins_common.PluginChainInput{Request: req})
			s.Require().NoError(err)
			s.Require().Equal(mockOutput, output)
		})
	}
}

// TestApply_4a:
//   - Three contiguous chunks.
//   - Only one policy.
//   - The policy is present in each chunk.
//   - The policy has fully earned charges in the first and third chunk.
//   - Fully earned charges are the same in both chunks.
func (s *ruleTestSuite) TestApply_4a() {
	for _, policyName := range allPolicyNames {
		s.Run(fmt.Sprintf("With %s", policyName), func() {
			subCoverageGroup := policySubCoverageGroups[policyName]
			startDate1 := time.Now()
			endDate1 := startDate1.AddDate(0, 0, 1)
			startDate2 := endDate1
			endDate2 := startDate2.AddDate(0, 0, 1)
			startDate3 := endDate2
			endDate3 := startDate3.AddDate(0, 0, 1)

			req := &ptypes.Request{
				BundleSpec: &ptypes.BundleSpec{
					ChunkSpecs: []*ptypes.BundleSpec_ChunkSpec{
						{
							ChunkId: "chunk1",
							Dates: &common_proto.Interval{
								Start: timestamppb.New(startDate1),
								End:   timestamppb.New(endDate1),
							},
						},
						{
							ChunkId: "chunk2",
							Dates: &common_proto.Interval{
								Start: timestamppb.New(startDate2),
								End:   timestamppb.New(endDate2),
							},
						},
						{
							ChunkId: "chunk3",
							Dates: &common_proto.Interval{
								Start: timestamppb.New(startDate3),
								End:   timestamppb.New(endDate3),
							},
						},
					},
				},
				PolicySpecs: []*ptypes.PolicySpec{
					{
						PolicyNumber: "policy1",
						PolicyName:   policyName,
						ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
							{
								ChunkId: "chunk1",
								RegularAdditionalInsuredBlankets: []*ptypes.BlanketRegularAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								PrimaryAndNonContributoryAdditionalInsuredBlankets: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								WaiverOfSubrogationBlankets: []*ptypes.BlanketWaiverOfSubrogation{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedRegularAdditionalInsureds: []*ptypes.SpecifiedRegularAdditionalInsured{
									{
										Id:               "RAI-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-2",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-3",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-4",
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedPrimaryAndNonContributoryAdditionalInsureds: []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
									{
										Id:               "PNCAI-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-2",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-3",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-4",
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
									{
										Id:               "WOS-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "WOS-2",
										SubCoverageGroup: subCoverageGroup,
									},
								},
							},
							{
								ChunkId: "chunk2",
							},
							{
								ChunkId: "chunk3",
								RegularAdditionalInsuredBlankets: []*ptypes.BlanketRegularAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								PrimaryAndNonContributoryAdditionalInsuredBlankets: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								WaiverOfSubrogationBlankets: []*ptypes.BlanketWaiverOfSubrogation{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedRegularAdditionalInsureds: []*ptypes.SpecifiedRegularAdditionalInsured{
									{
										Id:               "RAI-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-2",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-3",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-4",
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedPrimaryAndNonContributoryAdditionalInsureds: []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
									{
										Id:               "PNCAI-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-2",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-3",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-4",
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
									{
										Id:               "WOS-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "WOS-2",
										SubCoverageGroup: subCoverageGroup,
									},
								},
							},
						},
					},
				},
			}
			expectedRequest := proto.Clone(req).(*ptypes.Request)
			mockOutput := plugins_common.PluginChainOutput{"key": &plugins_common.ChunkOutput{}}
			mockNextPluginApplyFn := func(_ context.Context, inp *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
				actualRequest, err := inp.GetPriceRequest()
				s.Require().NoError(err)
				s.Require().EqualExportedValues(expectedRequest, actualRequest)
				return mockOutput, nil
			}
			err := s.rule.SetNextPluginApplyFn(mockNextPluginApplyFn)
			s.Require().NoError(err)

			output, err := s.rule.Apply(s.ctx, &plugins_common.PluginChainInput{Request: req})
			s.Require().NoError(err)
			s.Require().Equal(mockOutput, output)
		})
	}
}

// TestApply_4b:
//   - Three contiguous chunks.
//   - Only one policy.
//   - The policy is present in the first and third chunk.
//   - The policy has fully earned charges in both chunks where it's present.
//   - Fully earned charges are the same in both chunks.
func (s *ruleTestSuite) TestApply_4b() {
	for _, policyName := range allPolicyNames {
		s.Run(fmt.Sprintf("With %s", policyName), func() {
			subCoverageGroup := policySubCoverageGroups[policyName]
			startDate1 := time.Now()
			endDate1 := startDate1.AddDate(0, 0, 1)
			startDate2 := endDate1
			endDate2 := startDate2.AddDate(0, 0, 1)
			startDate3 := endDate2
			endDate3 := startDate3.AddDate(0, 0, 1)

			req := &ptypes.Request{
				BundleSpec: &ptypes.BundleSpec{
					ChunkSpecs: []*ptypes.BundleSpec_ChunkSpec{
						{
							ChunkId: "chunk1",
							Dates: &common_proto.Interval{
								Start: timestamppb.New(startDate1),
								End:   timestamppb.New(endDate1),
							},
						},
						{
							ChunkId: "chunk2",
							Dates: &common_proto.Interval{
								Start: timestamppb.New(startDate2),
								End:   timestamppb.New(endDate2),
							},
						},
						{
							ChunkId: "chunk3",
							Dates: &common_proto.Interval{
								Start: timestamppb.New(startDate3),
								End:   timestamppb.New(endDate3),
							},
						},
					},
				},
				PolicySpecs: []*ptypes.PolicySpec{
					{
						PolicyNumber: "policy1",
						PolicyName:   policyName,
						ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
							{
								ChunkId: "chunk1",
								RegularAdditionalInsuredBlankets: []*ptypes.BlanketRegularAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								PrimaryAndNonContributoryAdditionalInsuredBlankets: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								WaiverOfSubrogationBlankets: []*ptypes.BlanketWaiverOfSubrogation{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedRegularAdditionalInsureds: []*ptypes.SpecifiedRegularAdditionalInsured{
									{
										Id:               "RAI-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-2",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-3",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-4",
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedPrimaryAndNonContributoryAdditionalInsureds: []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
									{
										Id:               "PNCAI-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-2",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-3",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-4",
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
									{
										Id:               "WOS-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "WOS-2",
										SubCoverageGroup: subCoverageGroup,
									},
								},
							},
							{
								ChunkId: "chunk3",
								RegularAdditionalInsuredBlankets: []*ptypes.BlanketRegularAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								PrimaryAndNonContributoryAdditionalInsuredBlankets: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								WaiverOfSubrogationBlankets: []*ptypes.BlanketWaiverOfSubrogation{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedRegularAdditionalInsureds: []*ptypes.SpecifiedRegularAdditionalInsured{
									{
										Id:               "RAI-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-2",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-3",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-4",
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedPrimaryAndNonContributoryAdditionalInsureds: []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
									{
										Id:               "PNCAI-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-2",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-3",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-4",
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
									{
										Id:               "WOS-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "WOS-2",
										SubCoverageGroup: subCoverageGroup,
									},
								},
							},
						},
					},
				},
			}
			expectedRequest := proto.Clone(req).(*ptypes.Request)
			mockOutput := plugins_common.PluginChainOutput{"key": &plugins_common.ChunkOutput{}}
			mockNextPluginApplyFn := func(_ context.Context, inp *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
				actualRequest, err := inp.GetPriceRequest()
				s.Require().NoError(err)
				s.Require().EqualExportedValues(expectedRequest, actualRequest)
				return mockOutput, nil
			}
			err := s.rule.SetNextPluginApplyFn(mockNextPluginApplyFn)
			s.Require().NoError(err)

			output, err := s.rule.Apply(s.ctx, &plugins_common.PluginChainInput{Request: req})
			s.Require().NoError(err)
			s.Require().Equal(mockOutput, output)
		})
	}
}

// TestApply_5a:
//   - Two contiguous chunks.
//   - Two policies with the same name (but different policy numbers).
//   - Each policy is present in only one chunk (one in the first chunk, one in the second chunk).
//   - Both policies have the same charges in their respective chunks.
//   - Fully earned charges are the same across chunks.
func (s *ruleTestSuite) TestApply_5a() {
	for _, policyName := range allPolicyNames {
		s.Run(fmt.Sprintf("With %s", policyName), func() {
			subCoverageGroup := policySubCoverageGroups[policyName]
			startDate1 := time.Now()
			endDate1 := startDate1.AddDate(0, 0, 1)
			startDate2 := endDate1
			endDate2 := startDate2.AddDate(0, 0, 1)

			req := &ptypes.Request{
				BundleSpec: &ptypes.BundleSpec{
					ChunkSpecs: []*ptypes.BundleSpec_ChunkSpec{
						{
							ChunkId: "chunk1",
							Dates: &common_proto.Interval{
								Start: timestamppb.New(startDate1),
								End:   timestamppb.New(endDate1),
							},
						},
						{
							ChunkId: "chunk2",
							Dates: &common_proto.Interval{
								Start: timestamppb.New(startDate2),
								End:   timestamppb.New(endDate2),
							},
						},
					},
				},
				PolicySpecs: []*ptypes.PolicySpec{
					{
						PolicyNumber: "policy1",
						PolicyName:   policyName,
						ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
							{
								ChunkId: "chunk1",
								RegularAdditionalInsuredBlankets: []*ptypes.BlanketRegularAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								PrimaryAndNonContributoryAdditionalInsuredBlankets: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								WaiverOfSubrogationBlankets: []*ptypes.BlanketWaiverOfSubrogation{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedRegularAdditionalInsureds: []*ptypes.SpecifiedRegularAdditionalInsured{
									{
										Id:               "RAI-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-2",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-3",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-4",
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedPrimaryAndNonContributoryAdditionalInsureds: []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
									{
										Id:               "PNCAI-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-2",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-3",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-4",
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
									{
										Id:               "WOS-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "WOS-2",
										SubCoverageGroup: subCoverageGroup,
									},
								},
							},
						},
					},
					{
						PolicyNumber: "policy2",
						PolicyName:   policyName,
						ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
							{
								ChunkId: "chunk2",
								RegularAdditionalInsuredBlankets: []*ptypes.BlanketRegularAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								PrimaryAndNonContributoryAdditionalInsuredBlankets: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								WaiverOfSubrogationBlankets: []*ptypes.BlanketWaiverOfSubrogation{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedRegularAdditionalInsureds: []*ptypes.SpecifiedRegularAdditionalInsured{
									{
										Id:               "RAI-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-2",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-3",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-4",
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedPrimaryAndNonContributoryAdditionalInsureds: []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
									{
										Id:               "PNCAI-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-2",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-3",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-4",
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
									{
										Id:               "WOS-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "WOS-2",
										SubCoverageGroup: subCoverageGroup,
									},
								},
							},
						},
					},
				},
			}
			expectedRequest := proto.Clone(req).(*ptypes.Request)
			expectedModifiedChunk := expectedRequest.PolicySpecs[1].ChunkSpecs[0]
			expectedModifiedChunk.RegularAdditionalInsuredBlankets = nil
			expectedModifiedChunk.PrimaryAndNonContributoryAdditionalInsuredBlankets = nil
			expectedModifiedChunk.WaiverOfSubrogationBlankets = nil
			expectedModifiedChunk.SpecifiedRegularAdditionalInsureds = nil
			expectedModifiedChunk.SpecifiedPrimaryAndNonContributoryAdditionalInsureds = nil
			expectedModifiedChunk.SpecifiedThirdPartiesWithWOS = nil
			mockOutput := plugins_common.PluginChainOutput{"key": &plugins_common.ChunkOutput{}}
			mockNextPluginApplyFn := func(_ context.Context, inp *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
				actualRequest, err := inp.GetPriceRequest()
				s.Require().NoError(err)
				s.Require().EqualExportedValues(expectedRequest, actualRequest)
				return mockOutput, nil
			}
			err := s.rule.SetNextPluginApplyFn(mockNextPluginApplyFn)
			s.Require().NoError(err)

			output, err := s.rule.Apply(s.ctx, &plugins_common.PluginChainInput{Request: req})
			s.Require().NoError(err)
			s.Require().Equal(mockOutput, output)
		})
	}
}

// TestApply_5b:
//   - Two non-contiguous chunks.
//   - Two policies with the same name (but different policy numbers).
//   - Each policy is present in only one chunk (one in the first chunk, one in the second chunk).
//   - Both policies have the same charges in their respective chunks.
//   - Fully earned charges are the same across chunks.
func (s *ruleTestSuite) TestApply_5b() {
	for _, policyName := range allPolicyNames {
		s.Run(fmt.Sprintf("With %s", policyName), func() {
			subCoverageGroup := policySubCoverageGroups[policyName]
			startDate1 := time.Now()
			endDate1 := startDate1.AddDate(0, 0, 1)
			startDate2 := endDate1.AddDate(0, 0, 1)
			endDate2 := startDate2.AddDate(0, 0, 1)

			req := &ptypes.Request{
				BundleSpec: &ptypes.BundleSpec{
					ChunkSpecs: []*ptypes.BundleSpec_ChunkSpec{
						{
							ChunkId: "chunk1",
							Dates: &common_proto.Interval{
								Start: timestamppb.New(startDate1),
								End:   timestamppb.New(endDate1),
							},
						},
						{
							ChunkId: "chunk2",
							Dates: &common_proto.Interval{
								Start: timestamppb.New(startDate2),
								End:   timestamppb.New(endDate2),
							},
						},
					},
				},
				PolicySpecs: []*ptypes.PolicySpec{
					{
						PolicyNumber: "policy1",
						PolicyName:   policyName,
						ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
							{
								ChunkId: "chunk1",
								RegularAdditionalInsuredBlankets: []*ptypes.BlanketRegularAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								PrimaryAndNonContributoryAdditionalInsuredBlankets: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								WaiverOfSubrogationBlankets: []*ptypes.BlanketWaiverOfSubrogation{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedRegularAdditionalInsureds: []*ptypes.SpecifiedRegularAdditionalInsured{
									{
										Id:               "RAI-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-2",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-3",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-4",
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedPrimaryAndNonContributoryAdditionalInsureds: []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
									{
										Id:               "PNCAI-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-2",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-3",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-4",
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
									{
										Id:               "WOS-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "WOS-2",
										SubCoverageGroup: subCoverageGroup,
									},
								},
							},
						},
					},
					{
						PolicyNumber: "policy2",
						PolicyName:   policyName,
						ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
							{
								ChunkId: "chunk2",
								RegularAdditionalInsuredBlankets: []*ptypes.BlanketRegularAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								PrimaryAndNonContributoryAdditionalInsuredBlankets: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								WaiverOfSubrogationBlankets: []*ptypes.BlanketWaiverOfSubrogation{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedRegularAdditionalInsureds: []*ptypes.SpecifiedRegularAdditionalInsured{
									{
										Id:               "RAI-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-2",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-3",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-4",
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedPrimaryAndNonContributoryAdditionalInsureds: []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
									{
										Id:               "PNCAI-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-2",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-3",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-4",
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
									{
										Id:               "WOS-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "WOS-2",
										SubCoverageGroup: subCoverageGroup,
									},
								},
							},
						},
					},
				},
			}
			expectedRequest := proto.Clone(req).(*ptypes.Request)
			mockOutput := plugins_common.PluginChainOutput{"key": &plugins_common.ChunkOutput{}}
			mockNextPluginApplyFn := func(_ context.Context, inp *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
				actualRequest, err := inp.GetPriceRequest()
				s.Require().NoError(err)
				s.Require().EqualExportedValues(expectedRequest, actualRequest)
				return mockOutput, nil
			}
			err := s.rule.SetNextPluginApplyFn(mockNextPluginApplyFn)
			s.Require().NoError(err)

			output, err := s.rule.Apply(s.ctx, &plugins_common.PluginChainInput{Request: req})
			s.Require().NoError(err)
			s.Require().Equal(mockOutput, output)
		})
	}
}

// TestApply_6:
//   - Two contiguous chunks.
//   - Three policies with different names.
//   - All policies are present in each chunk.
//   - Only one policy has fully earned charges, and it has them in both chunks.
//   - Fully earned charges are the same in both chunks.
func (s *ruleTestSuite) TestApply_6() {
	for i := range allPolicyNames {
		policyName1 := allPolicyNames[i]
		policyName2 := allPolicyNames[(i+1)%3]
		policyName3 := allPolicyNames[(i+2)%3]

		s.Run(fmt.Sprintf("With %s", policyName1), func() {
			subCoverageGroup := policySubCoverageGroups[policyName1]
			startDate1 := time.Now()
			endDate1 := startDate1.AddDate(0, 0, 1)
			startDate2 := endDate1
			endDate2 := startDate2.AddDate(0, 0, 1)

			req := &ptypes.Request{
				BundleSpec: &ptypes.BundleSpec{
					ChunkSpecs: []*ptypes.BundleSpec_ChunkSpec{
						{
							ChunkId: "chunk1",
							Dates: &common_proto.Interval{
								Start: timestamppb.New(startDate1),
								End:   timestamppb.New(endDate1),
							},
						},
						{
							ChunkId: "chunk2",
							Dates: &common_proto.Interval{
								Start: timestamppb.New(startDate2),
								End:   timestamppb.New(endDate2),
							},
						},
					},
				},
				PolicySpecs: []*ptypes.PolicySpec{
					{
						PolicyNumber: "policy1",
						PolicyName:   policyName1,
						ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
							{
								ChunkId: "chunk1",
								RegularAdditionalInsuredBlankets: []*ptypes.BlanketRegularAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								PrimaryAndNonContributoryAdditionalInsuredBlankets: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								WaiverOfSubrogationBlankets: []*ptypes.BlanketWaiverOfSubrogation{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedRegularAdditionalInsureds: []*ptypes.SpecifiedRegularAdditionalInsured{
									{
										Id:               "RAI-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-2",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-3",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-4",
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedPrimaryAndNonContributoryAdditionalInsureds: []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
									{
										Id:               "PNCAI-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-2",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-3",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-4",
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
									{
										Id:               "WOS-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "WOS-2",
										SubCoverageGroup: subCoverageGroup,
									},
								},
							},
							{
								ChunkId: "chunk2",
								RegularAdditionalInsuredBlankets: []*ptypes.BlanketRegularAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								PrimaryAndNonContributoryAdditionalInsuredBlankets: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								WaiverOfSubrogationBlankets: []*ptypes.BlanketWaiverOfSubrogation{
									{
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedRegularAdditionalInsureds: []*ptypes.SpecifiedRegularAdditionalInsured{
									{
										Id:               "RAI-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-2",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-3",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "RAI-4",
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedPrimaryAndNonContributoryAdditionalInsureds: []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
									{
										Id:               "PNCAI-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-2",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-3",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "PNCAI-4",
										SubCoverageGroup: subCoverageGroup,
									},
								},
								SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
									{
										Id:               "WOS-1",
										SubCoverageGroup: subCoverageGroup,
									},
									{
										Id:               "WOS-2",
										SubCoverageGroup: subCoverageGroup,
									},
								},
							},
						},
					},
					{
						PolicyNumber: "policy2",
						PolicyName:   policyName2,
						ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
							{
								ChunkId: "chunk1",
							},
							{
								ChunkId: "chunk2",
							},
						},
					},
					{
						PolicyNumber: "policy3",
						PolicyName:   policyName3,
						ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
							{
								ChunkId: "chunk1",
							},
							{
								ChunkId: "chunk2",
							},
						},
					},
				},
			}
			expectedRequest := proto.Clone(req).(*ptypes.Request)
			expectedModifiedChunk := expectedRequest.PolicySpecs[0].ChunkSpecs[1]
			expectedModifiedChunk.RegularAdditionalInsuredBlankets = nil
			expectedModifiedChunk.PrimaryAndNonContributoryAdditionalInsuredBlankets = nil
			expectedModifiedChunk.WaiverOfSubrogationBlankets = nil
			expectedModifiedChunk.SpecifiedRegularAdditionalInsureds = nil
			expectedModifiedChunk.SpecifiedPrimaryAndNonContributoryAdditionalInsureds = nil
			expectedModifiedChunk.SpecifiedThirdPartiesWithWOS = nil
			mockOutput := plugins_common.PluginChainOutput{"key": &plugins_common.ChunkOutput{}}
			mockNextPluginApplyFn := func(_ context.Context, inp *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
				actualRequest, err := inp.GetPriceRequest()
				s.Require().NoError(err)
				s.Require().EqualExportedValues(expectedRequest, actualRequest)
				return mockOutput, nil
			}
			err := s.rule.SetNextPluginApplyFn(mockNextPluginApplyFn)
			s.Require().NoError(err)

			output, err := s.rule.Apply(s.ctx, &plugins_common.PluginChainInput{Request: req})
			s.Require().NoError(err)
			s.Require().Equal(mockOutput, output)
		})
	}
}

// TestApply_7:
//   - Two contiguous chunks.
//   - Three policies with different names.
//   - All policies are present in each chunk.
//   - One policy has fully earned charges in all chunks.
//   - The other policies only have charges in the second chunk.
//   - Fully earned charges are the same across policies and across chunks.
func (s *ruleTestSuite) TestApply_7() {
	for i := range allPolicyNames {
		policyName1 := allPolicyNames[i]
		policyName2 := allPolicyNames[(i+1)%3]
		policyName3 := allPolicyNames[(i+2)%3]

		s.Run(fmt.Sprintf("With %s", policyName1), func() {
			subCoverageGroup1 := policySubCoverageGroups[policyName1]
			subCoverageGroup2 := policySubCoverageGroups[policyName2]
			subCoverageGroup3 := policySubCoverageGroups[policyName3]
			startDate1 := time.Now()
			endDate1 := startDate1.AddDate(0, 0, 1)
			startDate2 := endDate1
			endDate2 := startDate2.AddDate(0, 0, 1)

			req := &ptypes.Request{
				BundleSpec: &ptypes.BundleSpec{
					ChunkSpecs: []*ptypes.BundleSpec_ChunkSpec{
						{
							ChunkId: "chunk1",
							Dates: &common_proto.Interval{
								Start: timestamppb.New(startDate1),
								End:   timestamppb.New(endDate1),
							},
						},
						{
							ChunkId: "chunk2",
							Dates: &common_proto.Interval{
								Start: timestamppb.New(startDate2),
								End:   timestamppb.New(endDate2),
							},
						},
					},
				},
				PolicySpecs: []*ptypes.PolicySpec{
					{
						PolicyNumber: "policy1",
						PolicyName:   policyName1,
						ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
							{
								ChunkId: "chunk1",
								RegularAdditionalInsuredBlankets: []*ptypes.BlanketRegularAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup1,
									},
								},
								PrimaryAndNonContributoryAdditionalInsuredBlankets: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup1,
									},
								},
								WaiverOfSubrogationBlankets: []*ptypes.BlanketWaiverOfSubrogation{
									{
										SubCoverageGroup: subCoverageGroup1,
									},
								},
								SpecifiedRegularAdditionalInsureds: []*ptypes.SpecifiedRegularAdditionalInsured{
									{
										Id:               "RAI-1",
										SubCoverageGroup: subCoverageGroup1,
									},
									{
										Id:               "RAI-2",
										SubCoverageGroup: subCoverageGroup1,
									},
									{
										Id:               "RAI-3",
										SubCoverageGroup: subCoverageGroup1,
									},
									{
										Id:               "RAI-4",
										SubCoverageGroup: subCoverageGroup1,
									},
								},
								SpecifiedPrimaryAndNonContributoryAdditionalInsureds: []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
									{
										Id:               "PNCAI-1",
										SubCoverageGroup: subCoverageGroup1,
									},
									{
										Id:               "PNCAI-2",
										SubCoverageGroup: subCoverageGroup1,
									},
									{
										Id:               "PNCAI-3",
										SubCoverageGroup: subCoverageGroup1,
									},
									{
										Id:               "PNCAI-4",
										SubCoverageGroup: subCoverageGroup1,
									},
								},
								SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
									{
										Id:               "WOS-1",
										SubCoverageGroup: subCoverageGroup1,
									},
									{
										Id:               "WOS-2",
										SubCoverageGroup: subCoverageGroup1,
									},
								},
							},
							{
								ChunkId: "chunk2",
								RegularAdditionalInsuredBlankets: []*ptypes.BlanketRegularAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup1,
									},
								},
								PrimaryAndNonContributoryAdditionalInsuredBlankets: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup1,
									},
								},
								WaiverOfSubrogationBlankets: []*ptypes.BlanketWaiverOfSubrogation{
									{
										SubCoverageGroup: subCoverageGroup1,
									},
								},
								SpecifiedRegularAdditionalInsureds: []*ptypes.SpecifiedRegularAdditionalInsured{
									{
										Id:               "RAI-1",
										SubCoverageGroup: subCoverageGroup1,
									},
									{
										Id:               "RAI-2",
										SubCoverageGroup: subCoverageGroup1,
									},
									{
										Id:               "RAI-3",
										SubCoverageGroup: subCoverageGroup1,
									},
									{
										Id:               "RAI-4",
										SubCoverageGroup: subCoverageGroup1,
									},
								},
								SpecifiedPrimaryAndNonContributoryAdditionalInsureds: []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
									{
										Id:               "PNCAI-1",
										SubCoverageGroup: subCoverageGroup1,
									},
									{
										Id:               "PNCAI-2",
										SubCoverageGroup: subCoverageGroup1,
									},
									{
										Id:               "PNCAI-3",
										SubCoverageGroup: subCoverageGroup1,
									},
									{
										Id:               "PNCAI-4",
										SubCoverageGroup: subCoverageGroup1,
									},
								},
								SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
									{
										Id:               "WOS-1",
										SubCoverageGroup: subCoverageGroup1,
									},
									{
										Id:               "WOS-2",
										SubCoverageGroup: subCoverageGroup1,
									},
								},
							},
						},
					},
					{
						PolicyNumber: "policy2",
						PolicyName:   policyName2,
						ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
							{
								ChunkId: "chunk1",
							},
							{
								ChunkId: "chunk2",
								RegularAdditionalInsuredBlankets: []*ptypes.BlanketRegularAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup2,
									},
								},
								PrimaryAndNonContributoryAdditionalInsuredBlankets: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup2,
									},
								},
								WaiverOfSubrogationBlankets: []*ptypes.BlanketWaiverOfSubrogation{
									{
										SubCoverageGroup: subCoverageGroup2,
									},
								},
								SpecifiedRegularAdditionalInsureds: []*ptypes.SpecifiedRegularAdditionalInsured{
									{
										Id:               "RAI-1",
										SubCoverageGroup: subCoverageGroup2,
									},
									{
										Id:               "RAI-2",
										SubCoverageGroup: subCoverageGroup2,
									},
									{
										Id:               "RAI-3",
										SubCoverageGroup: subCoverageGroup2,
									},
									{
										Id:               "RAI-4",
										SubCoverageGroup: subCoverageGroup2,
									},
								},
								SpecifiedPrimaryAndNonContributoryAdditionalInsureds: []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
									{
										Id:               "PNCAI-1",
										SubCoverageGroup: subCoverageGroup2,
									},
									{
										Id:               "PNCAI-2",
										SubCoverageGroup: subCoverageGroup2,
									},
									{
										Id:               "PNCAI-3",
										SubCoverageGroup: subCoverageGroup2,
									},
									{
										Id:               "PNCAI-4",
										SubCoverageGroup: subCoverageGroup2,
									},
								},
								SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
									{
										Id:               "WOS-1",
										SubCoverageGroup: subCoverageGroup2,
									},
									{
										Id:               "WOS-2",
										SubCoverageGroup: subCoverageGroup2,
									},
								},
							},
						},
					},
					{
						PolicyNumber: "policy3",
						PolicyName:   policyName3,
						ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
							{
								ChunkId: "chunk1",
							},
							{
								ChunkId: "chunk2",
								RegularAdditionalInsuredBlankets: []*ptypes.BlanketRegularAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup3,
									},
								},
								PrimaryAndNonContributoryAdditionalInsuredBlankets: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup3,
									},
								},
								WaiverOfSubrogationBlankets: []*ptypes.BlanketWaiverOfSubrogation{
									{
										SubCoverageGroup: subCoverageGroup3,
									},
								},
								SpecifiedRegularAdditionalInsureds: []*ptypes.SpecifiedRegularAdditionalInsured{
									{
										Id:               "RAI-1",
										SubCoverageGroup: subCoverageGroup3,
									},
									{
										Id:               "RAI-2",
										SubCoverageGroup: subCoverageGroup3,
									},
									{
										Id:               "RAI-3",
										SubCoverageGroup: subCoverageGroup3,
									},
									{
										Id:               "RAI-4",
										SubCoverageGroup: subCoverageGroup3,
									},
								},
								SpecifiedPrimaryAndNonContributoryAdditionalInsureds: []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
									{
										Id:               "PNCAI-1",
										SubCoverageGroup: subCoverageGroup3,
									},
									{
										Id:               "PNCAI-2",
										SubCoverageGroup: subCoverageGroup3,
									},
									{
										Id:               "PNCAI-3",
										SubCoverageGroup: subCoverageGroup3,
									},
									{
										Id:               "PNCAI-4",
										SubCoverageGroup: subCoverageGroup3,
									},
								},
								SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
									{
										Id:               "WOS-1",
										SubCoverageGroup: subCoverageGroup3,
									},
									{
										Id:               "WOS-2",
										SubCoverageGroup: subCoverageGroup3,
									},
								},
							},
						},
					},
				},
			}
			expectedRequest := proto.Clone(req).(*ptypes.Request)
			expectedModifiedChunk1 := expectedRequest.PolicySpecs[0].ChunkSpecs[1]
			expectedModifiedChunk1.RegularAdditionalInsuredBlankets = nil
			expectedModifiedChunk1.PrimaryAndNonContributoryAdditionalInsuredBlankets = nil
			expectedModifiedChunk1.WaiverOfSubrogationBlankets = nil
			expectedModifiedChunk1.SpecifiedRegularAdditionalInsureds = nil
			expectedModifiedChunk1.SpecifiedPrimaryAndNonContributoryAdditionalInsureds = nil
			expectedModifiedChunk1.SpecifiedThirdPartiesWithWOS = nil
			expectedModifiedChunk2 := expectedRequest.PolicySpecs[1].ChunkSpecs[1]
			expectedModifiedChunk2.RegularAdditionalInsuredBlankets = nil
			expectedModifiedChunk2.PrimaryAndNonContributoryAdditionalInsuredBlankets = nil
			expectedModifiedChunk2.WaiverOfSubrogationBlankets = nil
			expectedModifiedChunk2.SpecifiedRegularAdditionalInsureds = nil
			expectedModifiedChunk2.SpecifiedPrimaryAndNonContributoryAdditionalInsureds = nil
			expectedModifiedChunk2.SpecifiedThirdPartiesWithWOS = nil
			expectedModifiedChunk3 := expectedRequest.PolicySpecs[2].ChunkSpecs[1]
			expectedModifiedChunk3.RegularAdditionalInsuredBlankets = nil
			expectedModifiedChunk3.PrimaryAndNonContributoryAdditionalInsuredBlankets = nil
			expectedModifiedChunk3.WaiverOfSubrogationBlankets = nil
			expectedModifiedChunk3.SpecifiedRegularAdditionalInsureds = nil
			expectedModifiedChunk3.SpecifiedPrimaryAndNonContributoryAdditionalInsureds = nil
			expectedModifiedChunk3.SpecifiedThirdPartiesWithWOS = nil
			mockOutput := plugins_common.PluginChainOutput{"key": &plugins_common.ChunkOutput{}}
			mockNextPluginApplyFn := func(_ context.Context, inp *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
				actualRequest, err := inp.GetPriceRequest()
				s.Require().NoError(err)
				s.Require().EqualExportedValues(expectedRequest, actualRequest)
				return mockOutput, nil
			}
			err := s.rule.SetNextPluginApplyFn(mockNextPluginApplyFn)
			s.Require().NoError(err)

			output, err := s.rule.Apply(s.ctx, &plugins_common.PluginChainInput{Request: req})
			s.Require().NoError(err)
			s.Require().Equal(mockOutput, output)
		})
	}
}

// TestApply_8:
//   - Two contiguous chunks.
//   - Three policies with different names.
//   - All policies are present in each chunk.
//   - One policy only has fully earned charges in the first chunk.
//   - The other policies only have charges in the second chunk.
//   - Fully earned charges are the same across policies and across chunks.
func (s *ruleTestSuite) TestApply_8() {
	for i := range allPolicyNames {
		policyName1 := allPolicyNames[i]
		policyName2 := allPolicyNames[(i+1)%3]
		policyName3 := allPolicyNames[(i+2)%3]
		s.Run(fmt.Sprintf("With %s", policyName1), func() {
			subCoverageGroup1 := policySubCoverageGroups[policyName1]
			subCoverageGroup2 := policySubCoverageGroups[policyName2]
			subCoverageGroup3 := policySubCoverageGroups[policyName3]
			startDate1 := time.Now()
			endDate1 := startDate1.AddDate(0, 0, 1)
			startDate2 := endDate1
			endDate2 := startDate2.AddDate(0, 0, 1)

			req := &ptypes.Request{
				BundleSpec: &ptypes.BundleSpec{
					ChunkSpecs: []*ptypes.BundleSpec_ChunkSpec{
						{
							ChunkId: "chunk1",
							Dates: &common_proto.Interval{
								Start: timestamppb.New(startDate1),
								End:   timestamppb.New(endDate1),
							},
						},
						{
							ChunkId: "chunk2",
							Dates: &common_proto.Interval{
								Start: timestamppb.New(startDate2),
								End:   timestamppb.New(endDate2),
							},
						},
					},
				},
				PolicySpecs: []*ptypes.PolicySpec{
					{
						PolicyNumber: "policy1",
						PolicyName:   policyName1,
						ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
							{
								ChunkId: "chunk1",
								RegularAdditionalInsuredBlankets: []*ptypes.BlanketRegularAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup1,
									},
								},
								PrimaryAndNonContributoryAdditionalInsuredBlankets: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup1,
									},
								},
								WaiverOfSubrogationBlankets: []*ptypes.BlanketWaiverOfSubrogation{
									{
										SubCoverageGroup: subCoverageGroup1,
									},
								},
								SpecifiedRegularAdditionalInsureds: []*ptypes.SpecifiedRegularAdditionalInsured{
									{
										Id:               "RAI-1",
										SubCoverageGroup: subCoverageGroup1,
									},
									{
										Id:               "RAI-2",
										SubCoverageGroup: subCoverageGroup1,
									},
									{
										Id:               "RAI-3",
										SubCoverageGroup: subCoverageGroup1,
									},
									{
										Id:               "RAI-4",
										SubCoverageGroup: subCoverageGroup1,
									},
								},
								SpecifiedPrimaryAndNonContributoryAdditionalInsureds: []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
									{
										Id:               "PNCAI-1",
										SubCoverageGroup: subCoverageGroup1,
									},
									{
										Id:               "PNCAI-2",
										SubCoverageGroup: subCoverageGroup1,
									},
									{
										Id:               "PNCAI-3",
										SubCoverageGroup: subCoverageGroup1,
									},
									{
										Id:               "PNCAI-4",
										SubCoverageGroup: subCoverageGroup1,
									},
								},
								SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
									{
										Id:               "WOS-1",
										SubCoverageGroup: subCoverageGroup1,
									},
									{
										Id:               "WOS-2",
										SubCoverageGroup: subCoverageGroup1,
									},
								},
							},
							{
								ChunkId: "chunk2",
							},
						},
					},
					{
						PolicyNumber: "policy2",
						PolicyName:   policyName2,
						ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
							{
								ChunkId: "chunk1",
							},
							{
								ChunkId: "chunk2",
								RegularAdditionalInsuredBlankets: []*ptypes.BlanketRegularAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup2,
									},
								},
								PrimaryAndNonContributoryAdditionalInsuredBlankets: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup2,
									},
								},
								WaiverOfSubrogationBlankets: []*ptypes.BlanketWaiverOfSubrogation{
									{
										SubCoverageGroup: subCoverageGroup2,
									},
								},
								SpecifiedRegularAdditionalInsureds: []*ptypes.SpecifiedRegularAdditionalInsured{
									{
										Id:               "RAI-1",
										SubCoverageGroup: subCoverageGroup2,
									},
									{
										Id:               "RAI-2",
										SubCoverageGroup: subCoverageGroup2,
									},
									{
										Id:               "RAI-3",
										SubCoverageGroup: subCoverageGroup2,
									},
									{
										Id:               "RAI-4",
										SubCoverageGroup: subCoverageGroup2,
									},
								},
								SpecifiedPrimaryAndNonContributoryAdditionalInsureds: []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
									{
										Id:               "PNCAI-1",
										SubCoverageGroup: subCoverageGroup2,
									},
									{
										Id:               "PNCAI-2",
										SubCoverageGroup: subCoverageGroup2,
									},
									{
										Id:               "PNCAI-3",
										SubCoverageGroup: subCoverageGroup2,
									},
									{
										Id:               "PNCAI-4",
										SubCoverageGroup: subCoverageGroup2,
									},
								},
								SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
									{
										Id:               "WOS-1",
										SubCoverageGroup: subCoverageGroup2,
									},
									{
										Id:               "WOS-2",
										SubCoverageGroup: subCoverageGroup2,
									},
								},
							},
						},
					},
					{
						PolicyNumber: "policy3",
						PolicyName:   policyName3,
						ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
							{
								ChunkId: "chunk1",
							},
							{
								ChunkId: "chunk2",
								RegularAdditionalInsuredBlankets: []*ptypes.BlanketRegularAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup3,
									},
								},
								PrimaryAndNonContributoryAdditionalInsuredBlankets: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
									{
										SubCoverageGroup: subCoverageGroup3,
									},
								},
								WaiverOfSubrogationBlankets: []*ptypes.BlanketWaiverOfSubrogation{
									{
										SubCoverageGroup: subCoverageGroup3,
									},
								},
								SpecifiedRegularAdditionalInsureds: []*ptypes.SpecifiedRegularAdditionalInsured{
									{
										Id:               "RAI-1",
										SubCoverageGroup: subCoverageGroup3,
									},
									{
										Id:               "RAI-2",
										SubCoverageGroup: subCoverageGroup3,
									},
									{
										Id:               "RAI-3",
										SubCoverageGroup: subCoverageGroup3,
									},
									{
										Id:               "RAI-4",
										SubCoverageGroup: subCoverageGroup3,
									},
								},
								SpecifiedPrimaryAndNonContributoryAdditionalInsureds: []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
									{
										Id:               "PNCAI-1",
										SubCoverageGroup: subCoverageGroup3,
									},
									{
										Id:               "PNCAI-2",
										SubCoverageGroup: subCoverageGroup3,
									},
									{
										Id:               "PNCAI-3",
										SubCoverageGroup: subCoverageGroup3,
									},
									{
										Id:               "PNCAI-4",
										SubCoverageGroup: subCoverageGroup3,
									},
								},
								SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
									{
										Id:               "WOS-1",
										SubCoverageGroup: subCoverageGroup3,
									},
									{
										Id:               "WOS-2",
										SubCoverageGroup: subCoverageGroup3,
									},
								},
							},
						},
					},
				},
			}
			expectedRequest := proto.Clone(req).(*ptypes.Request)
			expectedModifiedChunk1 := expectedRequest.PolicySpecs[1].ChunkSpecs[1]
			expectedModifiedChunk1.RegularAdditionalInsuredBlankets = nil
			expectedModifiedChunk1.PrimaryAndNonContributoryAdditionalInsuredBlankets = nil
			expectedModifiedChunk1.WaiverOfSubrogationBlankets = nil
			expectedModifiedChunk1.SpecifiedRegularAdditionalInsureds = nil
			expectedModifiedChunk1.SpecifiedPrimaryAndNonContributoryAdditionalInsureds = nil
			expectedModifiedChunk1.SpecifiedThirdPartiesWithWOS = nil
			expectedModifiedChunk2 := expectedRequest.PolicySpecs[2].ChunkSpecs[1]
			expectedModifiedChunk2.RegularAdditionalInsuredBlankets = nil
			expectedModifiedChunk2.PrimaryAndNonContributoryAdditionalInsuredBlankets = nil
			expectedModifiedChunk2.WaiverOfSubrogationBlankets = nil
			expectedModifiedChunk2.SpecifiedRegularAdditionalInsureds = nil
			expectedModifiedChunk2.SpecifiedPrimaryAndNonContributoryAdditionalInsureds = nil
			expectedModifiedChunk2.SpecifiedThirdPartiesWithWOS = nil
			mockOutput := plugins_common.PluginChainOutput{"key": &plugins_common.ChunkOutput{}}
			mockNextPluginApplyFn := func(_ context.Context, inp *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
				actualRequest, err := inp.GetPriceRequest()
				s.Require().NoError(err)
				s.Require().EqualExportedValues(expectedRequest, actualRequest)
				return mockOutput, nil
			}
			err := s.rule.SetNextPluginApplyFn(mockNextPluginApplyFn)
			s.Require().NoError(err)

			output, err := s.rule.Apply(s.ctx, &plugins_common.PluginChainInput{Request: req})
			s.Require().NoError(err)
			s.Require().Equal(mockOutput, output)
		})
	}
}

// TestApply_9:
//   - One chunk.
//   - All three policies (MC, GL, MTC).
//   - All policies are present in the chunk.
//   - All policies have fully earned charges in the chunk.
//   - Fully earned charges are the same across policies.
func (s *ruleTestSuite) TestApply_9() {
	startDate1 := time.Now()
	endDate1 := startDate1.AddDate(0, 0, 1)

	req := &ptypes.Request{
		BundleSpec: &ptypes.BundleSpec{
			ChunkSpecs: []*ptypes.BundleSpec_ChunkSpec{
				{
					ChunkId: "chunk1",
					Dates: &common_proto.Interval{
						Start: timestamppb.New(startDate1),
						End:   timestamppb.New(endDate1),
					},
				},
			},
		},
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: "policy1",
				PolicyName:   ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
				ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
					{
						ChunkId: "chunk1",
						RegularAdditionalInsuredBlankets: []*ptypes.BlanketRegularAdditionalInsured{
							{
								SubCoverageGroup: motorCarrierSubCoverageGroup,
							},
						},
						PrimaryAndNonContributoryAdditionalInsuredBlankets: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
							{
								SubCoverageGroup: motorCarrierSubCoverageGroup,
							},
						},
						WaiverOfSubrogationBlankets: []*ptypes.BlanketWaiverOfSubrogation{
							{
								SubCoverageGroup: motorCarrierSubCoverageGroup,
							},
						},
						SpecifiedRegularAdditionalInsureds: []*ptypes.SpecifiedRegularAdditionalInsured{
							{
								Id:               "RAI-1",
								SubCoverageGroup: motorCarrierSubCoverageGroup,
							},
							{
								Id:               "RAI-2",
								SubCoverageGroup: motorCarrierSubCoverageGroup,
							},
							{
								Id:               "RAI-3",
								SubCoverageGroup: motorCarrierSubCoverageGroup,
							},
							{
								Id:               "RAI-4",
								SubCoverageGroup: motorCarrierSubCoverageGroup,
							},
						},
						SpecifiedPrimaryAndNonContributoryAdditionalInsureds: []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
							{
								Id:               "PNCAI-1",
								SubCoverageGroup: motorCarrierSubCoverageGroup,
							},
							{
								Id:               "PNCAI-2",
								SubCoverageGroup: motorCarrierSubCoverageGroup,
							},
							{
								Id:               "PNCAI-3",
								SubCoverageGroup: motorCarrierSubCoverageGroup,
							},
							{
								Id:               "PNCAI-4",
								SubCoverageGroup: motorCarrierSubCoverageGroup,
							},
						},
						SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
							{
								Id:               "WOS-1",
								SubCoverageGroup: motorCarrierSubCoverageGroup,
							},
							{
								Id:               "WOS-2",
								SubCoverageGroup: motorCarrierSubCoverageGroup,
							},
						},
					},
				},
			},
			{
				PolicyNumber: "policy2",
				PolicyName:   ptypes.PolicyName_PolicyName_GENERAL_LIABILITY,
				ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
					{
						ChunkId: "chunk1",
						RegularAdditionalInsuredBlankets: []*ptypes.BlanketRegularAdditionalInsured{
							{
								SubCoverageGroup: generalLiabilitySubCoverageGroup,
							},
						},
						PrimaryAndNonContributoryAdditionalInsuredBlankets: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
							{
								SubCoverageGroup: generalLiabilitySubCoverageGroup,
							},
						},
						WaiverOfSubrogationBlankets: []*ptypes.BlanketWaiverOfSubrogation{
							{
								SubCoverageGroup: generalLiabilitySubCoverageGroup,
							},
						},
						SpecifiedRegularAdditionalInsureds: []*ptypes.SpecifiedRegularAdditionalInsured{
							{
								Id:               "RAI-1",
								SubCoverageGroup: generalLiabilitySubCoverageGroup,
							},
							{
								Id:               "RAI-2",
								SubCoverageGroup: generalLiabilitySubCoverageGroup,
							},
							{
								Id:               "RAI-3",
								SubCoverageGroup: generalLiabilitySubCoverageGroup,
							},
							{
								Id:               "RAI-4",
								SubCoverageGroup: generalLiabilitySubCoverageGroup,
							},
						},
						SpecifiedPrimaryAndNonContributoryAdditionalInsureds: []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
							{
								Id:               "PNCAI-1",
								SubCoverageGroup: generalLiabilitySubCoverageGroup,
							},
							{
								Id:               "PNCAI-2",
								SubCoverageGroup: generalLiabilitySubCoverageGroup,
							},
							{
								Id:               "PNCAI-3",
								SubCoverageGroup: generalLiabilitySubCoverageGroup,
							},
							{
								Id:               "PNCAI-4",
								SubCoverageGroup: generalLiabilitySubCoverageGroup,
							},
						},
						SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
							{
								Id:               "WOS-1",
								SubCoverageGroup: generalLiabilitySubCoverageGroup,
							},
							{
								Id:               "WOS-2",
								SubCoverageGroup: generalLiabilitySubCoverageGroup,
							},
						},
					},
				},
			},
			{
				PolicyNumber: "policy3",
				PolicyName:   ptypes.PolicyName_PolicyName_MOTOR_TRUCK_CARGO,
				ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
					{
						ChunkId: "chunk1",
						RegularAdditionalInsuredBlankets: []*ptypes.BlanketRegularAdditionalInsured{
							{
								SubCoverageGroup: motorTruckCargoSubCoverageGroup,
							},
						},
						PrimaryAndNonContributoryAdditionalInsuredBlankets: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
							{
								SubCoverageGroup: motorTruckCargoSubCoverageGroup,
							},
						},
						WaiverOfSubrogationBlankets: []*ptypes.BlanketWaiverOfSubrogation{
							{
								SubCoverageGroup: motorTruckCargoSubCoverageGroup,
							},
						},
						SpecifiedRegularAdditionalInsureds: []*ptypes.SpecifiedRegularAdditionalInsured{
							{
								Id:               "RAI-1",
								SubCoverageGroup: motorTruckCargoSubCoverageGroup,
							},
							{
								Id:               "RAI-2",
								SubCoverageGroup: motorTruckCargoSubCoverageGroup,
							},
							{
								Id:               "RAI-3",
								SubCoverageGroup: motorTruckCargoSubCoverageGroup,
							},
							{
								Id:               "RAI-4",
								SubCoverageGroup: motorTruckCargoSubCoverageGroup,
							},
						},
						SpecifiedPrimaryAndNonContributoryAdditionalInsureds: []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
							{
								Id:               "PNCAI-1",
								SubCoverageGroup: motorTruckCargoSubCoverageGroup,
							},
							{
								Id:               "PNCAI-2",
								SubCoverageGroup: motorTruckCargoSubCoverageGroup,
							},
							{
								Id:               "PNCAI-3",
								SubCoverageGroup: motorTruckCargoSubCoverageGroup,
							},
							{
								Id:               "PNCAI-4",
								SubCoverageGroup: motorTruckCargoSubCoverageGroup,
							},
						},
						SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
							{
								Id:               "WOS-1",
								SubCoverageGroup: motorTruckCargoSubCoverageGroup,
							},
							{
								Id:               "WOS-2",
								SubCoverageGroup: motorTruckCargoSubCoverageGroup,
							},
						},
					},
				},
			},
		},
	}
	expectedRequest := proto.Clone(req).(*ptypes.Request)
	expectedModifiedChunk1 := expectedRequest.PolicySpecs[1].ChunkSpecs[0]
	expectedModifiedChunk1.RegularAdditionalInsuredBlankets = nil
	expectedModifiedChunk1.PrimaryAndNonContributoryAdditionalInsuredBlankets = nil
	expectedModifiedChunk1.WaiverOfSubrogationBlankets = nil
	expectedModifiedChunk1.SpecifiedRegularAdditionalInsureds = nil
	expectedModifiedChunk1.SpecifiedPrimaryAndNonContributoryAdditionalInsureds = nil
	expectedModifiedChunk1.SpecifiedThirdPartiesWithWOS = nil
	expectedModifiedChunk2 := expectedRequest.PolicySpecs[2].ChunkSpecs[0]
	expectedModifiedChunk2.RegularAdditionalInsuredBlankets = nil
	expectedModifiedChunk2.PrimaryAndNonContributoryAdditionalInsuredBlankets = nil
	expectedModifiedChunk2.WaiverOfSubrogationBlankets = nil
	expectedModifiedChunk2.SpecifiedRegularAdditionalInsureds = nil
	expectedModifiedChunk2.SpecifiedPrimaryAndNonContributoryAdditionalInsureds = nil
	expectedModifiedChunk2.SpecifiedThirdPartiesWithWOS = nil
	mockOutput := plugins_common.PluginChainOutput{"key": &plugins_common.ChunkOutput{}}
	mockNextPluginApplyFn := func(_ context.Context, inp *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
		actualRequest, err := inp.GetPriceRequest()
		s.Require().NoError(err)
		s.Require().EqualExportedValues(expectedRequest, actualRequest)
		return mockOutput, nil
	}
	err := s.rule.SetNextPluginApplyFn(mockNextPluginApplyFn)
	s.Require().NoError(err)

	output, err := s.rule.Apply(s.ctx, &plugins_common.PluginChainInput{Request: req})
	s.Require().NoError(err)
	s.Require().Equal(mockOutput, output)
}

// TestApply_10:
//   - One chunk.
//   - Only GL and MTC policies.
//   - Both policies are present in the chunk.
//   - Both policies have fully earned charges in the chunk.
//   - Fully earned charges are the same across policies.
func (s *ruleTestSuite) TestApply_10() {
	startDate1 := time.Now()
	endDate1 := startDate1.AddDate(0, 0, 1)

	req := &ptypes.Request{
		BundleSpec: &ptypes.BundleSpec{
			ChunkSpecs: []*ptypes.BundleSpec_ChunkSpec{
				{
					ChunkId: "chunk1",
					Dates: &common_proto.Interval{
						Start: timestamppb.New(startDate1),
						End:   timestamppb.New(endDate1),
					},
				},
			},
		},
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: "policy1",
				PolicyName:   ptypes.PolicyName_PolicyName_GENERAL_LIABILITY,
				ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
					{
						ChunkId: "chunk1",
						RegularAdditionalInsuredBlankets: []*ptypes.BlanketRegularAdditionalInsured{
							{
								SubCoverageGroup: generalLiabilitySubCoverageGroup,
							},
						},
						PrimaryAndNonContributoryAdditionalInsuredBlankets: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
							{
								SubCoverageGroup: generalLiabilitySubCoverageGroup,
							},
						},
						WaiverOfSubrogationBlankets: []*ptypes.BlanketWaiverOfSubrogation{
							{
								SubCoverageGroup: generalLiabilitySubCoverageGroup,
							},
						},
						SpecifiedRegularAdditionalInsureds: []*ptypes.SpecifiedRegularAdditionalInsured{
							{
								Id:               "RAI-1",
								SubCoverageGroup: generalLiabilitySubCoverageGroup,
							},
							{
								Id:               "RAI-2",
								SubCoverageGroup: generalLiabilitySubCoverageGroup,
							},
							{
								Id:               "RAI-3",
								SubCoverageGroup: generalLiabilitySubCoverageGroup,
							},
							{
								Id:               "RAI-4",
								SubCoverageGroup: generalLiabilitySubCoverageGroup,
							},
						},
						SpecifiedPrimaryAndNonContributoryAdditionalInsureds: []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
							{
								Id:               "PNCAI-1",
								SubCoverageGroup: generalLiabilitySubCoverageGroup,
							},
							{
								Id:               "PNCAI-2",
								SubCoverageGroup: generalLiabilitySubCoverageGroup,
							},
							{
								Id:               "PNCAI-3",
								SubCoverageGroup: generalLiabilitySubCoverageGroup,
							},
							{
								Id:               "PNCAI-4",
								SubCoverageGroup: generalLiabilitySubCoverageGroup,
							},
						},
						SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
							{
								Id:               "WOS-1",
								SubCoverageGroup: generalLiabilitySubCoverageGroup,
							},
							{
								Id:               "WOS-2",
								SubCoverageGroup: generalLiabilitySubCoverageGroup,
							},
						},
					},
				},
			},
			{
				PolicyNumber: "policy2",
				PolicyName:   ptypes.PolicyName_PolicyName_MOTOR_TRUCK_CARGO,
				ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
					{
						ChunkId: "chunk1",
						RegularAdditionalInsuredBlankets: []*ptypes.BlanketRegularAdditionalInsured{
							{
								SubCoverageGroup: motorTruckCargoSubCoverageGroup,
							},
						},
						PrimaryAndNonContributoryAdditionalInsuredBlankets: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
							{
								SubCoverageGroup: motorTruckCargoSubCoverageGroup,
							},
						},
						WaiverOfSubrogationBlankets: []*ptypes.BlanketWaiverOfSubrogation{
							{
								SubCoverageGroup: motorTruckCargoSubCoverageGroup,
							},
						},
						SpecifiedRegularAdditionalInsureds: []*ptypes.SpecifiedRegularAdditionalInsured{
							{
								Id:               "RAI-1",
								SubCoverageGroup: motorTruckCargoSubCoverageGroup,
							},
							{
								Id:               "RAI-2",
								SubCoverageGroup: motorTruckCargoSubCoverageGroup,
							},
							{
								Id:               "RAI-3",
								SubCoverageGroup: motorTruckCargoSubCoverageGroup,
							},
							{
								Id:               "RAI-4",
								SubCoverageGroup: motorTruckCargoSubCoverageGroup,
							},
						},
						SpecifiedPrimaryAndNonContributoryAdditionalInsureds: []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
							{
								Id:               "PNCAI-1",
								SubCoverageGroup: motorTruckCargoSubCoverageGroup,
							},
							{
								Id:               "PNCAI-2",
								SubCoverageGroup: motorTruckCargoSubCoverageGroup,
							},
							{
								Id:               "PNCAI-3",
								SubCoverageGroup: motorTruckCargoSubCoverageGroup,
							},
							{
								Id:               "PNCAI-4",
								SubCoverageGroup: motorTruckCargoSubCoverageGroup,
							},
						},
						SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
							{
								Id:               "WOS-1",
								SubCoverageGroup: motorTruckCargoSubCoverageGroup,
							},
							{
								Id:               "WOS-2",
								SubCoverageGroup: motorTruckCargoSubCoverageGroup,
							},
						},
					},
				},
			},
		},
	}
	expectedRequest := proto.Clone(req).(*ptypes.Request)
	expectedModifiedChunk1 := expectedRequest.PolicySpecs[1].ChunkSpecs[0]
	expectedModifiedChunk1.RegularAdditionalInsuredBlankets = nil
	expectedModifiedChunk1.PrimaryAndNonContributoryAdditionalInsuredBlankets = nil
	expectedModifiedChunk1.WaiverOfSubrogationBlankets = nil
	expectedModifiedChunk1.SpecifiedRegularAdditionalInsureds = nil
	expectedModifiedChunk1.SpecifiedPrimaryAndNonContributoryAdditionalInsureds = nil
	expectedModifiedChunk1.SpecifiedThirdPartiesWithWOS = nil
	mockOutput := plugins_common.PluginChainOutput{"key": &plugins_common.ChunkOutput{}}
	mockNextPluginApplyFn := func(_ context.Context, inp *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
		actualRequest, err := inp.GetPriceRequest()
		s.Require().NoError(err)
		s.Require().EqualExportedValues(expectedRequest, actualRequest)
		return mockOutput, nil
	}
	err := s.rule.SetNextPluginApplyFn(mockNextPluginApplyFn)
	s.Require().NoError(err)

	output, err := s.rule.Apply(s.ctx, &plugins_common.PluginChainInput{Request: req})
	s.Require().NoError(err)
	s.Require().Equal(mockOutput, output)
}
