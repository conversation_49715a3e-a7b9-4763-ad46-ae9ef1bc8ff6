package engine

import (
	"context"
	"testing"

	"github.com/cockroachdb/errors"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"go.uber.org/mock/gomock"

	"nirvanatech.com/nirvana/common-go/us_states"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins"
	plugins_common "nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/common"
	"nirvanatech.com/nirvana/rating/pricing/api/parsers"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
	"nirvanatech.com/nirvana/rating/pricing/api/validators"
	"nirvanatech.com/nirvana/rating/rtypes"
)

type engineTestEnv struct {
	fx.In

	Engine                 EngineI
	RateMLExecutorFactory  ModelExecutorFactory
	FetcherClientFactory   data_fetching.FetcherClientFactory
	ProcessorClientFactory data_processing.ProcessorClientFactory
}

type EngineTestSuite struct {
	suite.Suite

	env   engineTestEnv
	fxApp *fxtest.App
	ctrl  *gomock.Controller

	ctx          context.Context
	policyNumber string

	mockRequestValidator *validators.MockRequestValidator
	mockResponseBuilder  *MockResponseBuilder
	mockModelKeyParser   *parsers.MockModelKeyParser
	mockPlugin1          *plugins_common.MockPlugin
	mockPlugin2          *plugins_common.MockPlugin
	mockPlugin3          *plugins_common.MockPlugin
	mockPluginHelper     *plugins.MockHelper
	mockModelExecutor    *MockModelExecutor
}

func TestEngine(t *testing.T) {
	suite.Run(t, new(EngineTestSuite))
}

func (s *EngineTestSuite) SetupSuite() {
	s.ctx = context.Background()
	s.policyNumber = "NK123456"

	s.ctrl, s.ctx = gomock.WithContext(s.ctx, s.T())

	s.mockPluginHelper = plugins.NewMockHelper(s.ctrl)
	newMockPluginHelper := func() plugins.HelperI {
		return s.mockPluginHelper
	}

	s.mockRequestValidator = validators.NewMockRequestValidator(s.ctrl)
	mockRequestValidator := func() validators.RequestValidatorI {
		return s.mockRequestValidator
	}

	s.mockResponseBuilder = NewMockResponseBuilder(s.ctrl)
	newMockResponseBuilder := func() ResponseBuilderI {
		return s.mockResponseBuilder
	}

	s.mockModelExecutor = NewMockModelExecutor(s.ctrl)
	newMockModelExecutorFactory := func(deps modelExecutorDeps) ModelExecutorFactory {
		return func() ModelExecutorI {
			return s.mockModelExecutor
		}
	}

	s.mockModelKeyParser = parsers.NewMockModelKeyParser(s.ctrl)
	newMockModelKeyParser := func() parsers.ModelKeyParserI {
		return s.mockModelKeyParser
	}

	s.fxApp = testloader.RequireStart(
		s.T(),
		&s.env,
		testloader.Use(
			fx.Decorate(newMockPluginHelper),
			fx.Decorate(mockRequestValidator),
			fx.Decorate(newMockResponseBuilder),
			fx.Decorate(newMockModelExecutorFactory),
			fx.Decorate(newMockModelKeyParser),
		),
	)
}

func (s *EngineTestSuite) SetupTest() {
	s.mockPlugin1 = plugins_common.NewMockPlugin(s.ctrl)
	s.mockPlugin2 = plugins_common.NewMockPlugin(s.ctrl)
	s.mockPlugin3 = plugins_common.NewMockPlugin(s.ctrl)
}

func (s *EngineTestSuite) TearDownSuite() {
	s.fxApp.RequireStop()
}

func (s *EngineTestSuite) Test_Run_WithInvalidRequest() {
	request := &ptypes.Request{}
	validationErr := errors.New("invalid request")

	s.mockRequestValidator.EXPECT().
		Validate(s.ctx, request).
		Return(validationErr).
		Times(1)

	response, err := s.env.Engine.Run(s.ctx, request)
	s.Require().Nil(response)
	s.Require().Error(err)
	s.Require().ErrorIs(err, validationErr)
}

func (s *EngineTestSuite) Test_Run_WithErrorFromModelKeyParser() {
	modelSpec := &ptypes.ModelSpec{
		Provider: "provider",
		State:    "state",
		Version: &ptypes.ModelVersion{
			Major: 1,
			Minor: 2,
			Patch: 3,
		},
	}

	request := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: s.policyNumber,
				ModelSpec:    modelSpec,
			},
		},
	}

	s.mockRequestValidator.EXPECT().
		Validate(s.ctx, request).
		Return(nil).
		Times(1)

	parsingErr := errors.New("invalid model spec")
	s.mockModelKeyParser.EXPECT().
		Parse(modelSpec).
		Return(nil, parsingErr).
		Times(1)

	response, err := s.env.Engine.Run(s.ctx, request)
	s.Require().Nil(response)
	s.Require().Error(err)
	s.Require().ErrorIs(err, parsingErr)
	s.Require().Regexp("failed to parse model key for policy", err.Error())
	s.Require().Regexp("failed to get plugins for policy", err.Error())
}

func (s *EngineTestSuite) Test_Run_WithErrorGettingPlugins() {
	request := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: s.policyNumber,
				ModelSpec:    &ptypes.ModelSpec{},
				OptionalPlugins: []ptypes.PluginID{
					ptypes.PluginID_PluginID_MockPlugin_V1,
					ptypes.PluginID_PluginID_MockPlugin_V2,
				},
			},
		},
	}

	s.mockRequestValidator.EXPECT().
		Validate(s.ctx, request).
		Return(nil).
		Times(1)

	mk := rtypes.NewModelKey(rtypes.ProviderProgressive, us_states.AK, rtypes.Version003)
	s.mockModelKeyParser.EXPECT().
		Parse(gomock.Any()).
		Return(&mk, nil).
		Times(1)

	s.mockPluginHelper.EXPECT().
		GetMandatoryPlugins(mk).
		Return([]ptypes.PluginID{
			ptypes.PluginID_PluginID_MockPlugin_V1,
			ptypes.PluginID_PluginID_MockPlugin_V3,
		}).
		Times(1)

	expectedPlugins := []ptypes.PluginID{
		ptypes.PluginID_PluginID_MockPlugin_V1,
		ptypes.PluginID_PluginID_MockPlugin_V3,
		ptypes.PluginID_PluginID_MockPlugin_V2,
	}
	s.mockPluginHelper.EXPECT().
		SortPlugins(expectedPlugins).
		Times(1)

	pluginsValidationError := errors.New("failed to validate plugins")
	s.mockPluginHelper.EXPECT().
		ValidatePlugins(expectedPlugins).
		Return(pluginsValidationError).
		Times(1)

	response, err := s.env.Engine.Run(s.ctx, request)
	s.Require().Nil(response)
	s.Require().Error(err)
	s.Require().ErrorIs(err, pluginsValidationError)
	s.Require().Regexp("failed to validate plugins for policy", err.Error())
	s.Require().Regexp("failed to get plugins for policy", err.Error())
}

func (s *EngineTestSuite) Test_Run_WithNilPluginFactory() {
	request := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: s.policyNumber,
				ModelSpec:    &ptypes.ModelSpec{},
			},
		},
	}

	s.mockRequestValidator.EXPECT().
		Validate(s.ctx, request).
		Return(nil).
		Times(1)

	mk := rtypes.NewModelKey(rtypes.ProviderProgressive, us_states.AK, rtypes.Version003)
	s.mockModelKeyParser.EXPECT().
		Parse(gomock.Any()).
		Return(&mk, nil).
		Times(1)

	s.mockPluginHelper.EXPECT().
		GetMandatoryPlugins(mk).
		Return([]ptypes.PluginID{
			ptypes.PluginID_PluginID_MockPlugin_V1,
		}).
		Times(1)

	s.mockPluginHelper.EXPECT().
		SortPlugins(gomock.Any()).
		Times(1)

	s.mockPluginHelper.EXPECT().
		ValidatePlugins(gomock.Any()).
		Return(nil).
		Times(1)

	s.mockPluginHelper.EXPECT().
		GetPluginFactory(ptypes.PluginID_PluginID_MockPlugin_V1).
		Return(nil).
		Times(1)

	response, err := s.env.Engine.Run(s.ctx, request)
	s.Require().Nil(response)
	s.Require().Error(err)
	s.Require().Regexp("plugin factory is nil for plugin", err.Error())
	s.Require().Regexp("failed to build plugin chain", err.Error())
}

func (s *EngineTestSuite) Test_Run_WithErrorCreatingPlugin() {
	request := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: s.policyNumber,
				ModelSpec:    &ptypes.ModelSpec{},
			},
		},
	}

	s.mockRequestValidator.EXPECT().
		Validate(s.ctx, request).
		Return(nil).
		Times(1)

	mk := rtypes.NewModelKey(rtypes.ProviderProgressive, us_states.AK, rtypes.Version003)
	s.mockModelKeyParser.EXPECT().
		Parse(gomock.Any()).
		Return(&mk, nil).
		Times(1)

	s.mockPluginHelper.EXPECT().
		GetMandatoryPlugins(mk).
		Return([]ptypes.PluginID{
			ptypes.PluginID_PluginID_MockPlugin_V1,
		}).
		Times(1)

	s.mockPluginHelper.EXPECT().
		SortPlugins(gomock.Any()).
		Times(1)

	s.mockPluginHelper.EXPECT().
		ValidatePlugins(gomock.Any()).
		Return(nil).
		Times(1)

	pluginCreationErr := errors.New("failed to initialize plugin")
	s.mockPluginHelper.EXPECT().
		GetPluginFactory(ptypes.PluginID_PluginID_MockPlugin_V1).
		Return(func() (plugins_common.PluginI, error) {
			return nil, pluginCreationErr
		}).
		Times(1)

	response, err := s.env.Engine.Run(s.ctx, request)
	s.Require().Nil(response)
	s.Require().Error(err)
	s.Require().ErrorIs(err, pluginCreationErr)
	s.Require().Regexp("failed to create plugin with ID: PluginID_MockPlugin_V1", err.Error())
	s.Require().Regexp("failed to build plugin chain", err.Error())
}

func (s *EngineTestSuite) Test_Run_WithErrorSettingFirstPluginApplyFn() {
	request := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: s.policyNumber,
				ModelSpec:    &ptypes.ModelSpec{},
			},
		},
	}

	s.mockRequestValidator.EXPECT().
		Validate(s.ctx, request).
		Return(nil).
		Times(1)

	mk := rtypes.NewModelKey(rtypes.ProviderProgressive, us_states.AK, rtypes.Version003)
	s.mockModelKeyParser.EXPECT().
		Parse(gomock.Any()).
		Return(&mk, nil).
		Times(1)

	s.mockPluginHelper.EXPECT().
		GetMandatoryPlugins(mk).
		Return([]ptypes.PluginID{
			ptypes.PluginID_PluginID_MockPlugin_V1,
		}).
		Times(1)

	s.mockPluginHelper.EXPECT().
		SortPlugins(gomock.Any()).
		Times(1)

	s.mockPluginHelper.EXPECT().
		ValidatePlugins(gomock.Any()).
		Return(nil).
		Times(1)

	s.mockPluginHelper.EXPECT().
		GetPluginFactory(ptypes.PluginID_PluginID_MockPlugin_V1).
		Return(func() (plugins_common.PluginI, error) {
			return s.mockPlugin1, nil
		}).
		Times(1)

	setNextFnErr := errors.New("failed to set apply fn")
	s.mockPlugin1.EXPECT().
		SetNextPluginApplyFn(gomock.Any()).
		Return(setNextFnErr).
		Times(1)

	response, err := s.env.Engine.Run(s.ctx, request)
	s.Require().Nil(response)
	s.Require().Error(err)
	s.Require().ErrorIs(err, setNextFnErr)
	s.Require().Regexp("failed to set apply function for plugin PluginID_MockPlugin_V1", err.Error())
	s.Require().Regexp("failed to build plugin chain", err.Error())
}

func (s *EngineTestSuite) Test_Run_WithErrorSettingRemainingPluginApplyFn() {
	request := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: s.policyNumber,
				ModelSpec:    &ptypes.ModelSpec{},
			},
		},
	}

	s.mockRequestValidator.EXPECT().
		Validate(s.ctx, request).
		Return(nil).
		Times(1)

	mk := rtypes.NewModelKey(rtypes.ProviderProgressive, us_states.AK, rtypes.Version003)
	s.mockModelKeyParser.EXPECT().
		Parse(gomock.Any()).
		Return(&mk, nil).
		Times(1)

	s.mockPluginHelper.EXPECT().
		GetMandatoryPlugins(mk).
		Return([]ptypes.PluginID{
			ptypes.PluginID_PluginID_MockPlugin_V1,
			ptypes.PluginID_PluginID_MockPlugin_V2,
		}).
		Times(1)

	s.mockPluginHelper.EXPECT().
		SortPlugins(gomock.Any()).
		Times(1)

	s.mockPluginHelper.EXPECT().
		ValidatePlugins(gomock.Any()).
		Return(nil).
		Times(1)

	s.mockPluginHelper.EXPECT().
		GetPluginFactory(ptypes.PluginID_PluginID_MockPlugin_V1).
		Return(func() (plugins_common.PluginI, error) {
			return s.mockPlugin1, nil
		}).
		Times(1)
	s.mockPluginHelper.EXPECT().
		GetPluginFactory(ptypes.PluginID_PluginID_MockPlugin_V2).
		Return(func() (plugins_common.PluginI, error) {
			return s.mockPlugin2, nil
		}).
		Times(1)

	setNextFnErr := errors.New("failed to set apply fn for second plugin")
	s.mockPlugin1.EXPECT().
		SetNextPluginApplyFn(gomock.Any()).
		Return(setNextFnErr).
		Times(1)

	s.mockPlugin2.EXPECT().
		SetNextPluginApplyFn(gomock.Any()).
		Return(nil).
		Times(1)

	response, err := s.env.Engine.Run(s.ctx, request)
	s.Require().Nil(response)
	s.Require().Error(err)
	s.Require().ErrorIs(err, setNextFnErr)
	s.Require().Regexp("failed to set apply function for plugin PluginID_MockPlugin_V1", err.Error())
	s.Require().Regexp("failed to build plugin chain", err.Error())
}

func (s *EngineTestSuite) Test_Run_WithErrorExecutingPluginChain() {
	request := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: s.policyNumber,
				ModelSpec:    &ptypes.ModelSpec{},
			},
		},
	}

	s.mockRequestValidator.EXPECT().
		Validate(s.ctx, request).
		Return(nil).
		Times(1)

	mk := rtypes.NewModelKey(rtypes.ProviderProgressive, us_states.AK, rtypes.Version003)
	s.mockModelKeyParser.EXPECT().
		Parse(gomock.Any()).
		Return(&mk, nil).
		Times(1)

	s.mockPluginHelper.EXPECT().
		GetMandatoryPlugins(mk).
		Return([]ptypes.PluginID{
			ptypes.PluginID_PluginID_MockPlugin_V1,
		}).
		Times(1)

	s.mockPluginHelper.EXPECT().
		SortPlugins(gomock.Any()).
		Times(1)

	s.mockPluginHelper.EXPECT().
		ValidatePlugins(gomock.Any()).
		Return(nil).
		Times(1)

	s.mockPluginHelper.EXPECT().
		GetPluginFactory(ptypes.PluginID_PluginID_MockPlugin_V1).
		Return(func() (plugins_common.PluginI, error) {
			return s.mockPlugin1, nil
		}).
		Times(1)

	s.mockPlugin1.EXPECT().
		SetNextPluginApplyFn(gomock.Any()).
		Return(nil).
		Times(1)

	applyErr := errors.New("failed to apply plugin")
	s.mockPlugin1.EXPECT().
		Apply(s.ctx, &plugins_common.PluginChainInput{
			Request:      request,
			PolicyNumber: s.policyNumber,
		}).
		Return(nil, applyErr).
		Times(1)

	response, err := s.env.Engine.Run(s.ctx, request)
	s.Require().Nil(response)
	s.Require().Error(err)
	s.Require().ErrorIs(err, applyErr)
	s.Require().Regexp("error executing plugin chain", err.Error())
}

func (s *EngineTestSuite) Test_Run_WhenResponseBuilderFails() {
	chunkID := "chunk-1"
	request := &ptypes.Request{
		BundleSpec: &ptypes.BundleSpec{
			ChunkSpecs: []*ptypes.BundleSpec_ChunkSpec{
				{
					ChunkId: chunkID,
				},
			},
		},
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: s.policyNumber,
				ModelSpec:    &ptypes.ModelSpec{},
			},
		},
	}

	s.mockRequestValidator.EXPECT().
		Validate(s.ctx, request).
		Return(nil).
		Times(1)

	mk := rtypes.NewModelKey(rtypes.ProviderProgressive, us_states.AK, rtypes.Version003)
	s.mockModelKeyParser.EXPECT().
		Parse(gomock.Any()).
		Return(&mk, nil).
		Times(1)

	s.mockPluginHelper.EXPECT().
		GetMandatoryPlugins(mk).
		Return([]ptypes.PluginID{
			ptypes.PluginID_PluginID_MockPlugin_V1,
		}).
		Times(1)

	s.mockPluginHelper.EXPECT().
		SortPlugins(gomock.Any()).
		Times(1)

	s.mockPluginHelper.EXPECT().
		ValidatePlugins(gomock.Any()).
		Return(nil).
		Times(1)

	s.mockPluginHelper.EXPECT().
		GetPluginFactory(ptypes.PluginID_PluginID_MockPlugin_V1).
		Return(func() (plugins_common.PluginI, error) {
			return s.mockPlugin1, nil
		}).
		Times(1)

	s.mockPlugin1.EXPECT().
		SetNextPluginApplyFn(gomock.Any()).
		Return(nil).
		Times(1)

	expectedPluginChainResponse := plugins_common.PluginChainOutput{
		chunkID: &plugins_common.ChunkOutput{
			Charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().Build(),
			},
		},
	}
	s.mockPlugin1.EXPECT().
		Apply(s.ctx, &plugins_common.PluginChainInput{
			Request:      request,
			PolicyNumber: s.policyNumber,
		}).
		Return(expectedPluginChainResponse, nil).
		Times(1)

	policyOutputs := map[string]plugins_common.PluginChainOutput{
		s.policyNumber: expectedPluginChainResponse,
	}

	buildResponseErr := errors.New("response builder error")
	s.mockResponseBuilder.EXPECT().
		Build(request.PolicySpecs, policyOutputs).
		Return(nil, buildResponseErr).
		Times(1)

	response, err := s.env.Engine.Run(s.ctx, request)
	s.Require().Nil(response)
	s.Require().Error(err)
	s.Require().ErrorIs(err, buildResponseErr)
	s.Require().Regexp("failed to build response", err.Error())
}

func (s *EngineTestSuite) Test_Run_WithValidRequest() {
	chunkID := "chunk-1"
	request := &ptypes.Request{
		BundleSpec: &ptypes.BundleSpec{
			ChunkSpecs: []*ptypes.BundleSpec_ChunkSpec{
				{
					ChunkId: chunkID,
				},
			},
		},
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: s.policyNumber,
				ModelSpec:    &ptypes.ModelSpec{},
			},
		},
	}

	s.mockRequestValidator.EXPECT().
		Validate(s.ctx, request).
		Return(nil).
		Times(1)

	mk := rtypes.NewModelKey(rtypes.ProviderProgressive, us_states.AK, rtypes.Version003)
	s.mockModelKeyParser.EXPECT().
		Parse(gomock.Any()).
		Return(&mk, nil).
		Times(1)

	s.mockPluginHelper.EXPECT().
		GetMandatoryPlugins(mk).
		Return(nil).
		Times(1)

	s.mockPluginHelper.EXPECT().
		SortPlugins(nil).
		Times(1)

	s.mockPluginHelper.EXPECT().
		ValidatePlugins(nil).
		Return(nil).
		Times(1)

	expectedPluginChainResponse := plugins_common.PluginChainOutput{
		chunkID: &plugins_common.ChunkOutput{
			Charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().Build(),
			},
		},
	}
	s.mockModelExecutor.EXPECT().
		Execute(s.ctx, &plugins_common.PluginChainInput{
			Request:      request,
			PolicyNumber: s.policyNumber,
		}).
		Return(expectedPluginChainResponse, nil).
		Times(1)

	policyOutputs := map[string]plugins_common.PluginChainOutput{
		s.policyNumber: expectedPluginChainResponse,
	}

	expectedResponse := &ptypes.Response{
		PolicyOutputs: []*ptypes.PolicyOutput{
			{
				PolicyNumber: s.policyNumber,
				ChunkOutputs: []*ptypes.ChunkOutput{
					{
						ChunkID: "chunk-1",
					},
				},
			},
		},
	}

	s.mockResponseBuilder.EXPECT().
		Build(request.PolicySpecs, policyOutputs).
		Return(expectedResponse, nil).
		Times(1)

	response, err := s.env.Engine.Run(s.ctx, request)
	s.Require().NoError(err)
	s.Require().Equal(expectedResponse, response)
}

func (s *EngineTestSuite) Test_Run_WithChecksForCorrectPluginOrder() {
	chunkID := "chunk-1"
	request := &ptypes.Request{
		BundleSpec: &ptypes.BundleSpec{
			ChunkSpecs: []*ptypes.BundleSpec_ChunkSpec{
				{
					ChunkId: chunkID,
				},
			},
		},
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: s.policyNumber,
				ModelSpec:    &ptypes.ModelSpec{},
			},
		},
	}

	s.mockRequestValidator.EXPECT().
		Validate(s.ctx, request).
		Return(nil).
		Times(1)

	mk := rtypes.NewModelKey(rtypes.ProviderProgressive, us_states.AK, rtypes.Version003)
	s.mockModelKeyParser.EXPECT().
		Parse(gomock.Any()).
		Return(&mk, nil).
		Times(1)

	mockMandatoryPlugins := []ptypes.PluginID{
		ptypes.PluginID_PluginID_MockPlugin_V1,
		ptypes.PluginID_PluginID_MockPlugin_V2,
		ptypes.PluginID_PluginID_MockPlugin_V3,
	}
	s.mockPluginHelper.EXPECT().
		GetMandatoryPlugins(mk).
		Return(mockMandatoryPlugins).
		Times(1)

	// We define these variables to store the value passed into
	// the SetNextPluginApplyFn method of each plugin. We are
	// able to intercept this value thanks to the mock plugin.
	var plugin1NextApplyFn, plugin2NextApplyFn, plugin3NextApplyFn plugins_common.PluginApplyFn

	s.mockPlugin1.EXPECT().
		SetNextPluginApplyFn(gomock.Any()).
		DoAndReturn(func(fn plugins_common.PluginApplyFn) error {
			plugin1NextApplyFn = fn
			return nil
		}).
		Times(1)

	s.mockPlugin2.EXPECT().
		SetNextPluginApplyFn(gomock.Any()).
		DoAndReturn(func(fn plugins_common.PluginApplyFn) error {
			plugin2NextApplyFn = fn
			return nil
		}).
		Times(1)

	s.mockPlugin3.EXPECT().
		SetNextPluginApplyFn(gomock.Any()).
		DoAndReturn(func(fn plugins_common.PluginApplyFn) error {
			plugin3NextApplyFn = fn
			return nil
		}).
		Times(1)

	initialApplyParams := &plugins_common.PluginChainInput{
		Request:      request,
		PolicyNumber: s.policyNumber,
	}

	// We mock the sorting of the plugins, so that the plugins are executed
	// in the order plugin3 -> plugin2 -> plugin1.
	mockSortedPlugins := []ptypes.PluginID{
		ptypes.PluginID_PluginID_MockPlugin_V3,
		ptypes.PluginID_PluginID_MockPlugin_V2,
		ptypes.PluginID_PluginID_MockPlugin_V1,
	}
	s.mockPluginHelper.EXPECT().
		SortPlugins(mockMandatoryPlugins).
		DoAndReturn(func(plugins []ptypes.PluginID) {
			s.Require().Len(plugins, 3)
			plugins[0] = mockSortedPlugins[0]
			plugins[1] = mockSortedPlugins[1]
			plugins[2] = mockSortedPlugins[2]
		}).
		Times(1)

	s.mockPluginHelper.EXPECT().
		ValidatePlugins(mockSortedPlugins).
		Return(nil).
		Times(1)

	s.mockPluginHelper.EXPECT().
		GetPluginFactory(ptypes.PluginID_PluginID_MockPlugin_V1).
		Return(func() (plugins_common.PluginI, error) {
			return s.mockPlugin1, nil
		}).
		Times(1)
	s.mockPluginHelper.EXPECT().
		GetPluginFactory(ptypes.PluginID_PluginID_MockPlugin_V2).
		Return(func() (plugins_common.PluginI, error) {
			return s.mockPlugin2, nil
		}).
		Times(1)
	s.mockPluginHelper.EXPECT().
		GetPluginFactory(ptypes.PluginID_PluginID_MockPlugin_V3).
		Return(func() (plugins_common.PluginI, error) {
			return s.mockPlugin3, nil
		}).
		Times(1)

	// pluginCallTurn is used to assert the order of the calls
	pluginCallTurn := 0

	pluginChainResponse := plugins_common.PluginChainOutput{
		chunkID: &plugins_common.ChunkOutput{
			Charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().Build(),
			},
		},
	}

	s.mockModelExecutor.EXPECT().
		Execute(s.ctx, initialApplyParams).
		DoAndReturn(func(ctx context.Context, p *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
			s.Require().Equal(pluginCallTurn, 3)
			pluginCallTurn = 4
			return pluginChainResponse, nil
		}).
		Times(1)

	s.mockPlugin1.EXPECT().
		Apply(s.ctx, initialApplyParams).
		DoAndReturn(func(ctx context.Context, p *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
			s.Require().Equal(pluginCallTurn, 2)
			pluginCallTurn = 3
			res, err := plugin1NextApplyFn(ctx, p)
			s.Require().Equal(pluginCallTurn, 4)
			pluginCallTurn = 5
			return res, err
		}).
		Times(1)

	s.mockPlugin2.EXPECT().
		Apply(s.ctx, initialApplyParams).
		DoAndReturn(func(ctx context.Context, p *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
			s.Require().Equal(pluginCallTurn, 1)
			pluginCallTurn = 2
			res, err := plugin2NextApplyFn(ctx, p)
			s.Require().Equal(pluginCallTurn, 5)
			pluginCallTurn = 6
			return res, err
		}).
		Times(1)

	s.mockPlugin3.EXPECT().
		Apply(s.ctx, initialApplyParams).
		DoAndReturn(func(ctx context.Context, p *plugins_common.PluginChainInput) (plugins_common.PluginChainOutput, error) {
			s.Require().Equal(pluginCallTurn, 0)
			pluginCallTurn = 1
			res, err := plugin3NextApplyFn(ctx, p)
			s.Require().Equal(pluginCallTurn, 6)
			pluginCallTurn = 7
			return res, err
		}).
		Times(1)

	expectedPolicyOutputs := map[string]plugins_common.PluginChainOutput{
		s.policyNumber: pluginChainResponse,
	}
	expectedResponse := &ptypes.Response{
		PolicyOutputs: []*ptypes.PolicyOutput{
			{
				PolicyNumber: s.policyNumber,
				ChunkOutputs: []*ptypes.ChunkOutput{
					{
						ChunkID: chunkID,
					},
				},
			},
		},
	}
	s.mockResponseBuilder.EXPECT().
		Build(request.PolicySpecs, expectedPolicyOutputs).
		Return(expectedResponse, nil).
		Times(1)

	response, err := s.env.Engine.Run(s.ctx, request)
	s.Require().NoError(err)
	s.Require().Equal(expectedResponse, response)
}
