// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v4.24.4
// source: pricing/sub_coverages.proto

package ptypes

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LimitCadenceType int32

const (
	LimitCadenceType_LimitCadenceType_Unspecified LimitCadenceType = 0
	LimitCadenceType_LimitCadenceType_Occurrence  LimitCadenceType = 1
	LimitCadenceType_LimitCadenceType_Aggregate   LimitCadenceType = 2
	LimitCadenceType_LimitCadenceType_Monthly     LimitCadenceType = 3
)

// Enum value maps for LimitCadenceType.
var (
	LimitCadenceType_name = map[int32]string{
		0: "LimitCadenceType_Unspecified",
		1: "LimitCadenceType_Occurrence",
		2: "LimitCadenceType_Aggregate",
		3: "LimitCadenceType_Monthly",
	}
	LimitCadenceType_value = map[string]int32{
		"LimitCadenceType_Unspecified": 0,
		"LimitCadenceType_Occurrence":  1,
		"LimitCadenceType_Aggregate":   2,
		"LimitCadenceType_Monthly":     3,
	}
)

func (x LimitCadenceType) Enum() *LimitCadenceType {
	p := new(LimitCadenceType)
	*p = x
	return p
}

func (x LimitCadenceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LimitCadenceType) Descriptor() protoreflect.EnumDescriptor {
	return file_pricing_sub_coverages_proto_enumTypes[0].Descriptor()
}

func (LimitCadenceType) Type() protoreflect.EnumType {
	return &file_pricing_sub_coverages_proto_enumTypes[0]
}

func (x LimitCadenceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LimitCadenceType.Descriptor instead.
func (LimitCadenceType) EnumDescriptor() ([]byte, []int) {
	return file_pricing_sub_coverages_proto_rawDescGZIP(), []int{0}
}

type SubCoverageType int32

const (
	SubCoverageType_SubCoverageType_Unspecified                        SubCoverageType = 0
	SubCoverageType_SubCoverageType_Comprehensive                      SubCoverageType = 1
	SubCoverageType_SubCoverageType_Collision                          SubCoverageType = 2
	SubCoverageType_SubCoverageType_TrailerInterchange                 SubCoverageType = 3
	SubCoverageType_SubCoverageType_NonOwnedTrailer                    SubCoverageType = 4
	SubCoverageType_SubCoverageType_Towing                             SubCoverageType = 5
	SubCoverageType_SubCoverageType_RentalReimbursement                SubCoverageType = 6
	SubCoverageType_SubCoverageType_Cargo                              SubCoverageType = 7
	SubCoverageType_SubCoverageType_ReeferWithoutHumanError            SubCoverageType = 8
	SubCoverageType_SubCoverageType_ReeferWithHumanError               SubCoverageType = 9
	SubCoverageType_SubCoverageType_BodilyInjury                       SubCoverageType = 10
	SubCoverageType_SubCoverageType_PropertyDamage                     SubCoverageType = 11
	SubCoverageType_SubCoverageType_UninsuredMotorist                  SubCoverageType = 12
	SubCoverageType_SubCoverageType_UnderInsuredMotorist               SubCoverageType = 13
	SubCoverageType_SubCoverageType_UMUIM                              SubCoverageType = 14
	SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage    SubCoverageType = 15
	SubCoverageType_SubCoverageType_MedicalPayments                    SubCoverageType = 16
	SubCoverageType_SubCoverageType_PersonalInjuryProtection           SubCoverageType = 17
	SubCoverageType_SubCoverageType_PIPAttendantCare                   SubCoverageType = 18
	SubCoverageType_SubCoverageType_PIPWorkLossAndRPLService           SubCoverageType = 19
	SubCoverageType_SubCoverageType_PropertyProtectionInsurance        SubCoverageType = 20
	SubCoverageType_SubCoverageType_GeneralLiability                   SubCoverageType = 21
	SubCoverageType_SubCoverageType_HiredAuto                          SubCoverageType = 22
	SubCoverageType_SubCoverageType_PollutantCleanupAndRemoval         SubCoverageType = 23
	SubCoverageType_SubCoverageType_LossMitigationExpenses             SubCoverageType = 24
	SubCoverageType_SubCoverageType_MiscellaneousEquipment             SubCoverageType = 25
	SubCoverageType_SubCoverageType_EarnedFreight                      SubCoverageType = 26
	SubCoverageType_SubCoverageType_DebrisRemoval                      SubCoverageType = 27
	SubCoverageType_SubCoverageType_CargoAtScheduledTerminals          SubCoverageType = 28
	SubCoverageType_SubCoverageType_CargoTrailerInterchange            SubCoverageType = 29
	SubCoverageType_SubCoverageType_NonOwnedVehicle                    SubCoverageType = 30
	SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury      SubCoverageType = 31
	SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury   SubCoverageType = 32
	SubCoverageType_SubCoverageType_HiredAutoLiability                 SubCoverageType = 33
	SubCoverageType_SubCoverageType_HiredAutoPhysicalDamage            SubCoverageType = 34
	SubCoverageType_SubCoverageType_MedicalExpenseBenefits             SubCoverageType = 35
	SubCoverageType_SubCoverageType_WorkLossBenefits                   SubCoverageType = 36
	SubCoverageType_SubCoverageType_FuneralExpenseBenefits             SubCoverageType = 37
	SubCoverageType_SubCoverageType_AccidentalDeathBenefits            SubCoverageType = 38
	SubCoverageType_SubCoverageType_ExtraordinaryMedicalBenefits       SubCoverageType = 39
	SubCoverageType_SubCoverageType_UnderinsuredMotoristPropertyDamage SubCoverageType = 40
	SubCoverageType_SubCoverageType_EssentialServiceExpenses           SubCoverageType = 41
	SubCoverageType_SubCoverageType_GuestPersonalInjuryProtection      SubCoverageType = 42
	SubCoverageType_SubCoverageType_BroadenedPollution                 SubCoverageType = 43
	SubCoverageType_SubCoverageType_StopGap                            SubCoverageType = 44
	SubCoverageType_SubCoverageType_ElectronicEquipment                SubCoverageType = 45
)

// Enum value maps for SubCoverageType.
var (
	SubCoverageType_name = map[int32]string{
		0:  "SubCoverageType_Unspecified",
		1:  "SubCoverageType_Comprehensive",
		2:  "SubCoverageType_Collision",
		3:  "SubCoverageType_TrailerInterchange",
		4:  "SubCoverageType_NonOwnedTrailer",
		5:  "SubCoverageType_Towing",
		6:  "SubCoverageType_RentalReimbursement",
		7:  "SubCoverageType_Cargo",
		8:  "SubCoverageType_ReeferWithoutHumanError",
		9:  "SubCoverageType_ReeferWithHumanError",
		10: "SubCoverageType_BodilyInjury",
		11: "SubCoverageType_PropertyDamage",
		12: "SubCoverageType_UninsuredMotorist",
		13: "SubCoverageType_UnderInsuredMotorist",
		14: "SubCoverageType_UMUIM",
		15: "SubCoverageType_UninsuredMotoristPropertyDamage",
		16: "SubCoverageType_MedicalPayments",
		17: "SubCoverageType_PersonalInjuryProtection",
		18: "SubCoverageType_PIPAttendantCare",
		19: "SubCoverageType_PIPWorkLossAndRPLService",
		20: "SubCoverageType_PropertyProtectionInsurance",
		21: "SubCoverageType_GeneralLiability",
		22: "SubCoverageType_HiredAuto",
		23: "SubCoverageType_PollutantCleanupAndRemoval",
		24: "SubCoverageType_LossMitigationExpenses",
		25: "SubCoverageType_MiscellaneousEquipment",
		26: "SubCoverageType_EarnedFreight",
		27: "SubCoverageType_DebrisRemoval",
		28: "SubCoverageType_CargoAtScheduledTerminals",
		29: "SubCoverageType_CargoTrailerInterchange",
		30: "SubCoverageType_NonOwnedVehicle",
		31: "SubCoverageType_UninsuredMotoristBodilyInjury",
		32: "SubCoverageType_UnderinsuredMotoristBodilyInjury",
		33: "SubCoverageType_HiredAutoLiability",
		34: "SubCoverageType_HiredAutoPhysicalDamage",
		35: "SubCoverageType_MedicalExpenseBenefits",
		36: "SubCoverageType_WorkLossBenefits",
		37: "SubCoverageType_FuneralExpenseBenefits",
		38: "SubCoverageType_AccidentalDeathBenefits",
		39: "SubCoverageType_ExtraordinaryMedicalBenefits",
		40: "SubCoverageType_UnderinsuredMotoristPropertyDamage",
		41: "SubCoverageType_EssentialServiceExpenses",
		42: "SubCoverageType_GuestPersonalInjuryProtection",
		43: "SubCoverageType_BroadenedPollution",
		44: "SubCoverageType_StopGap",
		45: "SubCoverageType_ElectronicEquipment",
	}
	SubCoverageType_value = map[string]int32{
		"SubCoverageType_Unspecified":                        0,
		"SubCoverageType_Comprehensive":                      1,
		"SubCoverageType_Collision":                          2,
		"SubCoverageType_TrailerInterchange":                 3,
		"SubCoverageType_NonOwnedTrailer":                    4,
		"SubCoverageType_Towing":                             5,
		"SubCoverageType_RentalReimbursement":                6,
		"SubCoverageType_Cargo":                              7,
		"SubCoverageType_ReeferWithoutHumanError":            8,
		"SubCoverageType_ReeferWithHumanError":               9,
		"SubCoverageType_BodilyInjury":                       10,
		"SubCoverageType_PropertyDamage":                     11,
		"SubCoverageType_UninsuredMotorist":                  12,
		"SubCoverageType_UnderInsuredMotorist":               13,
		"SubCoverageType_UMUIM":                              14,
		"SubCoverageType_UninsuredMotoristPropertyDamage":    15,
		"SubCoverageType_MedicalPayments":                    16,
		"SubCoverageType_PersonalInjuryProtection":           17,
		"SubCoverageType_PIPAttendantCare":                   18,
		"SubCoverageType_PIPWorkLossAndRPLService":           19,
		"SubCoverageType_PropertyProtectionInsurance":        20,
		"SubCoverageType_GeneralLiability":                   21,
		"SubCoverageType_HiredAuto":                          22,
		"SubCoverageType_PollutantCleanupAndRemoval":         23,
		"SubCoverageType_LossMitigationExpenses":             24,
		"SubCoverageType_MiscellaneousEquipment":             25,
		"SubCoverageType_EarnedFreight":                      26,
		"SubCoverageType_DebrisRemoval":                      27,
		"SubCoverageType_CargoAtScheduledTerminals":          28,
		"SubCoverageType_CargoTrailerInterchange":            29,
		"SubCoverageType_NonOwnedVehicle":                    30,
		"SubCoverageType_UninsuredMotoristBodilyInjury":      31,
		"SubCoverageType_UnderinsuredMotoristBodilyInjury":   32,
		"SubCoverageType_HiredAutoLiability":                 33,
		"SubCoverageType_HiredAutoPhysicalDamage":            34,
		"SubCoverageType_MedicalExpenseBenefits":             35,
		"SubCoverageType_WorkLossBenefits":                   36,
		"SubCoverageType_FuneralExpenseBenefits":             37,
		"SubCoverageType_AccidentalDeathBenefits":            38,
		"SubCoverageType_ExtraordinaryMedicalBenefits":       39,
		"SubCoverageType_UnderinsuredMotoristPropertyDamage": 40,
		"SubCoverageType_EssentialServiceExpenses":           41,
		"SubCoverageType_GuestPersonalInjuryProtection":      42,
		"SubCoverageType_BroadenedPollution":                 43,
		"SubCoverageType_StopGap":                            44,
		"SubCoverageType_ElectronicEquipment":                45,
	}
)

func (x SubCoverageType) Enum() *SubCoverageType {
	p := new(SubCoverageType)
	*p = x
	return p
}

func (x SubCoverageType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SubCoverageType) Descriptor() protoreflect.EnumDescriptor {
	return file_pricing_sub_coverages_proto_enumTypes[1].Descriptor()
}

func (SubCoverageType) Type() protoreflect.EnumType {
	return &file_pricing_sub_coverages_proto_enumTypes[1]
}

func (x SubCoverageType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SubCoverageType.Descriptor instead.
func (SubCoverageType) EnumDescriptor() ([]byte, []int) {
	return file_pricing_sub_coverages_proto_rawDescGZIP(), []int{1}
}

type LimitSpec struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SubCoverageGroup *SubCoverageGroup `protobuf:"bytes,1,opt,name=subCoverageGroup,proto3" json:"subCoverageGroup,omitempty"`
	Amount           float64           `protobuf:"fixed64,2,opt,name=amount,proto3" json:"amount,omitempty"`
	Cadence          LimitCadenceType  `protobuf:"varint,3,opt,name=cadence,proto3,enum=pricing.LimitCadenceType" json:"cadence,omitempty"`
	Stacked          bool              `protobuf:"varint,4,opt,name=stacked,proto3" json:"stacked,omitempty"`
	AddedOn          bool              `protobuf:"varint,5,opt,name=addedOn,proto3" json:"addedOn,omitempty"`
}

func (x *LimitSpec) Reset() {
	*x = LimitSpec{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_sub_coverages_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LimitSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LimitSpec) ProtoMessage() {}

func (x *LimitSpec) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_sub_coverages_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LimitSpec.ProtoReflect.Descriptor instead.
func (*LimitSpec) Descriptor() ([]byte, []int) {
	return file_pricing_sub_coverages_proto_rawDescGZIP(), []int{0}
}

func (x *LimitSpec) GetSubCoverageGroup() *SubCoverageGroup {
	if x != nil {
		return x.SubCoverageGroup
	}
	return nil
}

func (x *LimitSpec) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *LimitSpec) GetCadence() LimitCadenceType {
	if x != nil {
		return x.Cadence
	}
	return LimitCadenceType_LimitCadenceType_Unspecified
}

func (x *LimitSpec) GetStacked() bool {
	if x != nil {
		return x.Stacked
	}
	return false
}

func (x *LimitSpec) GetAddedOn() bool {
	if x != nil {
		return x.AddedOn
	}
	return false
}

type DeductibleSpec struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SubCoverageGroup *SubCoverageGroup `protobuf:"bytes,1,opt,name=subCoverageGroup,proto3" json:"subCoverageGroup,omitempty"`
	Amount           float64           `protobuf:"fixed64,2,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (x *DeductibleSpec) Reset() {
	*x = DeductibleSpec{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_sub_coverages_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeductibleSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeductibleSpec) ProtoMessage() {}

func (x *DeductibleSpec) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_sub_coverages_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeductibleSpec.ProtoReflect.Descriptor instead.
func (*DeductibleSpec) Descriptor() ([]byte, []int) {
	return file_pricing_sub_coverages_proto_rawDescGZIP(), []int{1}
}

func (x *DeductibleSpec) GetSubCoverageGroup() *SubCoverageGroup {
	if x != nil {
		return x.SubCoverageGroup
	}
	return nil
}

func (x *DeductibleSpec) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

type CombinedDeductibleSpec struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SubCoverageGroups []*SubCoverageGroup `protobuf:"bytes,1,rep,name=subCoverageGroups,proto3" json:"subCoverageGroups,omitempty"`
}

func (x *CombinedDeductibleSpec) Reset() {
	*x = CombinedDeductibleSpec{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_sub_coverages_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CombinedDeductibleSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CombinedDeductibleSpec) ProtoMessage() {}

func (x *CombinedDeductibleSpec) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_sub_coverages_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CombinedDeductibleSpec.ProtoReflect.Descriptor instead.
func (*CombinedDeductibleSpec) Descriptor() ([]byte, []int) {
	return file_pricing_sub_coverages_proto_rawDescGZIP(), []int{2}
}

func (x *CombinedDeductibleSpec) GetSubCoverageGroups() []*SubCoverageGroup {
	if x != nil {
		return x.SubCoverageGroups
	}
	return nil
}

type SubCoverageGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SubCoverages []SubCoverageType `protobuf:"varint,1,rep,packed,name=subCoverages,proto3,enum=pricing.SubCoverageType" json:"subCoverages,omitempty"`
}

func (x *SubCoverageGroup) Reset() {
	*x = SubCoverageGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pricing_sub_coverages_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubCoverageGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubCoverageGroup) ProtoMessage() {}

func (x *SubCoverageGroup) ProtoReflect() protoreflect.Message {
	mi := &file_pricing_sub_coverages_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubCoverageGroup.ProtoReflect.Descriptor instead.
func (*SubCoverageGroup) Descriptor() ([]byte, []int) {
	return file_pricing_sub_coverages_proto_rawDescGZIP(), []int{3}
}

func (x *SubCoverageGroup) GetSubCoverages() []SubCoverageType {
	if x != nil {
		return x.SubCoverages
	}
	return nil
}

var File_pricing_sub_coverages_proto protoreflect.FileDescriptor

var file_pricing_sub_coverages_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2f, 0x73, 0x75, 0x62, 0x5f, 0x63, 0x6f,
	0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x70,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x22, 0xd3, 0x01, 0x0a, 0x09, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x53, 0x70, 0x65, 0x63, 0x12, 0x45, 0x0a, 0x10, 0x73, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72,
	0x61, 0x67, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65,
	0x72, 0x61, 0x67, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x10, 0x73, 0x75, 0x62, 0x43, 0x6f,
	0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x33, 0x0a, 0x07, 0x63, 0x61, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x43, 0x61, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x07, 0x63, 0x61, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x74, 0x61, 0x63,
	0x6b, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x74, 0x61, 0x63, 0x6b,
	0x65, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x65, 0x64, 0x4f, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x61, 0x64, 0x64, 0x65, 0x64, 0x4f, 0x6e, 0x22, 0x6f, 0x0a, 0x0e,
	0x44, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x62, 0x6c, 0x65, 0x53, 0x70, 0x65, 0x63, 0x12, 0x45,
	0x0a, 0x10, 0x73, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x2e, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x52, 0x10, 0x73, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x61, 0x0a,
	0x16, 0x43, 0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x64, 0x44, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69,
	0x62, 0x6c, 0x65, 0x53, 0x70, 0x65, 0x63, 0x12, 0x47, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x43, 0x6f,
	0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x75, 0x62,
	0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x11, 0x73,
	0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73,
	0x22, 0x50, 0x0a, 0x10, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x12, 0x3c, 0x0a, 0x0c, 0x73, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72,
	0x61, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x73, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67,
	0x65, 0x73, 0x2a, 0x93, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x43, 0x61, 0x64, 0x65,
	0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x1c, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x43, 0x61, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x55, 0x6e, 0x73, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x43, 0x61, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4f, 0x63,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x43, 0x61, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x41,
	0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x10, 0x02, 0x12, 0x1c, 0x0a, 0x18, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x43, 0x61, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4d,
	0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x10, 0x03, 0x2a, 0xed, 0x0e, 0x0a, 0x0f, 0x53, 0x75, 0x62,
	0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x1b,
	0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x10, 0x00, 0x12, 0x21, 0x0a,
	0x1d, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x5f, 0x43, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x68, 0x65, 0x6e, 0x73, 0x69, 0x76, 0x65, 0x10, 0x01,
	0x12, 0x1d, 0x0a, 0x19, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x5f, 0x43, 0x6f, 0x6c, 0x6c, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x10, 0x02, 0x12,
	0x26, 0x0a, 0x22, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x54, 0x72, 0x61, 0x69, 0x6c, 0x65, 0x72, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x10, 0x03, 0x12, 0x23, 0x0a, 0x1f, 0x53, 0x75, 0x62, 0x43, 0x6f,
	0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4e, 0x6f, 0x6e, 0x4f, 0x77,
	0x6e, 0x65, 0x64, 0x54, 0x72, 0x61, 0x69, 0x6c, 0x65, 0x72, 0x10, 0x04, 0x12, 0x1a, 0x0a, 0x16,
	0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x54, 0x6f, 0x77, 0x69, 0x6e, 0x67, 0x10, 0x05, 0x12, 0x27, 0x0a, 0x23, 0x53, 0x75, 0x62, 0x43,
	0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x52, 0x65, 0x6e, 0x74,
	0x61, 0x6c, 0x52, 0x65, 0x69, 0x6d, 0x62, 0x75, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x10,
	0x06, 0x12, 0x19, 0x0a, 0x15, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x5f, 0x43, 0x61, 0x72, 0x67, 0x6f, 0x10, 0x07, 0x12, 0x2b, 0x0a, 0x27,
	0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x52, 0x65, 0x65, 0x66, 0x65, 0x72, 0x57, 0x69, 0x74, 0x68, 0x6f, 0x75, 0x74, 0x48, 0x75, 0x6d,
	0x61, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x08, 0x12, 0x28, 0x0a, 0x24, 0x53, 0x75, 0x62,
	0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x52, 0x65, 0x65,
	0x66, 0x65, 0x72, 0x57, 0x69, 0x74, 0x68, 0x48, 0x75, 0x6d, 0x61, 0x6e, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x10, 0x09, 0x12, 0x20, 0x0a, 0x1c, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61,
	0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x42, 0x6f, 0x64, 0x69, 0x6c, 0x79, 0x49, 0x6e, 0x6a,
	0x75, 0x72, 0x79, 0x10, 0x0a, 0x12, 0x22, 0x0a, 0x1e, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65,
	0x72, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x79, 0x44, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x10, 0x0b, 0x12, 0x25, 0x0a, 0x21, 0x53, 0x75, 0x62,
	0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x55, 0x6e, 0x69,
	0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x4d, 0x6f, 0x74, 0x6f, 0x72, 0x69, 0x73, 0x74, 0x10, 0x0c,
	0x12, 0x28, 0x0a, 0x24, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x5f, 0x55, 0x6e, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64,
	0x4d, 0x6f, 0x74, 0x6f, 0x72, 0x69, 0x73, 0x74, 0x10, 0x0d, 0x12, 0x19, 0x0a, 0x15, 0x53, 0x75,
	0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x55, 0x4d,
	0x55, 0x49, 0x4d, 0x10, 0x0e, 0x12, 0x33, 0x0a, 0x2f, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65,
	0x72, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x55, 0x6e, 0x69, 0x6e, 0x73, 0x75, 0x72,
	0x65, 0x64, 0x4d, 0x6f, 0x74, 0x6f, 0x72, 0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x79, 0x44, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x10, 0x0f, 0x12, 0x23, 0x0a, 0x1f, 0x53, 0x75,
	0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4d, 0x65,
	0x64, 0x69, 0x63, 0x61, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x10, 0x10, 0x12,
	0x2c, 0x0a, 0x28, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x6a, 0x75, 0x72,
	0x79, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x11, 0x12, 0x24, 0x0a,
	0x20, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x5f, 0x50, 0x49, 0x50, 0x41, 0x74, 0x74, 0x65, 0x6e, 0x64, 0x61, 0x6e, 0x74, 0x43, 0x61, 0x72,
	0x65, 0x10, 0x12, 0x12, 0x2c, 0x0a, 0x28, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61,
	0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x50, 0x49, 0x50, 0x57, 0x6f, 0x72, 0x6b, 0x4c, 0x6f,
	0x73, 0x73, 0x41, 0x6e, 0x64, 0x52, 0x50, 0x4c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x10,
	0x13, 0x12, 0x2f, 0x0a, 0x2b, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x5f, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x50, 0x72, 0x6f,
	0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65,
	0x10, 0x14, 0x12, 0x24, 0x0a, 0x20, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x4c, 0x69, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x10, 0x15, 0x12, 0x1d, 0x0a, 0x19, 0x53, 0x75, 0x62, 0x43,
	0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x48, 0x69, 0x72, 0x65,
	0x64, 0x41, 0x75, 0x74, 0x6f, 0x10, 0x16, 0x12, 0x2e, 0x0a, 0x2a, 0x53, 0x75, 0x62, 0x43, 0x6f,
	0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x50, 0x6f, 0x6c, 0x6c, 0x75,
	0x74, 0x61, 0x6e, 0x74, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x75, 0x70, 0x41, 0x6e, 0x64, 0x52, 0x65,
	0x6d, 0x6f, 0x76, 0x61, 0x6c, 0x10, 0x17, 0x12, 0x2a, 0x0a, 0x26, 0x53, 0x75, 0x62, 0x43, 0x6f,
	0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4c, 0x6f, 0x73, 0x73, 0x4d,
	0x69, 0x74, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x70, 0x65, 0x6e, 0x73, 0x65,
	0x73, 0x10, 0x18, 0x12, 0x2a, 0x0a, 0x26, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61,
	0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4d, 0x69, 0x73, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x6e,
	0x65, 0x6f, 0x75, 0x73, 0x45, 0x71, 0x75, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x10, 0x19, 0x12,
	0x21, 0x0a, 0x1d, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x45, 0x61, 0x72, 0x6e, 0x65, 0x64, 0x46, 0x72, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x10, 0x1a, 0x12, 0x21, 0x0a, 0x1d, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x44, 0x65, 0x62, 0x72, 0x69, 0x73, 0x52, 0x65, 0x6d, 0x6f,
	0x76, 0x61, 0x6c, 0x10, 0x1b, 0x12, 0x2d, 0x0a, 0x29, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65,
	0x72, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x43, 0x61, 0x72, 0x67, 0x6f, 0x41, 0x74,
	0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61,
	0x6c, 0x73, 0x10, 0x1c, 0x12, 0x2b, 0x0a, 0x27, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72,
	0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x43, 0x61, 0x72, 0x67, 0x6f, 0x54, 0x72, 0x61,
	0x69, 0x6c, 0x65, 0x72, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x10,
	0x1d, 0x12, 0x23, 0x0a, 0x1f, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x5f, 0x4e, 0x6f, 0x6e, 0x4f, 0x77, 0x6e, 0x65, 0x64, 0x56, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x10, 0x1e, 0x12, 0x31, 0x0a, 0x2d, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76,
	0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x55, 0x6e, 0x69, 0x6e, 0x73, 0x75,
	0x72, 0x65, 0x64, 0x4d, 0x6f, 0x74, 0x6f, 0x72, 0x69, 0x73, 0x74, 0x42, 0x6f, 0x64, 0x69, 0x6c,
	0x79, 0x49, 0x6e, 0x6a, 0x75, 0x72, 0x79, 0x10, 0x1f, 0x12, 0x34, 0x0a, 0x30, 0x53, 0x75, 0x62,
	0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x55, 0x6e, 0x64,
	0x65, 0x72, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x64, 0x4d, 0x6f, 0x74, 0x6f, 0x72, 0x69, 0x73,
	0x74, 0x42, 0x6f, 0x64, 0x69, 0x6c, 0x79, 0x49, 0x6e, 0x6a, 0x75, 0x72, 0x79, 0x10, 0x20, 0x12,
	0x26, 0x0a, 0x22, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x48, 0x69, 0x72, 0x65, 0x64, 0x41, 0x75, 0x74, 0x6f, 0x4c, 0x69, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x10, 0x21, 0x12, 0x2b, 0x0a, 0x27, 0x53, 0x75, 0x62, 0x43, 0x6f,
	0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x48, 0x69, 0x72, 0x65, 0x64,
	0x41, 0x75, 0x74, 0x6f, 0x50, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x44, 0x61, 0x6d, 0x61,
	0x67, 0x65, 0x10, 0x22, 0x12, 0x2a, 0x0a, 0x26, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72,
	0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x6c, 0x45,
	0x78, 0x70, 0x65, 0x6e, 0x73, 0x65, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x10, 0x23,
	0x12, 0x24, 0x0a, 0x20, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x5f, 0x57, 0x6f, 0x72, 0x6b, 0x4c, 0x6f, 0x73, 0x73, 0x42, 0x65, 0x6e, 0x65,
	0x66, 0x69, 0x74, 0x73, 0x10, 0x24, 0x12, 0x2a, 0x0a, 0x26, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76,
	0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x46, 0x75, 0x6e, 0x65, 0x72, 0x61,
	0x6c, 0x45, 0x78, 0x70, 0x65, 0x6e, 0x73, 0x65, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73,
	0x10, 0x25, 0x12, 0x2b, 0x0a, 0x27, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x41, 0x63, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x61, 0x6c,
	0x44, 0x65, 0x61, 0x74, 0x68, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x10, 0x26, 0x12,
	0x30, 0x0a, 0x2c, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x45, 0x78, 0x74, 0x72, 0x61, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x72, 0x79,
	0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x6c, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x10,
	0x27, 0x12, 0x36, 0x0a, 0x32, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x5f, 0x55, 0x6e, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x65,
	0x64, 0x4d, 0x6f, 0x74, 0x6f, 0x72, 0x69, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x79, 0x44, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x10, 0x28, 0x12, 0x2c, 0x0a, 0x28, 0x53, 0x75, 0x62,
	0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x45, 0x73, 0x73,
	0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x78, 0x70,
	0x65, 0x6e, 0x73, 0x65, 0x73, 0x10, 0x29, 0x12, 0x31, 0x0a, 0x2d, 0x53, 0x75, 0x62, 0x43, 0x6f,
	0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x47, 0x75, 0x65, 0x73, 0x74,
	0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x6a, 0x75, 0x72, 0x79, 0x50, 0x72,
	0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x2a, 0x12, 0x26, 0x0a, 0x22, 0x53, 0x75,
	0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x42, 0x72,
	0x6f, 0x61, 0x64, 0x65, 0x6e, 0x65, 0x64, 0x50, 0x6f, 0x6c, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e,
	0x10, 0x2b, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x53, 0x74, 0x6f, 0x70, 0x47, 0x61, 0x70, 0x10, 0x2c, 0x12,
	0x27, 0x0a, 0x23, 0x53, 0x75, 0x62, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x45, 0x6c, 0x65, 0x63, 0x74, 0x72, 0x6f, 0x6e, 0x69, 0x63, 0x45, 0x71, 0x75,
	0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x10, 0x2d, 0x42, 0x33, 0x5a, 0x31, 0x6e, 0x69, 0x72, 0x76,
	0x61, 0x6e, 0x61, 0x74, 0x65, 0x63, 0x68, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6e, 0x69, 0x72, 0x76,
	0x61, 0x6e, 0x61, 0x2f, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x74, 0x79, 0x70, 0x65, 0x73, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pricing_sub_coverages_proto_rawDescOnce sync.Once
	file_pricing_sub_coverages_proto_rawDescData = file_pricing_sub_coverages_proto_rawDesc
)

func file_pricing_sub_coverages_proto_rawDescGZIP() []byte {
	file_pricing_sub_coverages_proto_rawDescOnce.Do(func() {
		file_pricing_sub_coverages_proto_rawDescData = protoimpl.X.CompressGZIP(file_pricing_sub_coverages_proto_rawDescData)
	})
	return file_pricing_sub_coverages_proto_rawDescData
}

var file_pricing_sub_coverages_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_pricing_sub_coverages_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_pricing_sub_coverages_proto_goTypes = []interface{}{
	(LimitCadenceType)(0),          // 0: pricing.LimitCadenceType
	(SubCoverageType)(0),           // 1: pricing.SubCoverageType
	(*LimitSpec)(nil),              // 2: pricing.LimitSpec
	(*DeductibleSpec)(nil),         // 3: pricing.DeductibleSpec
	(*CombinedDeductibleSpec)(nil), // 4: pricing.CombinedDeductibleSpec
	(*SubCoverageGroup)(nil),       // 5: pricing.SubCoverageGroup
}
var file_pricing_sub_coverages_proto_depIdxs = []int32{
	5, // 0: pricing.LimitSpec.subCoverageGroup:type_name -> pricing.SubCoverageGroup
	0, // 1: pricing.LimitSpec.cadence:type_name -> pricing.LimitCadenceType
	5, // 2: pricing.DeductibleSpec.subCoverageGroup:type_name -> pricing.SubCoverageGroup
	5, // 3: pricing.CombinedDeductibleSpec.subCoverageGroups:type_name -> pricing.SubCoverageGroup
	1, // 4: pricing.SubCoverageGroup.subCoverages:type_name -> pricing.SubCoverageType
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_pricing_sub_coverages_proto_init() }
func file_pricing_sub_coverages_proto_init() {
	if File_pricing_sub_coverages_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pricing_sub_coverages_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LimitSpec); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_sub_coverages_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeductibleSpec); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_sub_coverages_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CombinedDeductibleSpec); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pricing_sub_coverages_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubCoverageGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pricing_sub_coverages_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pricing_sub_coverages_proto_goTypes,
		DependencyIndexes: file_pricing_sub_coverages_proto_depIdxs,
		EnumInfos:         file_pricing_sub_coverages_proto_enumTypes,
		MessageInfos:      file_pricing_sub_coverages_proto_msgTypes,
	}.Build()
	File_pricing_sub_coverages_proto = out.File
	file_pricing_sub_coverages_proto_rawDesc = nil
	file_pricing_sub_coverages_proto_goTypes = nil
	file_pricing_sub_coverages_proto_depIdxs = nil
}
