load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "utils",
    srcs = ["dates.go"],
    importpath = "nirvanatech.com/nirvana/rating/pricing/api/utils",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/proto",
        "//nirvana/common-go/time_utils",
        "@com_github_shopspring_decimal//:decimal",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)
