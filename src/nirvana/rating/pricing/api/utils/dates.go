package utils

import (
	"time"

	"github.com/shopspring/decimal"
	"google.golang.org/protobuf/types/known/timestamppb"

	common_proto "nirvanatech.com/nirvana/common-go/proto"
	"nirvanatech.com/nirvana/common-go/time_utils"
)

// CalculateDaysDuration returns the number of days between the start and
// end dates, without considering the end date. This follows the same semantics
// as a chunk's dates, and of a policy's dates.
//
// It removes the time part of the datetime object, before calculating the
// difference (in days) between them.
func CalculateDaysDuration(dates *common_proto.Interval) decimal.Decimal {
	startDate := truncateDatetime(dates.Start)
	endDate := truncateDatetime(dates.End)
	durationInNanoSeconds := int64(endDate.Sub(startDate))
	durationInDays := durationInNanoSeconds / (1e9 * 3600 * 24)
	return decimal.NewFromInt(durationInDays)
}

// truncateDatetime removes the time part of the datetime object.
func truncateDatetime(t *timestamppb.Timestamp) time.Time {
	return time_utils.DateFromTime(t.AsTime()).ToTime()
}
