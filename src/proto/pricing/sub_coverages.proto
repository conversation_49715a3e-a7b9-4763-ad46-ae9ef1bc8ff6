syntax = "proto3";
package pricing;

option go_package = "nirvanatech.com/nirvana/rating/pricing/api/ptypes";

// Note that within a LimitSpec there's a SubCoverageGroup.
// The group is a list of sub-coverages for which the limit
// applies. If the number of sub-coverages in the group is
// only one, the limit is an individual limit. Otherwise it's
// known as a grouped limit. Do not confuse with the concept
// of "combined" limit, which is different.
message LimitSpec {
	SubCoverageGroup subCoverageGroup = 1;
	double amount = 2;
	LimitCadenceType cadence = 3;
	bool stacked = 4;
	bool addedOn = 5;
}

enum LimitCadenceType {
	LimitCadenceType_Unspecified = 0;
	LimitCadenceType_Occurrence = 1;
	LimitCadenceType_Aggregate = 2;
	LimitCadenceType_Monthly = 3;
}

// Note that within a DeductibleSpec there's a SubCoverageGroup.
// The group is a list of sub-coverages for which the deductible
// applies. If the number of sub-coverages in the group is only
// one, the deductible is an individual deductible. Otherwise it's
// known as a grouped deductible. Do not confuse with the concept
// of "combined" deductible, which is different.
message DeductibleSpec {
	SubCoverageGroup subCoverageGroup = 1;
	double amount = 2;
}

// The definition of "combined" is explained here:
// https://nirvana-tech.slack.com/archives/C075LSF92G0/p1726773575775709?thread_ts=1726752920.791909&cid=C075LSF92G0
message CombinedDeductibleSpec {
	repeated SubCoverageGroup subCoverageGroups = 1;
}

// A SubCoverageGroup is a list of sub-coverages
// for which we can define things like limits,
// deductibles, charges, schedule mods, etc.
message SubCoverageGroup {
	repeated SubCoverageType subCoverages = 1;
}

enum SubCoverageType {
	SubCoverageType_Unspecified = 0;
	SubCoverageType_Comprehensive = 1; // Comp
	SubCoverageType_Collision = 2; // Coll
	SubCoverageType_TrailerInterchange = 3; // TI
	SubCoverageType_NonOwnedTrailer = 4; // NOT
	SubCoverageType_Towing = 5;
	SubCoverageType_RentalReimbursement = 6;
	SubCoverageType_Cargo = 7;
	SubCoverageType_ReeferWithoutHumanError = 8;
	SubCoverageType_ReeferWithHumanError = 9;
	SubCoverageType_BodilyInjury = 10; // BI
	SubCoverageType_PropertyDamage = 11; // PD
	SubCoverageType_UninsuredMotorist = 12; // UM
	SubCoverageType_UnderInsuredMotorist = 13; // UIM (note: misspelled, should be "UnderinsuredMotorist")
	SubCoverageType_UMUIM = 14;
	SubCoverageType_UninsuredMotoristPropertyDamage = 15; // UMPD
	SubCoverageType_MedicalPayments = 16; // MedPay
	SubCoverageType_PersonalInjuryProtection = 17; // PIP
	SubCoverageType_PIPAttendantCare = 18;
	SubCoverageType_PIPWorkLossAndRPLService = 19;
	SubCoverageType_PropertyProtectionInsurance = 20; // PPI
	SubCoverageType_GeneralLiability = 21; // GL
	SubCoverageType_HiredAuto = 22; // HA
	SubCoverageType_PollutantCleanupAndRemoval = 23; 
	SubCoverageType_LossMitigationExpenses = 24;
	SubCoverageType_MiscellaneousEquipment = 25;
	SubCoverageType_EarnedFreight = 26;
	SubCoverageType_DebrisRemoval = 27;
	SubCoverageType_CargoAtScheduledTerminals = 28;
	SubCoverageType_CargoTrailerInterchange = 29;
	SubCoverageType_NonOwnedVehicle = 30;
	SubCoverageType_UninsuredMotoristBodilyInjury = 31; // UMBI
	SubCoverageType_UnderinsuredMotoristBodilyInjury = 32; // UIMBI
	SubCoverageType_HiredAutoLiability = 33;
	SubCoverageType_HiredAutoPhysicalDamage = 34;
	SubCoverageType_MedicalExpenseBenefits = 35;
	SubCoverageType_WorkLossBenefits = 36;
	SubCoverageType_FuneralExpenseBenefits = 37;
	SubCoverageType_AccidentalDeathBenefits = 38;
	SubCoverageType_ExtraordinaryMedicalBenefits = 39;
	SubCoverageType_UnderinsuredMotoristPropertyDamage = 40; // UIMPD
	SubCoverageType_EssentialServiceExpenses = 41;
  SubCoverageType_GuestPersonalInjuryProtection = 42; // GuestPIP
  SubCoverageType_BroadenedPollution = 43;
  SubCoverageType_StopGap = 44;
  SubCoverageType_ElectronicEquipment = 45;
}
