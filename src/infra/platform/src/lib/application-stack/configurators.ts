import { Statement } from 'iam-floyd'

import { DatabaseInfo } from '@nvinfra/common/interfaces'

import { HclEcsTaskDefinitionConfig } from '../../../generated/modules/hcl-ecs-task-definition'

/***** Service Configuration *****/

export interface ServiceDeploymentConfig {
  /**
   * (Optional) The number of instances of the task definition to place and keep running.
   * @default 1
   */
  desiredCount?: number
  /**
   * (Optional) The upper limit (as a percentage of the service's desiredCount) of the number of running tasks that can be running in a service during a deployment.
   * @default 200
   */
  deploymentMaximumPercent?: number
  /**
   * (Optional) The lower limit (as a percentage of the service's desiredCount) of the number of running tasks that must remain running and healthy in a service during a deployment.
   * @default 100
   */
  deploymentMinimumHealthyPercent?: number

  /**
   * (Optional) If this service needs to expose an HTTP endpoint,
   * we also need to 1) configure a load balancer, and 2) add a
   * Route53 A record pointing to the load balancer.
   */
  http?: {
    /**
     * The fully qualified domain name for this service.
     *
     * This must be a subdomain of the default route53 zone for the environment
     * that is provided in the `dns` property of `ApplicationStackProps`.
     *
     * For example, if the default route53 zone is `prod.nirvanatech.com`, then
     * the endpoint would be `foo.prod.nirvanatech.com` for a `foo` service.
     */
    endpoint: string
    /**
     * Configuration for the load balancer for the service.
     */
    loadBalancer: ServiceLoadBalancerConfig
  }
}

export interface ServiceHealthCheckConfig {
  /**
   * The approximate amount of time, in seconds, between health checks of an individual target.
   * Must be between 5-300 seconds.
   */
  interval: number
  /**
   * The path to use for the listener.
   */
  path: string
  /**
   * The amount of time, in seconds, during which no response from a target is considered a failed health check.
   * Must be between 2-120 seconds.
   */
  timeout: number
  /**
   * The number of consecutive health checks successes required before considering an unhealthy target healthy.
   * Must be between 2-10.
   */
  healthyThreshold: number
  /**
   * The number of consecutive health check failures required before considering the target unhealthy.
   * Must be between 2-10.
   */
  unhealthyThreshold: number
  /**
   * The HTTP codes to use when checking for a successful response from a target.
   * You can specify multiple values (for example, "200,202") or a range of values (for example, "200-299").
   * Default is 200.
   */
  matcher: string
}

export interface ServiceLoadBalancerConfig {
  /**
   * Name prefix for resources on AWS. Note that this can be different from
   * the namePrefix of the service.
   */
  namePrefix: string
  /**
   * Target group configuration
   */
  targetGroup: {
    /**
     * Health check configuration
     */
    healthCheck: ServiceHealthCheckConfig
    /**
     * (Optional) The amount of time for Elastic Load Balancing to wait before changing the state of a deregistering target from draining to unused.
     * Must be between 0-3600 seconds.
     */
    deregistrationDelay?: number
    /**
     * (Optional) Stickiness configuration
     */
    stickiness?: {
      type: 'lb_cookie' | 'app_cookie'
      cookieDuration: number
      cookieName: string
    }
  }
}

/**
 * Configuration for a Fargate service.
 */
export interface FargateAppServiceConfig<Clusters extends string> {
  /**
   * Name prefix for resources on AWS
   */
  namePrefix: string

  /**
   * Symbolic name for the cluster to deploy the service to.
   * Eg: `app`, `internal_tools`, `jobber_singletons`, etc.
   */
  cluster: Clusters

  /**
   * The service discovery namespace id to create the service_registry. We don't strictly need this for some of our services, but we do it for future proofing. It doesn't hurt to have DNS names for our services.
   */
  useServiceDiscoveryNamespaceOf: Clusters

  /**
   * Deployment configuration for the service.
   */
  deployment: ServiceDeploymentConfig

  /**
   * Tags to apply to the resources created for this service. Note that:
   * - this is over and above the tags automatically set on all resources for
   *   being part of a given stack.
   * - the `Application` tag is automatically added to all resources.
   */
  tags: {
    Application: string
    [key: string]: string
  }
}

/***** Task Definition Configuration *****/

/**
 * Represents a configurator function for a Fargate task definition.
 */
export type FargateAppTaskDefConfigurator<D extends TaskDefDeps> = (
  props: FargateAppTaskDefConfiguratorProps<D>,
) => FargateAppTaskDefConfig

/**
 * Represents a port mapping for a task definition.
 *
 * For example, if you want to expose port 8080 from the container as port
 * 8080 on the host, you would use:
 *  - containerPort: 8080
 *  - hostPort: 8080
 *  - protocol: 'tcp'
 */
export interface TaskDefPortMapping {
  containerPort: number
  hostPort: number
  protocol: 'tcp' | 'udp'
}

/**
 * Configuration for a Fargate task definition.
 */
export interface FargateAppTaskDefConfig {
  // Leave this undefined if you want to create a variable for the image tag
  // so that the image tag can be set at deploy time
  imageTag?: string

  taskRole?: {
    name: string
    policyStatements: Statement.All[]
  }
  taskDefinition: Omit<
    HclEcsTaskDefinitionConfig,
    | 'containerImage'
    | 'taskRoleArn'
    | 'taskExecutionRoleArn'
    | 'portMappings'
    | 'logConfiguration'
  > & {
    portMappings: {
      app: TaskDefPortMapping
      pprof?: TaskDefPortMapping
    }
    logConfiguration: {
      options: {
        awslogsStreamPrefix: string
      }
    }
  }
}

/**
 * Defines the resource types available for injection into Fargate task definitions.
 *
 * This registry maps resource category names to their corresponding type definitions.
 * Task configurators use these categories to request specific resources they need.
 *
 * @example
 * // In configurator function:
 * function apiServerTaskDefConfig(
 *   props: FargateAppTaskDefConfiguratorProps<{
 *     s3Buckets: 'GoServiceProfiles',
 *     databases: 'applicationDB'
 *   }>
 * ) {
 *   // Access the resources:
 *   const bucketName = props.s3Buckets.GoServiceProfiles.name;
 *   const dbHost = props.databases.applicationDB.host;
 * }
 *
 * @property s3Buckets - S3 bucket resources with name properties
 * @property databases - Database connection information including host, port, credentials
 * @property secrets - Secret resources with ARN and value properties for secure values
 * @property kmsKeys - Customer-managed KMS keys exposed by the KMS stack; each key provides
 *   its `arn`
 */
export type InputTypes = {
  s3Buckets: { name: string }
  databases: DatabaseInfo
  secrets: { arn: string; value: string }
  kmsKeys: { arn: string }
}

/**
 * Specifies which specific resources a task definition requires from each category.
 *
 * This type lets configurators declare their dependencies using string identifiers.
 * Each property matches a resource category in InputTypes, and its value is
 * a string or union of strings identifying specific resources needed.
 *
 * All properties are optional, so configurators only need to specify
 * the resource categories they actually use.
 *
 * @example
 * // Declaring dependencies for a task definition:
 * type MyTaskDeps = {
 *   s3Buckets: 'GoServiceProfiles' | 'PdfgenServer',
 *   databases: 'applicationDB' | 'fmcsaDB',
 *   kmsKeys: 'UserSsn' | 'TelematicsApiKeyCredentials'
 * }
 *
 * // These dependencies are then resolved at deployment time:
 * taskDefConfiguratorProps: {
 *   awsAccountId: '************',
 *   s3Buckets: {
 *     GoServiceProfiles: { name: 'nirvana-go-service-profiles' },
 *     PdfgenServer: { name: 'nirvana-pdfgen' }
 *   },
 *   databases: {
 *     applicationDB: { host: 'db.example.com', port: 5432, ... },
 *     fmcsaDB: { host: 'fmcsa-db.example.com', port: 5432, ... }
 *   },
 *   kmsKeys: {
 *     UserSsn: { arn: 'arn:aws:kms:us-east-2:************:key/aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee' },
 *     TelematicsApiKeyCredentials: { arn: 'arn:aws:kms:us-east-2:************:key/ffffffff-1111-2222-3333-************' }
 *   }
 * }
 */
export type TaskDefDeps = {
  [K in keyof InputTypes]?: string
}

/**
 * Properties passed to the configurator function for a Fargate task definition.
 *
 * This type transforms a dependency specification into a structured props object
 * that provides type-safe access to required resources. For each requested
 * dependency, it creates a property with the requested resources available
 * as properly typed objects.
 *
 * It also includes the AWS account ID which is needed for creating IAM policy
 * statements that reference resources in other accounts.
 *
 * @example
 * // If TaskDefDeps is:
 * // {
 * //   s3Buckets: "GoServiceProfiles",
 * //   databases: "applicationDB" | "fmcsaDB",
 * //   kmsKeys: "UserSsn" | "TelematicsApiKeyCredentials"
 * // }
 * //
 * // Then FargateAppTaskDefConfiguratorProps will be:
 * // {
 * //   awsAccountId: "************",
 * //   s3Buckets: {
 * //     GoServiceProfiles: { name: string }
 * //   },
 * //   databases: {
 * //     applicationDB: DatabaseInfo,
 * //     fmcsaDB: DatabaseInfo
 * //   },
 * //   kmsKeys: {
 * //     UserSsn: { arn: string },
 * //     TelematicsApiKeyCredentials: { arn: string }
 * //   }
 * // }
 *
 * @template D - A type extending TaskDefDeps, specifying resource identifiers
 */
export type FargateAppTaskDefConfiguratorProps<D extends TaskDefDeps> = {
  [K in keyof D]: K extends keyof InputTypes
    ? D[K] extends string
      ? Record<D[K], InputTypes[K]>
      : never
    : never
} & {
  /**
   * Account ID of the AWS account this task definition is being created in.
   *
   * This is needed for job processor policy statements that need to access
   * resources in other accounts.
   */
  awsAccountId: string
}

export interface FargateAppConfig<
  Clusters extends string,
  Deps extends TaskDefDeps,
> {
  /**
   * The service configuration for the application.
   */
  service: FargateAppServiceConfig<Clusters>

  /**
   * Logging configuration for the application.
   */
  logging: {
    logGroup: {
      name: string
      tags: {
        Environment: string
        Application: string
      }
    }
  }
  /**
   * Configurator function to get the task definition
   * configuration for the application.
   */
  taskDef: FargateAppTaskDefConfigurator<Deps>
}
