import { Environment } from '@nvinfra/common'
import { AWSAccount } from '@nvinfra/common/constants'
import { NvKmsKeyStackProps } from '@nvinfra/common/constructs/stacks'

import { KmsKeys } from '../supported-kms-keys'

export const stagingKmsKeyConfig: NvKmsKeyStackProps<KmsKeys> = {
  group: 'backend',
  region: 'us-east-2',
  environment: Environment.Staging,
  additionalProviderProps: {
    aws: {
      allowedAccountIds: [AWSAccount.Staging.Id],
      assumeRole: [
        {
          roleArn: AWSAccount.Staging.Role.Administrator,
        },
      ],
    },
  },
  keys: {
    FmcsaCredentials: {
      keyname: 'default-fmcsa-credentials',
      description: 'The key used to encrypt/decrypt FMCSA DOT `PIN`',
    },
    TelematicsApiKeyCredentials: {
      keyname: 'default-telematics-api-key-credentials',
      description:
        'The key used to encrypt/decrypt `telematics.api_key_credentials`',
    },
    TerminalUserCredentials: {
      keyname: 'default-terminal-user-credentials',
      description:
        'The key used to encrypt/decrypt user credentials for Terminal (withterminal.com)',
    },
    TruckerCloudUserCredentials: {
      keyname: 'default-truckercloud-user-credentials',
      description:
        'The key used to encrypt/decrypt TruckerCloud `user_credentials`',
    },
    TspWebhookSecretKeysCredentials: {
      keyname: 'default-tsp-webhook-secret-keys-credentials',
      description: 'The key used to encrypt/decrypt TSP webhook secret keys',
    },
    TelematicsConsentUserRequestMetadata: {
      keyname: 'default-telematics-consent-user-request-metadata',
      description:
        'The key used to encrypt/decrypt telematics consent user request metadata',
    },
    UserSsn: {
      keyname: 'default-user-ssn',
      description: 'The key used to encrypt/decrypt User SSN',
    },
    UserSsnLastFour: {
      keyname: 'default-user-ssn-last-four',
      description:
        'The key used to encrypt/decrypt User SSN last four characters',
    },
  },
}
