import { App } from 'cdktf'

import { AWSAccount } from '@nvinfra/common/constants'
import { EcsClusterWithDNS } from '@nvinfra/common/constructs'
import {
  ECRStack,
  NvBucketStack,
  NvKmsKeyStack,
} from '@nvinfra/common/constructs/stacks'

import { ECRRepoConfigurations } from './ecr.config'
import { addAirbyteStackToApp } from './src/airbyte'
import { AltDomainsStack } from './src/alt-domains'
import { AccountEssentialsStack } from './src/app-stacks/account-essentials-stack'
import {
  prodLaunchPadCfg,
  stagingApplicationStackConfig,
  stagingBucketConfig,
  stagingKmsKeyConfig,
  stagingLaunchPadCfg,
} from './src/app-stacks/config'
import { createLaunchPadStacks } from './src/app-stacks/launchpad'
import { addAthensToApp } from './src/athens'
import { ApplicationStack } from './src/lib/application-stack'
import { addLLMInfraToApp } from './src/llm-agents'
import { graftMetaflowEnvironmentsToApp } from './src/metaflow'
import { addMLStacksToApp } from './src/ml'
import { graftNirvanaMQEnvironmentsToApp } from './src/nirvanamq'
import { addRunsOnStacksToApp } from './src/runs-on'
import { addSnowflakeStacksToApp } from './src/snowflake'
import { addSparkStacks } from './src/spark'

const app = new App()

const ecrStack = new ECRStack(app, {
  group: 'coreinfra',
  region: 'us-east-2',
  repoConfigurations: ECRRepoConfigurations,
  hostAccount: {
    id: AWSAccount.Management.Id,
  },
  upstreamRegistries: {
    DockerHub: {
      ecrRepositoryPrefix: 'pullthrough/docker-hub',
      upstreamRegistryUrl: 'registry-1.docker.io',
      secretName: 'ecr-pullthroughcache/docker-hub',
      enableCrossAccountAccessFor: ['staging'],
    },
    Github: {
      ecrRepositoryPrefix: 'pullthrough/github',
      upstreamRegistryUrl: 'ghcr.io',
      secretName: 'ecr-pullthroughcache/ghcr.io',
    },
    PublicECR: {
      // No secret needed for ECR.
      ecrRepositoryPrefix: 'pullthrough/public-ecr',
      upstreamRegistryUrl: 'public.ecr.aws',
    },
  },
  crossAccountAccessConfig: {
    staging: {
      accountId: AWSAccount.Staging.Id,
      ecsTaskExecutionRoleArn: AccountEssentialsStack.ecsTaskExecutionRoleArn(
        AWSAccount.Staging.Id,
      ),
      // TODO: This is too broad, create a dedicated role for this.
      lambdaDeploymentRoleArn: AWSAccount.Staging.Role.Administrator,
    },
  },
})

new AltDomainsStack(app, {
  group: 'coreinfra',
  region: 'us-east-2',
  destination: new URL('https://www.nirvanatech.com'),
  domains: [
    'nirvanainsurance.com',
    'nirvana-insurance.com',
    'insurenirvana.com',
  ],
  additionalProviderProps: {
    aws: {
      allowedAccountIds: [AWSAccount.Management.Id],
    },
  },
})

const launchPadStacks = {
  prod: createLaunchPadStacks(app, ecrStack.repositories, prodLaunchPadCfg),
  staging: createLaunchPadStacks(
    app,
    ecrStack.repositories,
    stagingLaunchPadCfg,
  ),
}

const stagingS3Buckets = new NvBucketStack(
  app,
  'env-scoped',
  stagingBucketConfig,
)

const stagingKMSKeys = new NvKmsKeyStack(app, 'kms-keys', stagingKmsKeyConfig)

// Only staging for now.
// TODO: All services right now also set an "Environment" tag for backwards compatibility.
// We should be able to remove that since every environment gets its own AWS account.
new ApplicationStack(
  app,
  stagingApplicationStackConfig(
    ecrStack.repositories,
    stagingS3Buckets.buckets,
    launchPadStacks.staging,
    stagingKMSKeys.keys,
  ),
)

/**
 * This is needed because although metaflow & nirvanaMQ services
 * use the `internal_tools` cluster, they have been using the
 * DNS namespace of the `app` cluster.
 */
const internalToolsClusterWithAppDNSInfo: EcsClusterWithDNS = {
  ...launchPadStacks.prod.containerCompute.clusters.internal_tools,
  privateDNSNamespace:
    launchPadStacks.prod.containerCompute.clusters.app.privateDNSNamespace,
}

graftNirvanaMQEnvironmentsToApp(
  app,
  launchPadStacks.prod.network,
  internalToolsClusterWithAppDNSInfo,
)

graftMetaflowEnvironmentsToApp(
  app,
  launchPadStacks.prod.network,
  internalToolsClusterWithAppDNSInfo,
  launchPadStacks.prod.rds.applicationDB.Info(),
)

addMLStacksToApp(app, {
  production: launchPadStacks.prod.network,
})

addSnowflakeStacksToApp(app)

addAirbyteStackToApp(app, launchPadStacks.prod.network)

const runsOnStack = addRunsOnStacksToApp(app)

addAthensToApp(
  app,
  runsOnStack.network,
  runsOnStack.ecs,
  'nirvana-athens-storage',
)

addLLMInfraToApp(app, launchPadStacks.prod.network)

addSparkStacks(app, launchPadStacks.prod.network)

app.synth()
