{"//": {"metadata": {"backend": "s3", "overrides": {"stack": ["terraform"]}, "stackName": "default-staging-backend-application-stack-us-east-2", "version": "0.20.10"}, "outputs": {"default-staging-backend-application-stack-us-east-2": {"outputs": {"api_server_tag": "api_server_tag", "distsem_server_tag": "distsem_server_tag", "event_job_processor_tag": "event_job_processor_tag", "feature_store_server_tag": "feature_store_server_tag", "graphql_server_tag": "graphql_server_tag", "job_processor_tag": "job_processor_tag", "jobber_monitor_tag": "jobber_monitor_tag", "pdfgen_server_tag": "pdfgen_server_tag", "quoting_job_processor_tag": "quoting_job_processor_tag", "safety_job_processor_tag": "safety_job_processor_tag"}}}}, "data": {"aws_iam_policy_document": {"ecs-tasks-assume-role-policy": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-tasks-assume-role-policy", "uniqueId": "ecs-tasks-assume-role-policy"}}, "statement": [{"actions": ["sts:<PERSON><PERSON>Role"], "principals": [{"identifiers": ["ecs-tasks.amazonaws.com"], "type": "Service"}]}]}}, "aws_secretsmanager_secret": {"secret-LaunchDarklyApiKey": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/secret-LaunchDarklyApiKey", "uniqueId": "secret-LaunchDarklyApi<PERSON><PERSON>"}}, "name": "launchdarkly-api-key"}}, "aws_secretsmanager_secret_version": {"secret-version-LaunchDarklyApiKey": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/secret-version-LaunchDarklyApiKey", "uniqueId": "secret-version-LaunchDarklyApiKey"}}, "secret_id": "${data.aws_secretsmanager_secret.secret-LaunchDarklyApiKey.id}"}}, "terraform_remote_state": {"cross-stack-reference-input-default-root-coreinfra-ecr-us-east-2": {"backend": "s3", "config": {"bucket": "cloud.nirvanatech.com", "key": "private/deployment/cdktf/default/root/default-root-coreinfra-ecr-us-east-2.json", "region": "us-east-2"}, "workspace": "${terraform.workspace}"}, "cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2": {"backend": "s3", "config": {"bucket": "cloud.nirvanatech.com", "key": "private/deployment/cdktf/default/staging/default-staging-backend-env-scoped-buckets-us-east-2.json", "region": "us-east-2"}, "workspace": "${terraform.workspace}"}, "cross-stack-reference-input-default-staging-backend-kms-keys-us-east-2": {"backend": "s3", "config": {"bucket": "cloud.nirvanatech.com", "key": "private/deployment/cdktf/default/staging/default-staging-backend-kms-keys-us-east-2.json", "region": "us-east-2"}, "workspace": "${terraform.workspace}"}, "cross-stack-reference-input-default-staging-coreinfra-account-essentials": {"backend": "s3", "config": {"bucket": "cloud.nirvanatech.com", "key": "private/deployment/cdktf/default/staging/default-staging-coreinfra-account-essentials.json", "region": "us-east-2"}, "workspace": "${terraform.workspace}"}, "cross-stack-reference-input-default-staging-coreinfra-container-compute-us-east-2": {"backend": "s3", "config": {"bucket": "cloud.nirvanatech.com", "key": "private/deployment/cdktf/default/staging/default-staging-coreinfra-container-compute-us-east-2.json", "region": "us-east-2"}, "workspace": "${terraform.workspace}"}, "cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2": {"backend": "s3", "config": {"bucket": "cloud.nirvanatech.com", "key": "private/deployment/cdktf/default/staging/default-staging-coreinfra-legacy-network-us-east-2.json", "region": "us-east-2"}, "workspace": "${terraform.workspace}"}, "cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2": {"backend": "s3", "config": {"bucket": "cloud.nirvanatech.com", "key": "private/deployment/cdktf/default/staging/default-staging-storage-rds-stack-us-east-2.json", "region": "us-east-2"}, "workspace": "${terraform.workspace}"}}}, "module": {"ecs-app-ApiServer_load-balancer_56066FC1": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-ApiServer/load-balancer", "uniqueId": "ecs-app-ApiServer_load-balancer_56066FC1"}}, "default_certificate_arn": "${module.ssl-certificate.arn}", "http_ports": {"force_https": {"host": "#{host}", "listener_port": 80, "path": "/#{path}", "protocol": "HTTPS", "query": "#{query}", "status_code": "HTTP_301", "target_port": "443"}}, "https_ports": {"force_https": {"listener_port": 443}}, "load_balancing_algorithm_type": "round_robin", "name_prefix": "staging-api-server", "public_subnets": ["${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_subnetpublic_subnet_aid}", "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_subnetpublic_subnet_bid}", "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_subnetpublic_subnet_cid}"], "security_groups": ["${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_security_groupdefault_sgid}"], "source": "./assets/__cdktf_module_asset_26CE565C/5395951CF06B36840EF6A6BB5B657A3C/nv_svc_alb", "ssl_policy": "ELBSecurityPolicy-2016-08", "target_group_health_check_enabled": true, "target_group_health_check_healthy_threshold": 3, "target_group_health_check_interval": 30, "target_group_health_check_matcher": "200,301,302", "target_group_health_check_path": "/health", "target_group_health_check_timeout": 25, "target_group_health_check_unhealthy_threshold": 3, "vpc_id": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_vpcdefault_vpcid}"}, "ecs-app-ApiServer_service_250C06D0": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-ApiServer/service", "uniqueId": "ecs-app-ApiServer_service_250C06D0"}}, "container_name": "api_server", "container_port": 8080, "create_lb_access_sg_rule": true, "desired_count": 1, "ecs_cluster_arn": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-container-compute-us-east-2.outputs.cross-stack-output-aws_ecs_clustermanaged-cluster-app_ecs_11CC569Carn}", "ecs_cluster_name": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-container-compute-us-east-2.outputs.cross-stack-output-aws_ecs_clustermanaged-cluster-app_ecs_11CC569Cname}", "lb_access_sg": "${module.ecs-app-ApiServer_load-balancer_56066FC1.access_sg_id}", "lb_http_tg_arns": "${module.ecs-app-ApiServer_load-balancer_56066FC1.http_tgs_arns}", "lb_http_tg_ports": "${module.ecs-app-ApiServer_load-balancer_56066FC1.http_tgs_ports}", "name_prefix": "default-api_server", "private_subnets": ["${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_aid}", "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_bid}", "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_cid}"], "security_groups": ["${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_security_groupdefault_sgid}"], "service_discovery_namespace_id": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-container-compute-us-east-2.outputs.cross-stack-output-aws_service_discovery_private_dns_namespacemanaged-cluster-app_private_dns_namespace_204198B7id}", "source": "./assets/__cdktf_module_asset_26CE565C/5395951CF06B36840EF6A6BB5B657A3C/nv_fg_svc", "tags": {"Application": "api_server"}, "task_definition_arn": "${module.ecs-app-ApiServer_task-definition_6B9D23AB.arn}", "vpc_id": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_vpcdefault_vpcid}"}, "ecs-app-ApiServer_task-definition_6B9D23AB": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-ApiServer/task-definition", "uniqueId": "ecs-app-ApiServer_task-definition_6B9D23AB"}}, "container_image": "${data.terraform_remote_state.cross-stack-reference-input-default-root-coreinfra-ecr-us-east-2.outputs.cross-stack-output-aws_ecr_repositoryApiServerrepository_url}:${var.api_server_tag}", "container_name": "api_server", "essential": true, "family": "api_server", "log_configuration": {"logDriver": "awslogs", "options": {"awslogs-group": "${aws_cloudwatch_log_group.ecs-app-ApiServer_log-group_EE8C1EFB.name}", "awslogs-region": "us-east-2", "awslogs-stream-prefix": "awslogs-api-server"}}, "map_environment": {"DATABASES_FMCSAREADONLY_HOST": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478reader_endpoint}", "DATABASES_FMCSAREADONLY_NAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478database_name}", "DATABASES_FMCSAREADONLY_PORT": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478port}", "DATABASES_FMCSAREADONLY_USERNAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478master_username}", "DATABASES_FMCSAWRITE_HOST": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478endpoint}", "DATABASES_FMCSAWRITE_NAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478database_name}", "DATABASES_FMCSAWRITE_PORT": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478port}", "DATABASES_FMCSAWRITE_USERNAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478master_username}", "DATABASES_FMCSA_HOST": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478endpoint}", "DATABASES_FMCSA_NAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478database_name}", "DATABASES_FMCSA_PORT": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478port}", "DATABASES_FMCSA_USERNAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478master_username}", "DATABASES_NHTSA_HOST": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceNhtsaDB_F7136F88address}", "DATABASES_NIRVANA_HOST": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Caddress}", "DATABASES_NIRVANA_NAME": "postgres", "DATABASES_NIRVANA_PORT": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Cport}", "DATABASES_NIRVANA_USERNAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Cusername}", "ENV": "prod"}, "map_secrets": {"DATABASES_FMCSAREADONLY_PASSWORD": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_secretsmanager_secret_versionFmcsaDB_SecretValue_15098EB5arn}", "DATABASES_FMCSAWRITE_PASSWORD": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_secretsmanager_secret_versionFmcsaDB_SecretValue_15098EB5arn}", "DATABASES_FMCSA_PASSWORD": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_secretsmanager_secret_versionFmcsaDB_SecretValue_15098EB5arn}", "DATABASES_NHTSA_PASSWORD": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_secretsmanager_secret_versionNhtsaDB_SecretValue_EBD7DE9Earn}", "DATABASES_NIRVANA_PASSWORD": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_secretsmanager_secret_versionApplicationDB_SecretValue_app-db-readwrite-password_47B2BD30arn}", "PRODUCTTOOLS_LAUNCHDARKLYAPIKEY": "${data.aws_secretsmanager_secret.secret-LaunchDarklyApiKey.arn}"}, "port_mappings": [{"containerPort": 8080, "hostPort": 8080, "protocol": "tcp"}, {"containerPort": 6060, "hostPort": 6060, "protocol": "tcp"}], "source": "./assets/__cdktf_module_asset_26CE565C/5395951CF06B36840EF6A6BB5B657A3C/ecs_task_definition", "tags": {"Application": "api_server"}, "task_cpu": 256, "task_execution_role_arn": "arn:aws:iam::************:role/ecs-task-execution-role", "task_memory": 512, "task_role_arn": "${aws_iam_role.ecs-app-ApiServer_task-role_50461A6C.arn}"}, "ecs-app-DistsemServer_service_6463A1D7": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-DistsemServer/service", "uniqueId": "ecs-app-DistsemServer_service_6463A1D7"}}, "container_name": "distsem_server", "container_port": 34787, "deployment_maximum_percent": 100, "deployment_minimum_healthy_percent": 0, "desired_count": 1, "ecs_cluster_arn": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-container-compute-us-east-2.outputs.cross-stack-output-aws_ecs_clustermanaged-cluster-internal_tools_ecs_5D1FFB78arn}", "ecs_cluster_name": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-container-compute-us-east-2.outputs.cross-stack-output-aws_ecs_clustermanaged-cluster-internal_tools_ecs_5D1FFB78name}", "name_prefix": "distsem-server", "private_subnets": ["${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_aid}", "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_bid}", "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_cid}"], "security_groups": ["${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_security_groupdefault_sgid}"], "service_discovery_namespace_id": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-container-compute-us-east-2.outputs.cross-stack-output-aws_service_discovery_private_dns_namespacemanaged-cluster-app_private_dns_namespace_204198B7id}", "source": "./assets/__cdktf_module_asset_26CE565C/5395951CF06B36840EF6A6BB5B657A3C/nv_fg_svc", "tags": {"Application": "distsem_server", "Environment": "default"}, "task_definition_arn": "${module.ecs-app-DistsemServer_task-definition_12B167B1.arn}", "vpc_id": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_vpcdefault_vpcid}"}, "ecs-app-DistsemServer_task-definition_12B167B1": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-DistsemServer/task-definition", "uniqueId": "ecs-app-DistsemServer_task-definition_12B167B1"}}, "container_image": "${data.terraform_remote_state.cross-stack-reference-input-default-root-coreinfra-ecr-us-east-2.outputs.cross-stack-output-aws_ecr_repositoryDistsemServerrepository_url}:${var.distsem_server_tag}", "container_name": "distsem_server", "essential": true, "family": "distsem_server-td", "log_configuration": {"logDriver": "awslogs", "options": {"awslogs-group": "${aws_cloudwatch_log_group.ecs-app-DistsemServer_log-group_1C2CB9E3.name}", "awslogs-region": "us-east-2", "awslogs-stream-prefix": "awslogs-distsem-server"}}, "map_environment": {"DATABASES_NIRVANA_HOST": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Caddress}", "DATABASES_NIRVANA_NAME": "postgres", "DATABASES_NIRVANA_PASSWORD": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Cpassword}", "DATABASES_NIRVANA_PORT": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Cport}", "DATABASES_NIRVANA_USERNAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Cusername}", "ENV": "prod"}, "port_mappings": [{"containerPort": 34787, "hostPort": 34787, "protocol": "tcp"}, {"containerPort": 6067, "hostPort": 6067, "protocol": "tcp"}], "source": "./assets/__cdktf_module_asset_26CE565C/5395951CF06B36840EF6A6BB5B657A3C/ecs_task_definition", "task_cpu": 256, "task_execution_role_arn": "arn:aws:iam::************:role/ecs-task-execution-role", "task_memory": 512, "task_role_arn": "${aws_iam_role.ecs-app-DistsemServer_task-role_43EEF0C1.arn}"}, "ecs-app-EventJobProcessor_service_A2FBCA30": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-EventJobProcessor/service", "uniqueId": "ecs-app-EventJobProcessor_service_A2FBCA30"}}, "container_name": "event-job-processor", "container_port": 56222, "deployment_maximum_percent": 300, "deployment_minimum_healthy_percent": 100, "desired_count": 1, "ecs_cluster_arn": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-container-compute-us-east-2.outputs.cross-stack-output-aws_ecs_clustermanaged-cluster-internal_tools_ecs_5D1FFB78arn}", "ecs_cluster_name": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-container-compute-us-east-2.outputs.cross-stack-output-aws_ecs_clustermanaged-cluster-internal_tools_ecs_5D1FFB78name}", "name_prefix": "event-job-processor", "private_subnets": ["${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_aid}", "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_bid}", "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_cid}"], "security_groups": ["${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_security_groupdefault_sgid}"], "service_discovery_namespace_id": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-container-compute-us-east-2.outputs.cross-stack-output-aws_service_discovery_private_dns_namespacemanaged-cluster-app_private_dns_namespace_204198B7id}", "source": "./assets/__cdktf_module_asset_26CE565C/5395951CF06B36840EF6A6BB5B657A3C/nv_fg_svc", "tags": {"Application": "event-job-processor", "Environment": "default"}, "task_definition_arn": "${module.ecs-app-EventJobProcessor_task-definition_AA78AD7C.arn}", "vpc_id": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_vpcdefault_vpcid}"}, "ecs-app-EventJobProcessor_task-definition_AA78AD7C": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-EventJobProcessor/task-definition", "uniqueId": "ecs-app-EventJobProcessor_task-definition_AA78AD7C"}}, "container_image": "${data.terraform_remote_state.cross-stack-reference-input-default-root-coreinfra-ecr-us-east-2.outputs.cross-stack-output-aws_ecr_repositoryEventJobProcessorrepository_url}:${var.event_job_processor_tag}", "container_name": "event-job-processor", "essential": true, "family": "event-job-processor-td", "log_configuration": {"logDriver": "awslogs", "options": {"awslogs-group": "${aws_cloudwatch_log_group.ecs-app-EventJobProcessor_log-group_4705A617.name}", "awslogs-region": "us-east-2", "awslogs-stream-prefix": "awslogs-event-job-processor"}}, "map_environment": {"DATABASES_NIRVANA_HOST": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Caddress}", "DATABASES_NIRVANA_NAME": "postgres", "DATABASES_NIRVANA_PASSWORD": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Cpassword}", "DATABASES_NIRVANA_PORT": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Cport}", "DATABASES_NIRVANA_USERNAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Cusername}", "ENV": "prod", "PRODUCTTOOLS_LAUNCHDARKLYAPIKEY": "${data.aws_secretsmanager_secret_version.secret-version-LaunchDarklyApiKey.secret_string}"}, "port_mappings": [{"containerPort": 56222, "hostPort": 56222, "protocol": "tcp"}, {"containerPort": 6068, "hostPort": 6068, "protocol": "tcp"}], "source": "./assets/__cdktf_module_asset_26CE565C/5395951CF06B36840EF6A6BB5B657A3C/ecs_task_definition", "task_cpu": 256, "task_execution_role_arn": "arn:aws:iam::************:role/ecs-task-execution-role", "task_memory": 512, "task_role_arn": "${aws_iam_role.ecs-app-EventJobProcessor_task-role_12B57374.arn}"}, "ecs-app-FeatureStoreServer_service_79892F5D": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-FeatureStoreServer/service", "uniqueId": "ecs-app-FeatureStoreServer_service_79892F5D"}}, "container_name": "feature-store-server", "container_port": 5553, "ecs_cluster_arn": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-container-compute-us-east-2.outputs.cross-stack-output-aws_ecs_clustermanaged-cluster-app_ecs_11CC569Carn}", "ecs_cluster_name": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-container-compute-us-east-2.outputs.cross-stack-output-aws_ecs_clustermanaged-cluster-app_ecs_11CC569Cname}", "name_prefix": "feature-store-server", "private_subnets": ["${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_aid}", "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_bid}", "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_cid}"], "security_groups": ["${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_security_groupdefault_sgid}"], "service_discovery_namespace_id": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-container-compute-us-east-2.outputs.cross-stack-output-aws_service_discovery_private_dns_namespacemanaged-cluster-app_private_dns_namespace_204198B7id}", "source": "./assets/__cdktf_module_asset_26CE565C/5395951CF06B36840EF6A6BB5B657A3C/nv_fg_svc", "tags": {"Application": "feature-store-server", "Environment": "default"}, "task_definition_arn": "${module.ecs-app-FeatureStoreServer_task-definition_3DA6E786.arn}", "vpc_id": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_vpcdefault_vpcid}"}, "ecs-app-FeatureStoreServer_task-definition_3DA6E786": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-FeatureStoreServer/task-definition", "uniqueId": "ecs-app-FeatureStoreServer_task-definition_3DA6E786"}}, "container_image": "${data.terraform_remote_state.cross-stack-reference-input-default-root-coreinfra-ecr-us-east-2.outputs.cross-stack-output-aws_ecr_repositoryFeatureStoreServerrepository_url}:${var.feature_store_server_tag}", "container_name": "feature-store-server", "essential": true, "family": "feature-store-server-td", "log_configuration": {"logDriver": "awslogs", "options": {"awslogs-group": "${aws_cloudwatch_log_group.ecs-app-FeatureStoreServer_log-group_A70EFC37.name}", "awslogs-region": "us-east-2", "awslogs-stream-prefix": "awslogs-feature-store-server"}}, "map_environment": {"DATABASES_NIRVANA_HOST": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Caddress}", "DATABASES_NIRVANA_NAME": "postgres", "DATABASES_NIRVANA_PASSWORD": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Cpassword}", "DATABASES_NIRVANA_PORT": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Cport}", "DATABASES_NIRVANA_USERNAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Cusername}", "ENV": "prod", "PRODUCTTOOLS_LAUNCHDARKLYAPIKEY": "${data.aws_secretsmanager_secret_version.secret-version-LaunchDarklyApiKey.secret_string}"}, "port_mappings": [{"containerPort": 5553, "hostPort": 5553, "protocol": "tcp"}, {"containerPort": 6067, "hostPort": 6067, "protocol": "tcp"}], "source": "./assets/__cdktf_module_asset_26CE565C/5395951CF06B36840EF6A6BB5B657A3C/ecs_task_definition", "task_cpu": 256, "task_execution_role_arn": "arn:aws:iam::************:role/ecs-task-execution-role", "task_memory": 512, "task_role_arn": "${aws_iam_role.ecs-app-FeatureStoreServer_task-role_B924BED2.arn}"}, "ecs-app-GqlApiServer_load-balancer_6FCA286B": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-GqlApiServer/load-balancer", "uniqueId": "ecs-app-GqlApiServer_load-balancer_6FCA286B"}}, "default_certificate_arn": "${module.ssl-certificate.arn}", "http_ports": {"force_https": {"host": "#{host}", "listener_port": 80, "path": "/#{path}", "protocol": "HTTPS", "query": "#{query}", "status_code": "HTTP_301", "target_port": "443"}}, "https_ports": {"force_https": {"listener_port": 443}}, "load_balancing_algorithm_type": "round_robin", "name_prefix": "staging-gql-server", "public_subnets": ["${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_subnetpublic_subnet_aid}", "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_subnetpublic_subnet_bid}", "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_subnetpublic_subnet_cid}"], "security_groups": ["${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_security_groupdefault_sgid}"], "source": "./assets/__cdktf_module_asset_26CE565C/5395951CF06B36840EF6A6BB5B657A3C/nv_svc_alb", "ssl_policy": "ELBSecurityPolicy-2016-08", "target_group_health_check_enabled": true, "target_group_health_check_healthy_threshold": 3, "target_group_health_check_interval": 30, "target_group_health_check_matcher": "200,301,302", "target_group_health_check_path": "/health", "target_group_health_check_timeout": 10, "target_group_health_check_unhealthy_threshold": 3, "vpc_id": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_vpcdefault_vpcid}"}, "ecs-app-GqlApiServer_service_4EAA1F5F": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-GqlApiServer/service", "uniqueId": "ecs-app-GqlApiServer_service_4EAA1F5F"}}, "container_name": "gql-server", "container_port": 3030, "create_lb_access_sg_rule": true, "ecs_cluster_arn": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-container-compute-us-east-2.outputs.cross-stack-output-aws_ecs_clustermanaged-cluster-app_ecs_11CC569Carn}", "ecs_cluster_name": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-container-compute-us-east-2.outputs.cross-stack-output-aws_ecs_clustermanaged-cluster-app_ecs_11CC569Cname}", "lb_access_sg": "${module.ecs-app-GqlApiServer_load-balancer_6FCA286B.access_sg_id}", "lb_http_tg_arns": "${module.ecs-app-GqlApiServer_load-balancer_6FCA286B.http_tgs_arns}", "lb_http_tg_ports": "${module.ecs-app-GqlApiServer_load-balancer_6FCA286B.http_tgs_ports}", "name_prefix": "gql-server", "private_subnets": ["${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_aid}", "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_bid}", "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_cid}"], "security_groups": ["${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_security_groupdefault_sgid}"], "service_discovery_namespace_id": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-container-compute-us-east-2.outputs.cross-stack-output-aws_service_discovery_private_dns_namespacemanaged-cluster-app_private_dns_namespace_204198B7id}", "source": "./assets/__cdktf_module_asset_26CE565C/5395951CF06B36840EF6A6BB5B657A3C/nv_fg_svc", "tags": {"Application": "gql_server", "Environment": "default"}, "task_definition_arn": "${module.ecs-app-GqlApiServer_task-definition_2762DF9A.arn}", "vpc_id": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_vpcdefault_vpcid}"}, "ecs-app-GqlApiServer_task-definition_2762DF9A": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-GqlApiServer/task-definition", "uniqueId": "ecs-app-GqlApiServer_task-definition_2762DF9A"}}, "container_image": "${data.terraform_remote_state.cross-stack-reference-input-default-root-coreinfra-ecr-us-east-2.outputs.cross-stack-output-aws_ecr_repositoryGqlApiServerrepository_url}:${var.graphql_server_tag}", "container_name": "gql-server", "essential": true, "family": "gql-server-td", "log_configuration": {"logDriver": "awslogs", "options": {"awslogs-group": "${aws_cloudwatch_log_group.ecs-app-GqlApiServer_log-group_050EBB59.name}", "awslogs-region": "us-east-2", "awslogs-stream-prefix": "awslogs-gql-server"}}, "map_environment": {"DATABASES_FMCSAREADONLY_HOST": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478reader_endpoint}", "DATABASES_FMCSAREADONLY_NAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478database_name}", "DATABASES_FMCSAREADONLY_PASSWORD": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478master_password}", "DATABASES_FMCSAREADONLY_PORT": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478port}", "DATABASES_FMCSAREADONLY_USERNAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478master_username}", "DATABASES_FMCSAWRITE_HOST": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478endpoint}", "DATABASES_FMCSAWRITE_NAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478database_name}", "DATABASES_FMCSAWRITE_PASSWORD": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478master_password}", "DATABASES_FMCSAWRITE_PORT": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478port}", "DATABASES_FMCSAWRITE_USERNAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478master_username}", "DATABASES_FMCSA_HOST": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478endpoint}", "DATABASES_FMCSA_NAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478database_name}", "DATABASES_FMCSA_PASSWORD": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478master_password}", "DATABASES_FMCSA_PORT": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478port}", "DATABASES_FMCSA_USERNAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478master_username}", "DATABASES_NHTSA_HOST": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceNhtsaDB_F7136F88address}", "DATABASES_NHTSA_PASSWORD": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceNhtsaDB_F7136F88password}", "DATABASES_NIRVANA_HOST": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Caddress}", "DATABASES_NIRVANA_NAME": "postgres", "DATABASES_NIRVANA_PASSWORD": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Cpassword}", "DATABASES_NIRVANA_PORT": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Cport}", "DATABASES_NIRVANA_USERNAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Cusername}", "ENV": "prod", "PRODUCTTOOLS_LAUNCHDARKLYAPIKEY": "${data.aws_secretsmanager_secret_version.secret-version-LaunchDarklyApiKey.secret_string}"}, "port_mappings": [{"containerPort": 3030, "hostPort": 3030, "protocol": "tcp"}, {"containerPort": 6063, "hostPort": 6063, "protocol": "tcp"}], "source": "./assets/__cdktf_module_asset_26CE565C/5395951CF06B36840EF6A6BB5B657A3C/ecs_task_definition", "task_cpu": 256, "task_execution_role_arn": "arn:aws:iam::************:role/ecs-task-execution-role", "task_memory": 512, "task_role_arn": "${aws_iam_role.ecs-app-GqlApiServer_task-role_B9A03BEA.arn}"}, "ecs-app-JobProcessor_service_2B71340A": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-JobProcessor/service", "uniqueId": "ecs-app-JobProcessor_service_2B71340A"}}, "container_name": "job-processor", "container_port": 56223, "deployment_maximum_percent": 300, "deployment_minimum_healthy_percent": 100, "desired_count": 1, "ecs_cluster_arn": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-container-compute-us-east-2.outputs.cross-stack-output-aws_ecs_clustermanaged-cluster-internal_tools_ecs_5D1FFB78arn}", "ecs_cluster_name": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-container-compute-us-east-2.outputs.cross-stack-output-aws_ecs_clustermanaged-cluster-internal_tools_ecs_5D1FFB78name}", "name_prefix": "job-processor", "private_subnets": ["${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_aid}", "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_bid}", "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_cid}"], "security_groups": ["${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_security_groupdefault_sgid}"], "service_discovery_namespace_id": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-container-compute-us-east-2.outputs.cross-stack-output-aws_service_discovery_private_dns_namespacemanaged-cluster-app_private_dns_namespace_204198B7id}", "source": "./assets/__cdktf_module_asset_26CE565C/5395951CF06B36840EF6A6BB5B657A3C/nv_fg_svc", "tags": {"Application": "job-processor", "Environment": "default"}, "task_definition_arn": "${module.ecs-app-JobProcessor_task-definition_DAA38339.arn}", "vpc_id": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_vpcdefault_vpcid}"}, "ecs-app-JobProcessor_task-definition_DAA38339": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-JobProcessor/task-definition", "uniqueId": "ecs-app-JobProcessor_task-definition_DAA38339"}}, "container_image": "${data.terraform_remote_state.cross-stack-reference-input-default-root-coreinfra-ecr-us-east-2.outputs.cross-stack-output-aws_ecr_repositoryJobProcessorrepository_url}:${var.job_processor_tag}", "container_name": "job-processor", "essential": true, "family": "job-processor-td", "log_configuration": {"logDriver": "awslogs", "options": {"awslogs-group": "${aws_cloudwatch_log_group.ecs-app-JobProcessor_log-group_C2F6BA0F.name}", "awslogs-region": "us-east-2", "awslogs-stream-prefix": "awslogs-job-processor"}}, "map_environment": {"DATABASES_FMCSAREADONLY_HOST": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478reader_endpoint}", "DATABASES_FMCSAREADONLY_NAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478database_name}", "DATABASES_FMCSAREADONLY_PASSWORD": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478master_password}", "DATABASES_FMCSAREADONLY_PORT": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478port}", "DATABASES_FMCSAREADONLY_USERNAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478master_username}", "DATABASES_FMCSAWRITE_HOST": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478endpoint}", "DATABASES_FMCSAWRITE_NAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478database_name}", "DATABASES_FMCSAWRITE_PASSWORD": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478master_password}", "DATABASES_FMCSAWRITE_PORT": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478port}", "DATABASES_FMCSAWRITE_USERNAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478master_username}", "DATABASES_FMCSA_HOST": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478endpoint}", "DATABASES_FMCSA_NAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478database_name}", "DATABASES_FMCSA_PASSWORD": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478master_password}", "DATABASES_FMCSA_PORT": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478port}", "DATABASES_FMCSA_USERNAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478master_username}", "DATABASES_NHTSA_HOST": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceNhtsaDB_F7136F88address}", "DATABASES_NHTSA_PASSWORD": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceNhtsaDB_F7136F88password}", "DATABASES_NIRVANA_HOST": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Caddress}", "DATABASES_NIRVANA_NAME": "postgres", "DATABASES_NIRVANA_PASSWORD": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Cpassword}", "DATABASES_NIRVANA_PORT": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Cport}", "DATABASES_NIRVANA_USERNAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Cusername}", "ENV": "prod", "PRODUCTTOOLS_LAUNCHDARKLYAPIKEY": "${data.aws_secretsmanager_secret_version.secret-version-LaunchDarklyApiKey.secret_string}"}, "port_mappings": [{"containerPort": 56223, "hostPort": 56223, "protocol": "tcp"}, {"containerPort": 6065, "hostPort": 6065, "protocol": "tcp"}], "source": "./assets/__cdktf_module_asset_26CE565C/5395951CF06B36840EF6A6BB5B657A3C/ecs_task_definition", "task_cpu": 256, "task_execution_role_arn": "arn:aws:iam::************:role/ecs-task-execution-role", "task_memory": 512, "task_role_arn": "${aws_iam_role.ecs-app-JobProcessor_task-role_F4C86BE2.arn}"}, "ecs-app-JobberMonitor_service_977A1D12": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-JobberMonitor/service", "uniqueId": "ecs-app-JobberMonitor_service_977A1D12"}}, "container_name": "jobber_monitor", "container_port": 56666, "deployment_maximum_percent": 100, "deployment_minimum_healthy_percent": 0, "desired_count": 1, "ecs_cluster_arn": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-container-compute-us-east-2.outputs.cross-stack-output-aws_ecs_clustermanaged-cluster-jobber_singletons_ecs_0BC316EAarn}", "ecs_cluster_name": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-container-compute-us-east-2.outputs.cross-stack-output-aws_ecs_clustermanaged-cluster-jobber_singletons_ecs_0BC316EAname}", "name_prefix": "jobber-monitor", "private_subnets": ["${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_aid}", "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_bid}", "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_cid}"], "security_groups": ["${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_security_groupdefault_sgid}"], "service_discovery_namespace_id": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-container-compute-us-east-2.outputs.cross-stack-output-aws_service_discovery_private_dns_namespacemanaged-cluster-app_private_dns_namespace_204198B7id}", "source": "./assets/__cdktf_module_asset_26CE565C/5395951CF06B36840EF6A6BB5B657A3C/nv_fg_svc", "tags": {"Application": "jobber_monitor", "Environment": "default"}, "task_definition_arn": "${module.ecs-app-JobberMonitor_task-definition_E977CD21.arn}", "vpc_id": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_vpcdefault_vpcid}"}, "ecs-app-JobberMonitor_task-definition_E977CD21": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-JobberMonitor/task-definition", "uniqueId": "ecs-app-JobberMonitor_task-definition_E977CD21"}}, "container_image": "${data.terraform_remote_state.cross-stack-reference-input-default-root-coreinfra-ecr-us-east-2.outputs.cross-stack-output-aws_ecr_repositoryJobberMonitorrepository_url}:${var.jobber_monitor_tag}", "container_name": "jobber_monitor", "essential": true, "family": "jobber_monitor-td", "log_configuration": {"logDriver": "awslogs", "options": {"awslogs-group": "${aws_cloudwatch_log_group.ecs-app-JobberMonitor_log-group_89F06DC4.name}", "awslogs-region": "us-east-2", "awslogs-stream-prefix": "awslogs-jobber-monitor"}}, "map_environment": {"DATABASES_NIRVANA_HOST": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Caddress}", "DATABASES_NIRVANA_NAME": "postgres", "DATABASES_NIRVANA_PASSWORD": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Cpassword}", "DATABASES_NIRVANA_PORT": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Cport}", "DATABASES_NIRVANA_USERNAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Cusername}", "ENV": "prod"}, "port_mappings": [{"containerPort": 56666, "hostPort": 56666, "protocol": "tcp"}, {"containerPort": 6060, "hostPort": 6060, "protocol": "tcp"}], "source": "./assets/__cdktf_module_asset_26CE565C/5395951CF06B36840EF6A6BB5B657A3C/ecs_task_definition", "tags": {"Application": "jobber_monitor", "Environment": "default"}, "task_cpu": 256, "task_execution_role_arn": "arn:aws:iam::************:role/ecs-task-execution-role", "task_memory": 512}, "ecs-app-PdfgenServer_service_0BF3E5EE": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-PdfgenServer/service", "uniqueId": "ecs-app-PdfgenServer_service_0BF3E5EE"}}, "container_name": "pdfgen_server", "container_port": 33435, "ecs_cluster_arn": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-container-compute-us-east-2.outputs.cross-stack-output-aws_ecs_clustermanaged-cluster-app_ecs_11CC569Carn}", "ecs_cluster_name": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-container-compute-us-east-2.outputs.cross-stack-output-aws_ecs_clustermanaged-cluster-app_ecs_11CC569Cname}", "name_prefix": "pdfgen-server", "private_subnets": ["${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_aid}", "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_bid}", "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_cid}"], "security_groups": ["${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_security_groupdefault_sgid}"], "service_discovery_namespace_id": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-container-compute-us-east-2.outputs.cross-stack-output-aws_service_discovery_private_dns_namespacemanaged-cluster-app_private_dns_namespace_204198B7id}", "source": "./assets/__cdktf_module_asset_26CE565C/5395951CF06B36840EF6A6BB5B657A3C/nv_fg_svc", "tags": {"Application": "pdfgen_server", "Environment": "default"}, "task_definition_arn": "${module.ecs-app-PdfgenServer_task-definition_CB2B8212.arn}", "vpc_id": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_vpcdefault_vpcid}"}, "ecs-app-PdfgenServer_task-definition_CB2B8212": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-PdfgenServer/task-definition", "uniqueId": "ecs-app-PdfgenServer_task-definition_CB2B8212"}}, "container_image": "${data.terraform_remote_state.cross-stack-reference-input-default-root-coreinfra-ecr-us-east-2.outputs.cross-stack-output-aws_ecr_repositoryPdfgenServerrepository_url}:${var.pdfgen_server_tag}", "container_name": "pdfgen_server", "essential": true, "family": "pdfgen_server-td", "healthcheck": {"command": ["CMD-SHELL", "/app/grpc_health_probe -addr=:33435"], "interval": 30, "retries": 3, "startPeriod": 30, "timeout": 5}, "log_configuration": {"logDriver": "awslogs", "options": {"awslogs-group": "${aws_cloudwatch_log_group.ecs-app-PdfgenServer_log-group_BC4DCED9.name}", "awslogs-region": "us-east-2", "awslogs-stream-prefix": "awslogs-pdfgen-server"}}, "map_environment": {"DATABASES_NIRVANA_HOST": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Caddress}", "DATABASES_NIRVANA_NAME": "postgres", "DATABASES_NIRVANA_PASSWORD": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Cpassword}", "DATABASES_NIRVANA_PORT": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Cport}", "DATABASES_NIRVANA_USERNAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Cusername}", "ENV": "prod"}, "port_mappings": [{"containerPort": 33435, "hostPort": 33435, "protocol": "tcp"}, {"containerPort": 6062, "hostPort": 6062, "protocol": "tcp"}], "source": "./assets/__cdktf_module_asset_26CE565C/5395951CF06B36840EF6A6BB5B657A3C/ecs_task_definition", "tags": {"Application": "pdfgen_server", "Environment": "default"}, "task_cpu": 2048, "task_execution_role_arn": "arn:aws:iam::************:role/ecs-task-execution-role", "task_memory": 4096, "task_role_arn": "${aws_iam_role.ecs-app-PdfgenServer_task-role_02744525.arn}"}, "ecs-app-QuotingJobProcessor_service_CD2AEA2D": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-QuotingJobProcessor/service", "uniqueId": "ecs-app-QuotingJobProcessor_service_CD2AEA2D"}}, "container_name": "quoting-job-processor", "container_port": 56224, "deployment_maximum_percent": 300, "deployment_minimum_healthy_percent": 100, "desired_count": 1, "ecs_cluster_arn": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-container-compute-us-east-2.outputs.cross-stack-output-aws_ecs_clustermanaged-cluster-app_ecs_11CC569Carn}", "ecs_cluster_name": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-container-compute-us-east-2.outputs.cross-stack-output-aws_ecs_clustermanaged-cluster-app_ecs_11CC569Cname}", "name_prefix": "quoting-job-processor", "private_subnets": ["${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_aid}", "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_bid}", "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_cid}"], "security_groups": ["${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_security_groupdefault_sgid}"], "service_discovery_namespace_id": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-container-compute-us-east-2.outputs.cross-stack-output-aws_service_discovery_private_dns_namespacemanaged-cluster-app_private_dns_namespace_204198B7id}", "source": "./assets/__cdktf_module_asset_26CE565C/5395951CF06B36840EF6A6BB5B657A3C/nv_fg_svc", "tags": {"Application": "quoting-job-processor", "Environment": "default"}, "task_definition_arn": "${module.ecs-app-QuotingJobProcessor_task-definition_790BBEC4.arn}", "vpc_id": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_vpcdefault_vpcid}"}, "ecs-app-QuotingJobProcessor_task-definition_790BBEC4": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-QuotingJobProcessor/task-definition", "uniqueId": "ecs-app-QuotingJobProcessor_task-definition_790BBEC4"}}, "container_image": "${data.terraform_remote_state.cross-stack-reference-input-default-root-coreinfra-ecr-us-east-2.outputs.cross-stack-output-aws_ecr_repositoryQuotingJobProcessorrepository_url}:${var.quoting_job_processor_tag}", "container_name": "quoting-job-processor", "essential": true, "family": "quoting-job-processor-td", "log_configuration": {"logDriver": "awslogs", "options": {"awslogs-group": "${aws_cloudwatch_log_group.ecs-app-QuotingJobProcessor_log-group_644ABE1B.name}", "awslogs-region": "us-east-2", "awslogs-stream-prefix": "awslogs-quoting-job-processor"}}, "map_environment": {"DATABASES_FMCSAREADONLY_HOST": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478reader_endpoint}", "DATABASES_FMCSAREADONLY_NAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478database_name}", "DATABASES_FMCSAREADONLY_PASSWORD": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478master_password}", "DATABASES_FMCSAREADONLY_PORT": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478port}", "DATABASES_FMCSAREADONLY_USERNAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478master_username}", "DATABASES_FMCSAWRITE_HOST": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478endpoint}", "DATABASES_FMCSAWRITE_NAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478database_name}", "DATABASES_FMCSAWRITE_PASSWORD": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478master_password}", "DATABASES_FMCSAWRITE_PORT": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478port}", "DATABASES_FMCSAWRITE_USERNAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478master_username}", "DATABASES_FMCSA_HOST": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478endpoint}", "DATABASES_FMCSA_NAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478database_name}", "DATABASES_FMCSA_PASSWORD": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478master_password}", "DATABASES_FMCSA_PORT": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478port}", "DATABASES_FMCSA_USERNAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478master_username}", "DATABASES_NHTSA_HOST": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceNhtsaDB_F7136F88address}", "DATABASES_NHTSA_PASSWORD": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceNhtsaDB_F7136F88password}", "DATABASES_NIRVANA_HOST": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Caddress}", "DATABASES_NIRVANA_NAME": "postgres", "DATABASES_NIRVANA_PASSWORD": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Cpassword}", "DATABASES_NIRVANA_PORT": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Cport}", "DATABASES_NIRVANA_USERNAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Cusername}", "ENV": "prod", "PRODUCTTOOLS_LAUNCHDARKLYAPIKEY": "${data.aws_secretsmanager_secret_version.secret-version-LaunchDarklyApiKey.secret_string}"}, "port_mappings": [{"containerPort": 56224, "hostPort": 56224, "protocol": "tcp"}, {"containerPort": 6060, "hostPort": 6060, "protocol": "tcp"}], "source": "./assets/__cdktf_module_asset_26CE565C/5395951CF06B36840EF6A6BB5B657A3C/ecs_task_definition", "task_cpu": 256, "task_execution_role_arn": "arn:aws:iam::************:role/ecs-task-execution-role", "task_memory": 512, "task_role_arn": "${aws_iam_role.ecs-app-QuotingJobProcessor_task-role_9501B3AB.arn}"}, "ecs-app-SafetyJobProcessor_service_1D37913F": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-SafetyJobProcessor/service", "uniqueId": "ecs-app-SafetyJobProcessor_service_1D37913F"}}, "container_name": "safety-job-processor", "container_port": 56226, "deployment_maximum_percent": 300, "deployment_minimum_healthy_percent": 100, "desired_count": 1, "ecs_cluster_arn": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-container-compute-us-east-2.outputs.cross-stack-output-aws_ecs_clustermanaged-cluster-internal_tools_ecs_5D1FFB78arn}", "ecs_cluster_name": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-container-compute-us-east-2.outputs.cross-stack-output-aws_ecs_clustermanaged-cluster-internal_tools_ecs_5D1FFB78name}", "name_prefix": "safety-job-processor", "private_subnets": ["${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_aid}", "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_bid}", "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_subnetprivate_subnet_cid}"], "security_groups": ["${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_security_groupdefault_sgid}"], "service_discovery_namespace_id": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-container-compute-us-east-2.outputs.cross-stack-output-aws_service_discovery_private_dns_namespacemanaged-cluster-app_private_dns_namespace_204198B7id}", "source": "./assets/__cdktf_module_asset_26CE565C/5395951CF06B36840EF6A6BB5B657A3C/nv_fg_svc", "tags": {"Application": "safety-job-processor", "Environment": "default"}, "task_definition_arn": "${module.ecs-app-SafetyJobProcessor_task-definition_18CDA7A4.arn}", "vpc_id": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_vpcdefault_vpcid}"}, "ecs-app-SafetyJobProcessor_task-definition_18CDA7A4": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-SafetyJobProcessor/task-definition", "uniqueId": "ecs-app-SafetyJobProcessor_task-definition_18CDA7A4"}}, "container_image": "${data.terraform_remote_state.cross-stack-reference-input-default-root-coreinfra-ecr-us-east-2.outputs.cross-stack-output-aws_ecr_repositorySafetyJobProcessorrepository_url}:${var.safety_job_processor_tag}", "container_name": "safety-job-processor", "essential": true, "family": "safety-job-processor-td", "log_configuration": {"logDriver": "awslogs", "options": {"awslogs-group": "${aws_cloudwatch_log_group.ecs-app-SafetyJobProcessor_log-group_2B96F5D1.name}", "awslogs-region": "us-east-2", "awslogs-stream-prefix": "awslogs-job-processor"}}, "map_environment": {"DATABASES_FMCSAREADONLY_HOST": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478reader_endpoint}", "DATABASES_FMCSAREADONLY_NAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478database_name}", "DATABASES_FMCSAREADONLY_PASSWORD": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478master_password}", "DATABASES_FMCSAREADONLY_PORT": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478port}", "DATABASES_FMCSAREADONLY_USERNAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478master_username}", "DATABASES_FMCSAWRITE_HOST": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478endpoint}", "DATABASES_FMCSAWRITE_NAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478database_name}", "DATABASES_FMCSAWRITE_PASSWORD": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478master_password}", "DATABASES_FMCSAWRITE_PORT": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478port}", "DATABASES_FMCSAWRITE_USERNAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478master_username}", "DATABASES_FMCSA_HOST": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478endpoint}", "DATABASES_FMCSA_NAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478database_name}", "DATABASES_FMCSA_PASSWORD": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478master_password}", "DATABASES_FMCSA_PORT": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478port}", "DATABASES_FMCSA_USERNAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_rds_clusterFmcsaDB_FmcsaDBCluster_72173478master_username}", "DATABASES_NHTSA_HOST": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceNhtsaDB_F7136F88address}", "DATABASES_NHTSA_PASSWORD": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceNhtsaDB_F7136F88password}", "DATABASES_NIRVANA_HOST": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Caddress}", "DATABASES_NIRVANA_NAME": "postgres", "DATABASES_NIRVANA_PASSWORD": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Cpassword}", "DATABASES_NIRVANA_PORT": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Cport}", "DATABASES_NIRVANA_USERNAME": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_db_instanceApplicationDB_49B5873Cusername}", "ENV": "prod", "PRODUCTTOOLS_LAUNCHDARKLYAPIKEY": "${data.aws_secretsmanager_secret_version.secret-version-LaunchDarklyApiKey.secret_string}"}, "port_mappings": [{"containerPort": 56226, "hostPort": 56226, "protocol": "tcp"}, {"containerPort": 6067, "hostPort": 6067, "protocol": "tcp"}], "source": "./assets/__cdktf_module_asset_26CE565C/5395951CF06B36840EF6A6BB5B657A3C/ecs_task_definition", "task_cpu": 256, "task_execution_role_arn": "arn:aws:iam::************:role/ecs-task-execution-role", "task_memory": 512, "task_role_arn": "${aws_iam_role.ecs-app-SafetyJobProcessor_task-role_558536D0.arn}"}, "ssl-certificate": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ssl-certificate", "uniqueId": "ssl-certificate"}}, "domain_name": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-account-essentials.outputs.cross-stack-output-aws_route53_zonedns_zone_2E2729FBname}", "source": "./assets/__cdktf_module_asset_26CE565C/5395951CF06B36840EF6A6BB5B657A3C/acm", "subject_alternative_names": ["*.${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-account-essentials.outputs.cross-stack-output-aws_route53_zonedns_zone_2E2729FBname}"], "validation_method": "DNS", "wait_for_validation": false, "zone_id": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-account-essentials.outputs.cross-stack-output-aws_route53_zonedns_zone_2E2729FBzone_id}"}}, "output": {"api_server_tag": {"description": "The tag of the ApiServer image currently deployed", "value": "${var.api_server_tag}"}, "distsem_server_tag": {"description": "The tag of the DistsemServer image currently deployed", "value": "${var.distsem_server_tag}"}, "event_job_processor_tag": {"description": "The tag of the EventJobProcessor image currently deployed", "value": "${var.event_job_processor_tag}"}, "feature_store_server_tag": {"description": "The tag of the FeatureStoreServer image currently deployed", "value": "${var.feature_store_server_tag}"}, "graphql_server_tag": {"description": "The tag of the GqlApiServer image currently deployed", "value": "${var.graphql_server_tag}"}, "job_processor_tag": {"description": "The tag of the JobProcessor image currently deployed", "value": "${var.job_processor_tag}"}, "jobber_monitor_tag": {"description": "The tag of the JobberMonitor image currently deployed", "value": "${var.jobber_monitor_tag}"}, "pdfgen_server_tag": {"description": "The tag of the PdfgenServer image currently deployed", "value": "${var.pdfgen_server_tag}"}, "quoting_job_processor_tag": {"description": "The tag of the QuotingJobProcessor image currently deployed", "value": "${var.quoting_job_processor_tag}"}, "safety_job_processor_tag": {"description": "The tag of the SafetyJobProcessor image currently deployed", "value": "${var.safety_job_processor_tag}"}}, "provider": {"aws": [{"allowed_account_ids": ["************"], "assume_role": [{"role_arn": "arn:aws:iam::************:role/AdministratorAccessForManagementAccountUsers"}], "default_tags": [{"tags": {"environment": "staging", "group": "backend", "infraWorkspace": "default", "region": "us-east-2", "stackName": "application-stack"}}], "region": "us-east-2"}]}, "resource": {"aws_cloudwatch_log_group": {"ecs-app-ApiServer_log-group_EE8C1EFB": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-ApiServer/log-group", "uniqueId": "ecs-app-ApiServer_log-group_EE8C1EFB"}}, "name": "default-api-server-logs", "tags": {"Application": "api_server", "Environment": "default"}}, "ecs-app-DistsemServer_log-group_1C2CB9E3": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-DistsemServer/log-group", "uniqueId": "ecs-app-DistsemServer_log-group_1C2CB9E3"}}, "name": "default-distsem-server-logs", "tags": {"Application": "distsem-server", "Environment": "default"}}, "ecs-app-EventJobProcessor_log-group_4705A617": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-EventJobProcessor/log-group", "uniqueId": "ecs-app-EventJobProcessor_log-group_4705A617"}}, "name": "default-event-job-processor-logs", "tags": {"Application": "event_job_processor", "Environment": "default"}}, "ecs-app-FeatureStoreServer_log-group_A70EFC37": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-FeatureStoreServer/log-group", "uniqueId": "ecs-app-FeatureStoreServer_log-group_A70EFC37"}}, "name": "default-feature-store-server-logs", "tags": {"Application": "feature_store_server", "Environment": "default"}}, "ecs-app-GqlApiServer_log-group_050EBB59": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-GqlApiServer/log-group", "uniqueId": "ecs-app-GqlApiServer_log-group_050EBB59"}}, "name": "default-gql-server-logs", "tags": {"Application": "gql_server", "Environment": "default"}}, "ecs-app-JobProcessor_log-group_C2F6BA0F": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-JobProcessor/log-group", "uniqueId": "ecs-app-JobProcessor_log-group_C2F6BA0F"}}, "name": "default-job-processor-logs", "tags": {"Application": "job_processor", "Environment": "default"}}, "ecs-app-JobberMonitor_log-group_89F06DC4": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-JobberMonitor/log-group", "uniqueId": "ecs-app-JobberMonitor_log-group_89F06DC4"}}, "name": "default-jobber-monitor-logs", "tags": {"Application": "jobber-monitor", "Environment": "default"}}, "ecs-app-PdfgenServer_log-group_BC4DCED9": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-PdfgenServer/log-group", "uniqueId": "ecs-app-PdfgenServer_log-group_BC4DCED9"}}, "name": "default-pdfgen-server-logs", "tags": {"Application": "pdfgen-server", "Environment": "default"}}, "ecs-app-QuotingJobProcessor_log-group_644ABE1B": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-QuotingJobProcessor/log-group", "uniqueId": "ecs-app-QuotingJobProcessor_log-group_644ABE1B"}}, "name": "default-quoting-job-processor-logs", "tags": {"Application": "quoting_job_processor", "Environment": "default"}}, "ecs-app-SafetyJobProcessor_log-group_2B96F5D1": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-SafetyJobProcessor/log-group", "uniqueId": "ecs-app-SafetyJobProcessor_log-group_2B96F5D1"}}, "name": "default-safety-job-processor-logs", "tags": {"Application": "safety_job_processor", "Environment": "default"}}}, "aws_iam_policy": {"ecs-app-ApiServer_task-role-policy_788A5015": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-ApiServer/task-role-policy", "uniqueId": "ecs-app-ApiServer_task-role-policy_788A5015"}}, "policy": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Action\": [\n        \"kms:Decrypt\",\n        \"kms:GenerateData<PERSON>ey\"\n      ],\n      \"Resource\": [\n        \"${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-kms-keys-us-east-2.outputs.cross-stack-output-aws_kms_keyTelematicsApiKeyCredentials_key_18191D20arn}\",\n        \"${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-kms-keys-us-east-2.outputs.cross-stack-output-aws_kms_keyTelematicsConsentUserRequestMetadata_key_D8A98242arn}\",\n        \"${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-kms-keys-us-east-2.outputs.cross-stack-output-aws_kms_keyTerminalUserCredentials_key_39CA3536arn}\",\n        \"${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-kms-keys-us-east-2.outputs.cross-stack-output-aws_kms_keyTspWebhookSecretKeysCredentials_key_8AC8CC68arn}\",\n        \"${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-kms-keys-us-east-2.outputs.cross-stack-output-aws_kms_keyUserSsn_key_748A9B95arn}\",\n        \"${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-kms-keys-us-east-2.outputs.cross-stack-output-aws_kms_keyUserSsnLastFour_key_5875D18Barn}\"\n      ],\n      \"Effect\": \"Allow\"\n    },\n    {\n      \"Action\": \"s3:ListBucket\",\n      \"Resource\": \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketGoServiceProfiles_bucket_6F102610bucket}\",\n      \"Effect\": \"Allow\"\n    },\n    {\n      \"Action\": [\n        \"s3:GetObject\",\n        \"s3:PutObject\"\n      ],\n      \"Resource\": \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketGoServiceProfiles_bucket_6F102610bucket}/*\",\n      \"Effect\": \"Allow\"\n    }\n  ]\n}"}, "ecs-app-DistsemServer_task-role-policy_A58E15E9": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-DistsemServer/task-role-policy", "uniqueId": "ecs-app-DistsemServer_task-role-policy_A58E15E9"}}, "policy": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Action\": \"s3:ListBucket\",\n      \"Resource\": \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketGoServiceProfiles_bucket_6F102610bucket}\",\n      \"Effect\": \"Allow\"\n    },\n    {\n      \"Action\": [\n        \"s3:GetObject\",\n        \"s3:PutObject\"\n      ],\n      \"Resource\": \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketGoServiceProfiles_bucket_6F102610bucket}/*\",\n      \"Effect\": \"Allow\"\n    }\n  ]\n}"}, "ecs-app-EventJobProcessor_task-role-policy_FD4A1D91": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-EventJobProcessor/task-role-policy", "uniqueId": "ecs-app-EventJobProcessor_task-role-policy_FD4A1D91"}}, "policy": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Action\": \"s3:ListBucket\",\n      \"Resource\": \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketGoServiceProfiles_bucket_6F102610bucket}\",\n      \"Effect\": \"Allow\"\n    },\n    {\n      \"Action\": [\n        \"s3:GetObject\",\n        \"s3:PutObject\"\n      ],\n      \"Resource\": \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketGoServiceProfiles_bucket_6F102610bucket}/*\",\n      \"Effect\": \"Allow\"\n    },\n    {\n      \"Action\": \"s3:ListBucket\",\n      \"Resource\": \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketJobberSchedules_bucket_C51DBF80bucket}\",\n      \"Effect\": \"Allow\"\n    },\n    {\n      \"Action\": [\n        \"s3:GetObject\",\n        \"s3:PutObject\"\n      ],\n      \"Resource\": \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketJobberSchedules_bucket_C51DBF80bucket}/*\",\n      \"Effect\": \"Allow\"\n    },\n    {\n      \"Action\": [\n        \"ecs:GetTaskProtection\",\n        \"ecs:UpdateTaskProtection\"\n      ],\n      \"Resource\": \"arn:aws:ecs:*:************:task/*/*\",\n      \"Effect\": \"Allow\"\n    }\n  ]\n}"}, "ecs-app-FeatureStoreServer_task-role-policy_A03BBD15": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-FeatureStoreServer/task-role-policy", "uniqueId": "ecs-app-FeatureStoreServer_task-role-policy_A03BBD15"}}, "policy": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Action\": \"s3:ListBucket\",\n      \"Resource\": \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketGoServiceProfiles_bucket_6F102610bucket}\",\n      \"Effect\": \"Allow\"\n    },\n    {\n      \"Action\": [\n        \"s3:GetObject\",\n        \"s3:PutObject\"\n      ],\n      \"Resource\": \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketGoServiceProfiles_bucket_6F102610bucket}/*\",\n      \"Effect\": \"Allow\"\n    }\n  ]\n}"}, "ecs-app-GqlApiServer_task-role-policy_09E07EFC": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-GqlApiServer/task-role-policy", "uniqueId": "ecs-app-GqlApiServer_task-role-policy_09E07EFC"}}, "policy": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Action\": \"s3:ListBucket\",\n      \"Resource\": \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketGoServiceProfiles_bucket_6F102610bucket}\",\n      \"Effect\": \"Allow\"\n    },\n    {\n      \"Action\": [\n        \"s3:GetObject\",\n        \"s3:PutObject\"\n      ],\n      \"Resource\": \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketGoServiceProfiles_bucket_6F102610bucket}/*\",\n      \"Effect\": \"Allow\"\n    }\n  ]\n}"}, "ecs-app-JobProcessor_task-role-policy_C3870FD5": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-JobProcessor/task-role-policy", "uniqueId": "ecs-app-JobProcessor_task-role-policy_C3870FD5"}}, "policy": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Action\": \"s3:ListBucket\",\n      \"Resource\": \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketFmcsaPublicFiles_bucket_29109011bucket}\",\n      \"Effect\": \"Allow\"\n    },\n    {\n      \"Action\": [\n        \"s3:GetObject\",\n        \"s3:PutObject\"\n      ],\n      \"Resource\": \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketFmcsaPublicFiles_bucket_29109011bucket}/*\",\n      \"Effect\": \"Allow\"\n    },\n    {\n      \"Action\": \"s3:ListBucket\",\n      \"Resource\": \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketGoServiceProfiles_bucket_6F102610bucket}\",\n      \"Effect\": \"Allow\"\n    },\n    {\n      \"Action\": [\n        \"s3:GetObject\",\n        \"s3:PutObject\"\n      ],\n      \"Resource\": \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketGoServiceProfiles_bucket_6F102610bucket}/*\",\n      \"Effect\": \"Allow\"\n    },\n    {\n      \"Action\": \"s3:ListBucket\",\n      \"Resource\": \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketFmcsaSftpDownloads_bucket_035D7C25bucket}\",\n      \"Effect\": \"Allow\"\n    },\n    {\n      \"Action\": [\n        \"s3:GetObject\",\n        \"s3:PutObject\"\n      ],\n      \"Resource\": \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketFmcsaSftpDownloads_bucket_035D7C25bucket}/*\",\n      \"Effect\": \"Allow\"\n    },\n    {\n      \"Action\": \"s3:ListBucket\",\n      \"Resource\": \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketJobberSchedules_bucket_C51DBF80bucket}\",\n      \"Effect\": \"Allow\"\n    },\n    {\n      \"Action\": [\n        \"s3:GetObject\",\n        \"s3:PutObject\"\n      ],\n      \"Resource\": \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketJobberSchedules_bucket_C51DBF80bucket}/*\",\n      \"Effect\": \"Allow\"\n    },\n    {\n      \"Action\": \"s3:ListBucket\",\n      \"Resource\": \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketDatagov_bucket_7AD2F144bucket}\",\n      \"Effect\": \"Allow\"\n    },\n    {\n      \"Action\": [\n        \"s3:GetObject\",\n        \"s3:PutObject\"\n      ],\n      \"Resource\": \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketDatagov_bucket_7AD2F144bucket}/*\",\n      \"Effect\": \"Allow\"\n    },\n    {\n      \"Action\": [\n        \"ecs:GetTaskProtection\",\n        \"ecs:UpdateTaskProtection\"\n      ],\n      \"Resource\": \"arn:aws:ecs:*:************:task/*/*\",\n      \"Effect\": \"Allow\"\n    }\n  ]\n}"}, "ecs-app-PdfgenServer_task-role-policy_29D298EB": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-PdfgenServer/task-role-policy", "uniqueId": "ecs-app-PdfgenServer_task-role-policy_29D298EB"}}, "policy": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Action\": \"s3:ListBucket\",\n      \"Resource\": [\n        \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketPdfgenServer_bucket_9A92892Cbucket}\",\n        \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketGoServiceProfiles_bucket_6F102610bucket}\"\n      ],\n      \"Effect\": \"Allow\"\n    },\n    {\n      \"Action\": [\n        \"s3:GetObject\",\n        \"s3:PutObject\"\n      ],\n      \"Resource\": [\n        \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketPdfgenServer_bucket_9A92892Cbucket}/*\",\n        \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketGoServiceProfiles_bucket_6F102610bucket}/*\"\n      ],\n      \"Effect\": \"Allow\"\n    }\n  ]\n}"}, "ecs-app-QuotingJobProcessor_task-role-policy_0530070F": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-QuotingJobProcessor/task-role-policy", "uniqueId": "ecs-app-QuotingJobProcessor_task-role-policy_0530070F"}}, "policy": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Action\": \"s3:ListBucket\",\n      \"Resource\": \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketGoServiceProfiles_bucket_6F102610bucket}\",\n      \"Effect\": \"Allow\"\n    },\n    {\n      \"Action\": [\n        \"s3:GetObject\",\n        \"s3:PutObject\"\n      ],\n      \"Resource\": \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketGoServiceProfiles_bucket_6F102610bucket}/*\",\n      \"Effect\": \"Allow\"\n    }\n  ]\n}"}, "ecs-app-SafetyJobProcessor_task-role-policy_2DC040BB": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-SafetyJobProcessor/task-role-policy", "uniqueId": "ecs-app-SafetyJobProcessor_task-role-policy_2DC040BB"}}, "policy": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Action\": \"s3:ListBucket\",\n      \"Resource\": \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketFmcsaPublicFiles_bucket_29109011bucket}\",\n      \"Effect\": \"Allow\"\n    },\n    {\n      \"Action\": [\n        \"s3:GetObject\",\n        \"s3:PutObject\"\n      ],\n      \"Resource\": \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketFmcsaPublicFiles_bucket_29109011bucket}/*\",\n      \"Effect\": \"Allow\"\n    },\n    {\n      \"Action\": \"s3:ListBucket\",\n      \"Resource\": \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketGoServiceProfiles_bucket_6F102610bucket}\",\n      \"Effect\": \"Allow\"\n    },\n    {\n      \"Action\": [\n        \"s3:GetObject\",\n        \"s3:PutObject\"\n      ],\n      \"Resource\": \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketGoServiceProfiles_bucket_6F102610bucket}/*\",\n      \"Effect\": \"Allow\"\n    },\n    {\n      \"Action\": \"s3:ListBucket\",\n      \"Resource\": \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketFmcsaSftpDownloads_bucket_035D7C25bucket}\",\n      \"Effect\": \"Allow\"\n    },\n    {\n      \"Action\": [\n        \"s3:GetObject\",\n        \"s3:PutObject\"\n      ],\n      \"Resource\": \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketFmcsaSftpDownloads_bucket_035D7C25bucket}/*\",\n      \"Effect\": \"Allow\"\n    },\n    {\n      \"Action\": \"s3:ListBucket\",\n      \"Resource\": \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketJobberSchedules_bucket_C51DBF80bucket}\",\n      \"Effect\": \"Allow\"\n    },\n    {\n      \"Action\": [\n        \"s3:GetObject\",\n        \"s3:PutObject\"\n      ],\n      \"Resource\": \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketJobberSchedules_bucket_C51DBF80bucket}/*\",\n      \"Effect\": \"Allow\"\n    },\n    {\n      \"Action\": \"s3:ListBucket\",\n      \"Resource\": \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketDatagov_bucket_7AD2F144bucket}\",\n      \"Effect\": \"Allow\"\n    },\n    {\n      \"Action\": [\n        \"s3:GetObject\",\n        \"s3:PutObject\"\n      ],\n      \"Resource\": \"arn:aws:s3:::${data.terraform_remote_state.cross-stack-reference-input-default-staging-backend-env-scoped-buckets-us-east-2.outputs.cross-stack-output-aws_s3_bucketDatagov_bucket_7AD2F144bucket}/*\",\n      \"Effect\": \"Allow\"\n    },\n    {\n      \"Action\": [\n        \"ecs:GetTaskProtection\",\n        \"ecs:UpdateTaskProtection\"\n      ],\n      \"Resource\": \"arn:aws:ecs:*:************:task/*/*\",\n      \"Effect\": \"Allow\"\n    }\n  ]\n}"}, "ecs-exec-role-secrets-policy": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-exec-role-secrets-policy", "uniqueId": "ecs-exec-role-secrets-policy"}}, "name": "ecs-exec-role-secrets-policy", "policy": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Action\": \"secretsmanager:GetSecretValue\",\n      \"Resource\": [\n        \"${data.aws_secretsmanager_secret.secret-LaunchDarklyApiKey.arn}\",\n        \"${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_secretsmanager_secret_versionApplicationDB_SecretValue_app-db-migrator-password_3807CF8Barn}\",\n        \"${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_secretsmanager_secret_versionApplicationDB_SecretValue_app-db-readonly-password_D8C00E55arn}\",\n        \"${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_secretsmanager_secret_versionApplicationDB_SecretValue_app-db-readwrite-password_47B2BD30arn}\",\n        \"${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_secretsmanager_secret_versionApplicationDB_SecretValue_app-db-ds-user-password_4F3AA105arn}\",\n        \"${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_secretsmanager_secret_versionApplicationDB_SecretValue_7841D7E4arn}\",\n        \"${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_secretsmanager_secret_versionNhtsaDB_SecretValue_EBD7DE9Earn}\",\n        \"${data.terraform_remote_state.cross-stack-reference-input-default-staging-storage-rds-stack-us-east-2.outputs.cross-stack-output-aws_secretsmanager_secret_versionFmcsaDB_SecretValue_15098EB5arn}\"\n      ],\n      \"Effect\": \"Allow\"\n    }\n  ]\n}"}}, "aws_iam_role": {"ecs-app-ApiServer_task-role_50461A6C": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-ApiServer/task-role", "uniqueId": "ecs-app-ApiServer_task-role_50461A6C"}}, "assume_role_policy": "${data.aws_iam_policy_document.ecs-tasks-assume-role-policy.json}", "name": "api_server_task_role"}, "ecs-app-DistsemServer_task-role_43EEF0C1": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-DistsemServer/task-role", "uniqueId": "ecs-app-DistsemServer_task-role_43EEF0C1"}}, "assume_role_policy": "${data.aws_iam_policy_document.ecs-tasks-assume-role-policy.json}", "name": "distsem_server_task_role"}, "ecs-app-EventJobProcessor_task-role_12B57374": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-EventJobProcessor/task-role", "uniqueId": "ecs-app-EventJobProcessor_task-role_12B57374"}}, "assume_role_policy": "${data.aws_iam_policy_document.ecs-tasks-assume-role-policy.json}", "name": "event_job_processor_task_role"}, "ecs-app-FeatureStoreServer_task-role_B924BED2": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-FeatureStoreServer/task-role", "uniqueId": "ecs-app-FeatureStoreServer_task-role_B924BED2"}}, "assume_role_policy": "${data.aws_iam_policy_document.ecs-tasks-assume-role-policy.json}", "name": "feature_store_server_task_role"}, "ecs-app-GqlApiServer_task-role_B9A03BEA": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-GqlApiServer/task-role", "uniqueId": "ecs-app-GqlApiServer_task-role_B9A03BEA"}}, "assume_role_policy": "${data.aws_iam_policy_document.ecs-tasks-assume-role-policy.json}", "name": "gql_server_task_role"}, "ecs-app-JobProcessor_task-role_F4C86BE2": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-JobProcessor/task-role", "uniqueId": "ecs-app-JobProcessor_task-role_F4C86BE2"}}, "assume_role_policy": "${data.aws_iam_policy_document.ecs-tasks-assume-role-policy.json}", "name": "job_processor_task_role"}, "ecs-app-PdfgenServer_task-role_02744525": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-PdfgenServer/task-role", "uniqueId": "ecs-app-PdfgenServer_task-role_02744525"}}, "assume_role_policy": "${data.aws_iam_policy_document.ecs-tasks-assume-role-policy.json}", "name": "pdfgen_server_task"}, "ecs-app-QuotingJobProcessor_task-role_9501B3AB": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-QuotingJobProcessor/task-role", "uniqueId": "ecs-app-QuotingJobProcessor_task-role_9501B3AB"}}, "assume_role_policy": "${data.aws_iam_policy_document.ecs-tasks-assume-role-policy.json}", "name": "quoting_job_processor_task"}, "ecs-app-SafetyJobProcessor_task-role_558536D0": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-SafetyJobProcessor/task-role", "uniqueId": "ecs-app-SafetyJobProcessor_task-role_558536D0"}}, "assume_role_policy": "${data.aws_iam_policy_document.ecs-tasks-assume-role-policy.json}", "name": "safety_job_processor_task_role"}}, "aws_iam_role_policy_attachment": {"ecs-app-ApiServer_task-role-policy-attachment_1BFDA2B3": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-ApiServer/task-role-policy-attachment", "uniqueId": "ecs-app-ApiServer_task-role-policy-attachment_1BFDA2B3"}}, "policy_arn": "${aws_iam_policy.ecs-app-ApiServer_task-role-policy_788A5015.arn}", "role": "${aws_iam_role.ecs-app-ApiServer_task-role_50461A6C.name}"}, "ecs-app-DistsemServer_task-role-policy-attachment_CAD7ECED": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-DistsemServer/task-role-policy-attachment", "uniqueId": "ecs-app-DistsemServer_task-role-policy-attachment_CAD7ECED"}}, "policy_arn": "${aws_iam_policy.ecs-app-DistsemServer_task-role-policy_A58E15E9.arn}", "role": "${aws_iam_role.ecs-app-DistsemServer_task-role_43EEF0C1.name}"}, "ecs-app-EventJobProcessor_task-role-policy-attachment_10E4EC69": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-EventJobProcessor/task-role-policy-attachment", "uniqueId": "ecs-app-EventJobProcessor_task-role-policy-attachment_10E4EC69"}}, "policy_arn": "${aws_iam_policy.ecs-app-EventJobProcessor_task-role-policy_FD4A1D91.arn}", "role": "${aws_iam_role.ecs-app-EventJobProcessor_task-role_12B57374.name}"}, "ecs-app-FeatureStoreServer_task-role-policy-attachment_4ECC748B": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-FeatureStoreServer/task-role-policy-attachment", "uniqueId": "ecs-app-FeatureStoreServer_task-role-policy-attachment_4ECC748B"}}, "policy_arn": "${aws_iam_policy.ecs-app-FeatureStoreServer_task-role-policy_A03BBD15.arn}", "role": "${aws_iam_role.ecs-app-FeatureStoreServer_task-role_B924BED2.name}"}, "ecs-app-GqlApiServer_task-role-policy-attachment_29B68609": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-GqlApiServer/task-role-policy-attachment", "uniqueId": "ecs-app-GqlApiServer_task-role-policy-attachment_29B68609"}}, "policy_arn": "${aws_iam_policy.ecs-app-GqlApiServer_task-role-policy_09E07EFC.arn}", "role": "${aws_iam_role.ecs-app-GqlApiServer_task-role_B9A03BEA.name}"}, "ecs-app-JobProcessor_task-role-policy-attachment_72028456": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-JobProcessor/task-role-policy-attachment", "uniqueId": "ecs-app-JobProcessor_task-role-policy-attachment_72028456"}}, "policy_arn": "${aws_iam_policy.ecs-app-JobProcessor_task-role-policy_C3870FD5.arn}", "role": "${aws_iam_role.ecs-app-JobProcessor_task-role_F4C86BE2.name}"}, "ecs-app-PdfgenServer_task-role-policy-attachment_101D5611": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-PdfgenServer/task-role-policy-attachment", "uniqueId": "ecs-app-PdfgenServer_task-role-policy-attachment_101D5611"}}, "policy_arn": "${aws_iam_policy.ecs-app-PdfgenServer_task-role-policy_29D298EB.arn}", "role": "${aws_iam_role.ecs-app-PdfgenServer_task-role_02744525.name}"}, "ecs-app-QuotingJobProcessor_task-role-policy-attachment_5AF01375": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-QuotingJobProcessor/task-role-policy-attachment", "uniqueId": "ecs-app-QuotingJobProcessor_task-role-policy-attachment_5AF01375"}}, "policy_arn": "${aws_iam_policy.ecs-app-QuotingJobProcessor_task-role-policy_0530070F.arn}", "role": "${aws_iam_role.ecs-app-QuotingJobProcessor_task-role_9501B3AB.name}"}, "ecs-app-SafetyJobProcessor_task-role-policy-attachment_E2888DFA": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-SafetyJobProcessor/task-role-policy-attachment", "uniqueId": "ecs-app-SafetyJobProcessor_task-role-policy-attachment_E2888DFA"}}, "policy_arn": "${aws_iam_policy.ecs-app-SafetyJobProcessor_task-role-policy_2DC040BB.arn}", "role": "${aws_iam_role.ecs-app-SafetyJobProcessor_task-role_558536D0.name}"}, "ecs-exec-role-secrets-policy-attachment": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-exec-role-secrets-policy-attachment", "uniqueId": "ecs-exec-role-secrets-policy-attachment"}}, "policy_arn": "${aws_iam_policy.ecs-exec-role-secrets-policy.arn}", "role": "ecs-task-execution-role"}}, "aws_route53_record": {"ecs-app-ApiServer_dns_record_829176D1": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-ApiServer/dns_record", "uniqueId": "ecs-app-ApiServer_dns_record_829176D1"}}, "alias": {"evaluate_target_health": true, "name": "${module.ecs-app-ApiServer_load-balancer_56066FC1.dns_name}", "zone_id": "${module.ecs-app-ApiServer_load-balancer_56066FC1.zone_id}"}, "name": "api.staging.nirvanatech.com", "type": "A", "zone_id": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-account-essentials.outputs.cross-stack-output-aws_route53_zonedns_zone_2E2729FBzone_id}"}, "ecs-app-GqlApiServer_dns_record_9C9E0EDD": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-GqlApiServer/dns_record", "uniqueId": "ecs-app-GqlApiServer_dns_record_9C9E0EDD"}}, "alias": {"evaluate_target_health": true, "name": "${module.ecs-app-GqlApiServer_load-balancer_6FCA286B.dns_name}", "zone_id": "${module.ecs-app-GqlApiServer_load-balancer_6FCA286B.zone_id}"}, "name": "graphqlapi.staging.nirvanatech.com", "type": "A", "zone_id": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-account-essentials.outputs.cross-stack-output-aws_route53_zonedns_zone_2E2729FBzone_id}"}}, "aws_security_group_rule": {"ecs-app-ApiServer_allow_alb_ingress_default_FF3CD5DF": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-ApiServer/allow_alb_ingress_default", "uniqueId": "ecs-app-ApiServer_allow_alb_ingress_default_FF3CD5DF"}}, "from_port": 0, "protocol": "-1", "security_group_id": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_security_groupdefault_sgid}", "source_security_group_id": "${module.ecs-app-ApiServer_load-balancer_56066FC1.access_sg_id}", "to_port": 0, "type": "ingress"}, "ecs-app-GqlApiServer_allow_alb_ingress_default_FCCB91A7": {"//": {"metadata": {"path": "default-staging-backend-application-stack-us-east-2/ecs-app-GqlApiServer/allow_alb_ingress_default", "uniqueId": "ecs-app-GqlApiServer_allow_alb_ingress_default_FCCB91A7"}}, "from_port": 0, "protocol": "-1", "security_group_id": "${data.terraform_remote_state.cross-stack-reference-input-default-staging-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_default_security_groupdefault_sgid}", "source_security_group_id": "${module.ecs-app-GqlApiServer_load-balancer_6FCA286B.access_sg_id}", "to_port": 0, "type": "ingress"}}}, "terraform": {"backend": {"s3": {"bucket": "cloud.nirvanatech.com", "key": "private/deployment/cdktf/default/staging/default-staging-backend-application-stack-us-east-2.json", "region": "us-east-2"}}, "required_providers": {"aws": {"source": "aws", "version": "5.88.0"}}, "required_version": "1.7.5"}, "variable": {"api_server_tag": {"description": "The tag of the ApiServer image to be used to deploy", "nullable": false, "type": "string"}, "distsem_server_tag": {"description": "The tag of the DistsemServer image to be used to deploy", "nullable": false, "type": "string"}, "event_job_processor_tag": {"description": "The tag of the EventJobProcessor image to be used to deploy", "nullable": false, "type": "string"}, "feature_store_server_tag": {"description": "The tag of the FeatureStoreServer image to be used to deploy", "nullable": false, "type": "string"}, "graphql_server_tag": {"description": "The tag of the GqlApiServer image to be used to deploy", "nullable": false, "type": "string"}, "job_processor_tag": {"description": "The tag of the JobProcessor image to be used to deploy", "nullable": false, "type": "string"}, "jobber_monitor_tag": {"description": "The tag of the JobberMonitor image to be used to deploy", "nullable": false, "type": "string"}, "pdfgen_server_tag": {"description": "The tag of the PdfgenServer image to be used to deploy", "nullable": false, "type": "string"}, "quoting_job_processor_tag": {"description": "The tag of the QuotingJobProcessor image to be used to deploy", "nullable": false, "type": "string"}, "safety_job_processor_tag": {"description": "The tag of the SafetyJobProcessor image to be used to deploy", "nullable": false, "type": "string"}}}