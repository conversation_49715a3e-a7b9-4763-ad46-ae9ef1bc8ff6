{"//": {"metadata": {"backend": "s3", "overrides": {"stack": ["terraform"]}, "stackName": "default-staging-backend-kms-keys-us-east-2", "version": "0.20.10"}, "outputs": {"default-staging-backend-kms-keys-us-east-2": {"cross-stack-output-aws_kms_key.TelematicsApiKeyCredentials_key_18191D20.arn": "cross-stack-output-aws_kms_keyTelematicsApiKeyCredentials_key_18191D20arn", "cross-stack-output-aws_kms_key.TelematicsConsentUserRequestMetadata_key_D8A98242.arn": "cross-stack-output-aws_kms_keyTelematicsConsentUserRequestMetadata_key_D8A98242arn", "cross-stack-output-aws_kms_key.TerminalUserCredentials_key_39CA3536.arn": "cross-stack-output-aws_kms_keyTerminalUserCredentials_key_39CA3536arn", "cross-stack-output-aws_kms_key.TspWebhookSecretKeysCredentials_key_8AC8CC68.arn": "cross-stack-output-aws_kms_keyTspWebhookSecretKeysCredentials_key_8AC8CC68arn", "cross-stack-output-aws_kms_key.UserSsnLastFour_key_5875D18B.arn": "cross-stack-output-aws_kms_keyUserSsnLastFour_key_5875D18Barn", "cross-stack-output-aws_kms_key.UserSsn_key_748A9B95.arn": "cross-stack-output-aws_kms_keyUserSsn_key_748A9B95arn"}}}, "output": {"cross-stack-output-aws_kms_keyTelematicsApiKeyCredentials_key_18191D20arn": {"sensitive": true, "value": "${aws_kms_key.TelematicsApiKeyCredentials_key_18191D20.arn}"}, "cross-stack-output-aws_kms_keyTelematicsConsentUserRequestMetadata_key_D8A98242arn": {"sensitive": true, "value": "${aws_kms_key.TelematicsConsentUserRequestMetadata_key_D8A98242.arn}"}, "cross-stack-output-aws_kms_keyTerminalUserCredentials_key_39CA3536arn": {"sensitive": true, "value": "${aws_kms_key.TerminalUserCredentials_key_39CA3536.arn}"}, "cross-stack-output-aws_kms_keyTspWebhookSecretKeysCredentials_key_8AC8CC68arn": {"sensitive": true, "value": "${aws_kms_key.TspWebhookSecretKeysCredentials_key_8AC8CC68.arn}"}, "cross-stack-output-aws_kms_keyUserSsnLastFour_key_5875D18Barn": {"sensitive": true, "value": "${aws_kms_key.UserSsnLastFour_key_5875D18B.arn}"}, "cross-stack-output-aws_kms_keyUserSsn_key_748A9B95arn": {"sensitive": true, "value": "${aws_kms_key.UserSsn_key_748A9B95.arn}"}}, "provider": {"aws": [{"allowed_account_ids": ["************"], "assume_role": [{"role_arn": "arn:aws:iam::************:role/AdministratorAccessForManagementAccountUsers"}], "default_tags": [{"tags": {"environment": "staging", "group": "backend", "infraWorkspace": "default", "region": "us-east-2", "stackName": "kms-keys"}}], "region": "us-east-2"}]}, "resource": {"aws_kms_alias": {"FmcsaCredentials_alias_1CC2D144": {"//": {"metadata": {"path": "default-staging-backend-kms-keys-us-east-2/FmcsaCredentials/alias", "uniqueId": "FmcsaCredentials_alias_1CC2D144"}}, "name": "alias/default-fmcsa-credentials", "target_key_id": "${aws_kms_key.FmcsaCredentials_key_089E384E.id}"}, "TelematicsApiKeyCredentials_alias_18E51E7B": {"//": {"metadata": {"path": "default-staging-backend-kms-keys-us-east-2/TelematicsApiKeyCredentials/alias", "uniqueId": "TelematicsApiKeyCredentials_alias_18E51E7B"}}, "name": "alias/default-telematics-api-key-credentials", "target_key_id": "${aws_kms_key.TelematicsApiKeyCredentials_key_18191D20.id}"}, "TelematicsConsentUserRequestMetadata_alias_9B61A021": {"//": {"metadata": {"path": "default-staging-backend-kms-keys-us-east-2/TelematicsConsentUserRequestMetadata/alias", "uniqueId": "TelematicsConsentUserRequestMetadata_alias_9B61A021"}}, "name": "alias/default-telematics-consent-user-request-metadata", "target_key_id": "${aws_kms_key.TelematicsConsentUserRequestMetadata_key_D8A98242.id}"}, "TerminalUserCredentials_alias_C2F18512": {"//": {"metadata": {"path": "default-staging-backend-kms-keys-us-east-2/TerminalUserCredentials/alias", "uniqueId": "TerminalUserCredentials_alias_C2F18512"}}, "name": "alias/default-terminal-user-credentials", "target_key_id": "${aws_kms_key.TerminalUserCredentials_key_39CA3536.id}"}, "TruckerCloudUserCredentials_alias_DDFB6BB2": {"//": {"metadata": {"path": "default-staging-backend-kms-keys-us-east-2/TruckerCloudUserCredentials/alias", "uniqueId": "TruckerCloudUserCredentials_alias_DDFB6BB2"}}, "name": "alias/default-truckercloud-user-credentials", "target_key_id": "${aws_kms_key.TruckerCloudUserCredentials_key_F9C5B70D.id}"}, "TspWebhookSecretKeysCredentials_alias_F4A03C25": {"//": {"metadata": {"path": "default-staging-backend-kms-keys-us-east-2/TspWebhookSecretKeysCredentials/alias", "uniqueId": "TspWebhookSecretKeysCredentials_alias_F4A03C25"}}, "name": "alias/default-tsp-webhook-secret-keys-credentials", "target_key_id": "${aws_kms_key.TspWebhookSecretKeysCredentials_key_8AC8CC68.id}"}, "UserSsnLastFour_alias_FC46F24C": {"//": {"metadata": {"path": "default-staging-backend-kms-keys-us-east-2/UserSsnLastFour/alias", "uniqueId": "UserSsnLastFour_alias_FC46F24C"}}, "name": "alias/default-user-ssn-last-four", "target_key_id": "${aws_kms_key.UserSsnLastFour_key_5875D18B.id}"}, "UserSsn_alias_EB20106E": {"//": {"metadata": {"path": "default-staging-backend-kms-keys-us-east-2/UserSsn/alias", "uniqueId": "UserSsn_alias_EB20106E"}}, "name": "alias/default-user-ssn", "target_key_id": "${aws_kms_key.UserSsn_key_748A9B95.id}"}}, "aws_kms_key": {"FmcsaCredentials_key_089E384E": {"//": {"metadata": {"path": "default-staging-backend-kms-keys-us-east-2/FmcsaCredentials/key", "uniqueId": "FmcsaCredentials_key_089E384E"}}, "description": "The key used to encrypt/decrypt FMCSA DOT `PIN`", "tags": {"alias": "alias/default-fmcsa-credentials"}}, "TelematicsApiKeyCredentials_key_18191D20": {"//": {"metadata": {"path": "default-staging-backend-kms-keys-us-east-2/TelematicsApiKeyCredentials/key", "uniqueId": "TelematicsApiKeyCredentials_key_18191D20"}}, "description": "The key used to encrypt/decrypt `telematics.api_key_credentials`", "tags": {"alias": "alias/default-telematics-api-key-credentials"}}, "TelematicsConsentUserRequestMetadata_key_D8A98242": {"//": {"metadata": {"path": "default-staging-backend-kms-keys-us-east-2/TelematicsConsentUserRequestMetadata/key", "uniqueId": "TelematicsConsentUserRequestMetadata_key_D8A98242"}}, "description": "The key used to encrypt/decrypt telematics consent user request metadata", "tags": {"alias": "alias/default-telematics-consent-user-request-metadata"}}, "TerminalUserCredentials_key_39CA3536": {"//": {"metadata": {"path": "default-staging-backend-kms-keys-us-east-2/TerminalUserCredentials/key", "uniqueId": "TerminalUserCredentials_key_39CA3536"}}, "description": "The key used to encrypt/decrypt user credentials for Terminal (withterminal.com)", "tags": {"alias": "alias/default-terminal-user-credentials"}}, "TruckerCloudUserCredentials_key_F9C5B70D": {"//": {"metadata": {"path": "default-staging-backend-kms-keys-us-east-2/TruckerCloudUserCredentials/key", "uniqueId": "TruckerCloudUserCredentials_key_F9C5B70D"}}, "description": "The key used to encrypt/decrypt TruckerCloud `user_credentials`", "tags": {"alias": "alias/default-truckercloud-user-credentials"}}, "TspWebhookSecretKeysCredentials_key_8AC8CC68": {"//": {"metadata": {"path": "default-staging-backend-kms-keys-us-east-2/TspWebhookSecretKeysCredentials/key", "uniqueId": "TspWebhookSecretKeysCredentials_key_8AC8CC68"}}, "description": "The key used to encrypt/decrypt TSP webhook secret keys", "tags": {"alias": "alias/default-tsp-webhook-secret-keys-credentials"}}, "UserSsnLastFour_key_5875D18B": {"//": {"metadata": {"path": "default-staging-backend-kms-keys-us-east-2/UserSsnLastFour/key", "uniqueId": "UserSsnLastFour_key_5875D18B"}}, "description": "The key used to encrypt/decrypt User SSN last four characters", "tags": {"alias": "alias/default-user-ssn-last-four"}}, "UserSsn_key_748A9B95": {"//": {"metadata": {"path": "default-staging-backend-kms-keys-us-east-2/UserSsn/key", "uniqueId": "UserSsn_key_748A9B95"}}, "description": "The key used to encrypt/decrypt User SSN", "tags": {"alias": "alias/default-user-ssn"}}}}, "terraform": {"backend": {"s3": {"bucket": "cloud.nirvanatech.com", "key": "private/deployment/cdktf/default/staging/default-staging-backend-kms-keys-us-east-2.json", "region": "us-east-2"}}, "required_providers": {"aws": {"source": "aws", "version": "5.88.0"}}, "required_version": "1.7.5"}}