import { Construct } from 'constructs'

import { <PERSON>ms<PERSON><PERSON>s } from '@cdktf/provider-aws/lib/kms-alias'
import { Kms<PERSON>ey } from '@cdktf/provider-aws/lib/kms-key'

/**
 * Configuration for a Nirvana KMS Key.
 */
export interface NvKmsKeyProps {
  /**
   * The friendly alias name for the key (without the `alias/` prefix).
   * The construct will create an alias named `alias/<keyname>`.
   */
  keyname: string

  /**
   * Optional tags to apply to the key.
   */
  tags?: Record<string, string>

  /**
   * Optional description for the key.
   */
  description?: string
}

/**
 * A construct that creates an AWS KMS Key and corresponding Alias.
 *
 * This provides a simple interface for provisioning a customer-managed KMS key
 * with an alias and retrieving its identifying attributes.
 */
export class NvKmsKey extends Construct {
  private readonly _key: KmsKey
  private readonly _alias: KmsAlias

  constructor(scope: Construct, id: string, props: NvKmsKeyProps) {
    super(scope, id)

    // Create the KMS Key
    this._key = new KmsKey(this, 'key', {
      description: props.description,
      tags: {
        ...props.tags,
        alias: `alias/${props.keyname}`,
      },
    })

    // Create the KMS Alias pointing to the key
    this._alias = new KmsAlias(this, 'alias', {
      name: `alias/${props.keyname}`,
      // In Terraform, the resource id for aws_kms_key is the key_id, which is accepted by KMS Alias
      targetKeyId: this._key.id,
    })
  }

  /**
   * Gets the ARN of the KMS key.
   */
  public get arn(): string {
    return this._key.arn
  }

  /**
   * Gets the alias name for the KMS key (e.g., `alias/<keyname>`).
   */
  public get name(): string {
    return this._alias.name
  }
}
