import { Construct } from 'constructs'

import { BaseStack, BaseStackProps } from '@nvinfra/common/constructs/stacks'

import { NvKmsKey, NvKmsKeyProps } from '../nv-kms-key'

/**
 * NvKmsKeyStackProps is the set of properties required to create an
 * NvKmsKeyStack.
 *
 * @template KeyName - String literal type representing the identifiers for the
 * keys to be created.
 */
export interface NvKmsKeyStackProps<KeyName extends string>
  extends BaseStackProps {
  /**
   * The configuration for the KMS keys to create, keyed by the logical key id.
   * The key id is used as the construct id and to access the created key from
   * {@link NvKmsKeyStack.keys}.
   */
  keys: Record<KeyName, NvKmsKeyProps>
}

/**
 * NvKmsKeyStack provisions multiple customer-managed AWS KMS keys with
 * corresponding aliases.
 *
 * For each entry under {@link NvKmsKeyStackProps.keys}, the stack creates an
 * {@link NvKmsKey} construct and exposes the created resources via
 * {@link NvKmsKeyStack.keys} for convenient access to attributes like `arn` and
 * alias `name` (e.g., `alias/<keyname>`).
 *
 * @template KeyName - String literal type representing the identifiers for the
 * keys to be created and accessed.
 *
 * @example
 * const kmsStack = new NvKmsKeyStack<'data' | 'logs'>(app, 'security', {
 *   group: 'coreinfra',
 *   environment: Environment.Production,
 *   region: 'us-east-2',
 *   keys: {
 *     data: { keyname: 'nv-data', description: 'Application data key' },
 *     logs: { keyname: 'nv-logs' },
 *   },
 * })
 *
 * // Access created keys
 * const dataKeyArn = kmsStack.keys.data.arn
 * const logsKeyAlias = kmsStack.keys.logs.name
 */
export class NvKmsKeyStack<KeyName extends string> extends BaseStack {
  /** A record of created KMS keys indexed by the provided key ids. */
  public readonly keys: Record<KeyName, NvKmsKey>

  constructor(
    scope: Construct,
    id: string,
    props: NvKmsKeyStackProps<KeyName>,
  ) {
    super(scope, id, props)

    this.keys = Object.fromEntries(
      Object.entries<NvKmsKeyProps>(props.keys).map(([keyId, keyProps]) => [
        keyId,
        new NvKmsKey(this, keyId, keyProps),
      ]),
    ) as Record<KeyName, NvKmsKey>
  }
}
