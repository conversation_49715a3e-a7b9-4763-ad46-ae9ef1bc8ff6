"""HTTP API handlers.

This module provides route handlers for the Claims Agent service.
"""

import uuid
from datetime import datetime, timezone
from typing import Literal

from fastapi_injector import Injected
from fastapi import APIRouter, Depends, HTTPException, Request, Response, status
from loguru import logger
from pydantic import BaseModel

from claims_agent.api.auth import get_formatted_mcp_authorization
from claims_agent.api.errors import (
    ClaimsAgentError,
)
from claims_agent.api.services import ClaimsServiceProtocol
from claims_agent.config import Settings
from claims_agent.instrumentation.helpers import (
    set_span_attributes,
    trace_async_function,
)
from claims_agent.interfaces.repositories import (
    CoverageDeterminationRequestRepositoryProtocol,
    CreateCoverageDeterminationRequest,
    CoverageDeterminationError,
    CoverageRunRepositoryProtocol,
)

from nirvana_commons.auth import get_user_email

from claims_agent.interfaces.legacy_models import (
    CoverageDeterminationResponse as LegacyCoverageDeterminationResponse,
)
from claims_agent.models.feedback import (
    CoverageRunWithNotes,
    UpsertFeedbackRequest,
    FeedbackResponse,
)


router = APIRouter()


class HealthStatus(BaseModel):
    """Model for detailed health status of the service."""

    status: Literal["healthy", "unhealthy"]
    agent_status: Literal["initialized", "not_initialized", "error"]
    details: str | None = None


@router.get(
    "/health",
    tags=["health"],
)
async def health_check(
    service: ClaimsServiceProtocol = Injected(ClaimsServiceProtocol),  # type: ignore [type-abstract]
) -> HealthStatus:
    """Perform a health check on the service.

    This endpoint checks if the agent is initialized and running correctly.
    The response body always conforms to the `HealthStatus` schema, and the
    HTTP status code varies based on the actual health status:

    - **200 OK**: Returned when the service is healthy.
    - **503 Service Unavailable**: Returned when the service is unhealthy.

    Returns:
        A `HealthStatus` object with detailed service status.
    """
    logger.info("Health check endpoint called")

    try:
        is_healthy, details = await service.health_check()

        if is_healthy:
            return HealthStatus(
                status="healthy",
                agent_status="initialized",
                details=details,
            )
        else:
            # Return 503 status code for unhealthy service
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail=details,
            )
    except HTTPException:
        # Re-raise HTTP exceptions (like 503) as-is
        raise
    except Exception as e:
        logger.exception(f"Unexpected error during health check: {str(e)}")
        # Return 503 for unexpected errors
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Health check failed: {str(e)}",
        )


@router.get(
    "/claims/{claim_id}/coverage/determination",
    tags=["claims"],
    summary="Get Coverage Determination",
)
@trace_async_function(name="http.get_coverage_determination")
async def get_coverage_determination(
    claim_id: str,
    request: Request,
    response: Response,
    settings: Settings = Injected(Settings),
    service: ClaimsServiceProtocol = Injected(ClaimsServiceProtocol),  # type: ignore [type-abstract]
    request_repository: CoverageDeterminationRequestRepositoryProtocol = Injected(
        CoverageDeterminationRequestRepositoryProtocol  # type: ignore [type-abstract]
    ),
    formatted_mcp_auth: str | None = Depends(get_formatted_mcp_authorization),  # noqa: B008
    as_of_date: datetime | None = None,
    force_refresh: bool = False,
    ttl_seconds: int | None = None,
) -> LegacyCoverageDeterminationResponse:
    """Analyzes claim coverage based on policy details with caching support.

    By default, it will return cached results if available and within the TTL window.

    Args:
        claim_id: The ID of the claim to analyze.
        response: FastAPI Response object for setting headers (injected).
        service: The claims service for coverage determination (injected).
        request_repository: The request repository for coverage determination (injected).
        formatted_mcp_auth: Formatted MCP Authorization header value (injected).
        as_of_date: Optional date to perform coverage determination as of.
        force_refresh: If True, skip cache lookup and force a new determination.
        ttl_seconds: Override the default TTL for cache entries. Cannot exceed max TTL.

    Returns:
        A CoverageDeterminationResponse object with verification results.
    """
    logger.info(f"Received coverage determination request for claim_id: {claim_id}")

    # Set span attributes for tracing
    set_span_attributes(
        {
            "claim_id": claim_id,
            "force_refresh": force_refresh,
            "ttl_seconds": ttl_seconds,
            "as_of_date": as_of_date.isoformat() if as_of_date else None,
        }
    )

    if as_of_date:
        logger.info(
            f"Using as_of_date: {as_of_date.isoformat()} for coverage determination"
        )

    # Configure cache settings early to ensure headers are always set
    # Note: ttl_seconds=0 is a valid value that disables caching, so we use `is not None`
    effective_ttl_seconds = (
        ttl_seconds
        if ttl_seconds is not None
        else settings.COVERAGE_DETERMINATION_TTL_SECONDS
    )

    # Validate TTL
    if (
        settings.COVERAGE_DETERMINATION_CACHE_ENABLED
        and effective_ttl_seconds > settings.COVERAGE_DETERMINATION_TTL_SECONDS
    ):
        response.headers["X-Cache-Status"] = "ERROR"
        response.headers["X-Cache-TTL"] = str(effective_ttl_seconds)
        request.state.cache_headers = {
            "X-Cache-Status": "ERROR",
            "X-Cache-TTL": str(effective_ttl_seconds),
        }
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"TTL cannot exceed {settings.COVERAGE_DETERMINATION_TTL_SECONDS} seconds",
        )

    # TODO: Implement stronger validation for claim_id format (e.g., regex) if a specific pattern is known.
    if not claim_id or len(claim_id) < 3:  # Basic check, can be improved
        response.headers["X-Cache-Status"] = "ERROR"
        response.headers["X-Cache-TTL"] = str(effective_ttl_seconds)
        request.state.cache_headers = {
            "X-Cache-Status": "ERROR",
            "X-Cache-TTL": str(effective_ttl_seconds),
        }
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid claim ID format",
        )

    # Cache lookup (skip if force_refresh or caching disabled)
    if (
        settings.COVERAGE_DETERMINATION_CACHE_ENABLED
        and not force_refresh
        and effective_ttl_seconds > 0
    ):
        try:
            cached_response = await request_repository.get_latest_successful(
                claim_id=claim_id,
                max_age_seconds=effective_ttl_seconds,
            )
            if cached_response:
                logger.info(f"Cache HIT for claim_id: {claim_id}")
                response.headers["X-Cache-Status"] = "HIT"
                response.headers["X-Cache-TTL"] = str(effective_ttl_seconds)
                request.state.cache_headers = {
                    "X-Cache-Status": "HIT",
                    "X-Cache-TTL": str(effective_ttl_seconds),
                }
                set_span_attributes({"cache_status": "HIT"})
                return cached_response
        except Exception as cache_error:
            # Log cache lookup failures but continue with normal flow
            logger.warning(
                f"Cache lookup failed for claim_id: {claim_id}, error: {str(cache_error)}"
            )

    # Cache miss or disabled - proceed with normal flow
    cache_status = "BYPASS" if force_refresh else "MISS"
    logger.info(f"Cache {cache_status} for claim_id: {claim_id}")
    response.headers["X-Cache-Status"] = cache_status
    response.headers["X-Cache-TTL"] = str(effective_ttl_seconds)
    set_span_attributes({"cache_status": cache_status})

    # Store cache headers in request state for exception handler
    request.state.cache_headers = {
        "X-Cache-Status": cache_status,
        "X-Cache-TTL": str(effective_ttl_seconds),
    }

    request_id = uuid.uuid4()

    try:
        await request_repository.insert(
            CreateCoverageDeterminationRequest(
                request_id=request_id,
                claim_id=claim_id,
                workflow_id=f"dummy-workflow-{request_id}",
                timestamp=datetime.now(timezone.utc),
            )
        )

        await request_repository.set_in_progress(request_id, datetime.now(timezone.utc))

        coverage_response = await service.determine_coverage(
            claim_id=claim_id,
            authorization=formatted_mcp_auth,
            as_of_date=as_of_date,
        )
        await request_repository.set_succeeded(
            request_id, datetime.now(timezone.utc), coverage_response
        )
        return coverage_response
    except ClaimsAgentError as e:
        # Update cache status to ERROR in request state
        request.state.cache_headers["X-Cache-Status"] = "ERROR"
        try:
            await request_repository.set_failed(
                request_id,
                datetime.now(timezone.utc),
                CoverageDeterminationError(
                    kind=e.__class__.__name__,
                    message=str(e),
                ),
            )
        except Exception as repo_error:
            # Log repository error but still raise the original ClaimsAgentError
            logger.exception(f"Failed to record failure status: {str(repo_error)}")
        # Let the custom exception handler deal with ClaimsAgentError
        raise
    except Exception as e:
        # The determine_coverage method raises exceptions on failure
        logger.exception(f"Error during coverage determination: {str(e)}")
        # Cache headers already set above - just update status to ERROR
        response.headers["X-Cache-Status"] = "ERROR"
        try:
            await request_repository.set_failed(
                request_id,
                datetime.now(timezone.utc),
                CoverageDeterminationError(
                    kind=e.__class__.__name__,
                    message=str(e),
                ),
            )
        except Exception as repo_error:
            # Log repository error but still raise HTTP exception
            logger.exception(f"Failed to record failure status: {str(repo_error)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An error occurred during coverage determination: {str(e)}",
        ) from e


@router.get(
    "/v2/claims/{claim_id}/coverage/determination",
    tags=["claims"],
    summary="Get Coverage Determination with Storage",
)
@trace_async_function(name="http.get_coverage_determination_v2")
async def get_coverage_determination_v2(
    claim_id: str,
    service: ClaimsServiceProtocol = Injected(ClaimsServiceProtocol),  # type: ignore [type-abstract]
    formatted_mcp_auth: str | None = Depends(get_formatted_mcp_authorization),  # noqa: B008
    as_of_date: datetime | None = None,
) -> CoverageRunWithNotes:
    """Analyzes claim coverage and stores results in the database.

    This endpoint performs coverage determination and stores the results
    in the database for later feedback processing.

    Args:
        claim_id: The ID of the claim to analyze.
        service: The claims service for coverage determination (injected).
        formatted_mcp_auth: Formatted MCP Authorization header value (injected).
        as_of_date: Optional date to perform coverage determination as of.

    Returns:
        A CoverageRunWithNotes object with the stored results.
    """
    logger.info(f"Received coverage determination v2 request for claim_id: {claim_id}")

    # Set span attributes for tracing
    set_span_attributes(
        {
            "claim_id": claim_id,
            "as_of_date": as_of_date.isoformat() if as_of_date else None,
        }
    )

    # Input validation
    if not claim_id or len(claim_id) < 3:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid claim ID format. Claim ID must be at least 3 characters long.",
        )

    if as_of_date and as_of_date > datetime.now(timezone.utc):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="as_of_date cannot be in the future.",
        )

    # TODO: Add caching here
    try:
        coverage_run = await service.determine_coverage_with_storage(
            claim_id=claim_id,
            created_by=get_user_email() or "system",
            authorization=formatted_mcp_auth,
            as_of_date=as_of_date,
            trace_id=None,
        )
        return coverage_run
    except ClaimsAgentError as e:
        # Handle specific Claims Agent errors (agent not initialized, auth failures, etc.)
        logger.error(f"ClaimsAgentError during coverage determination v2: {str(e)}")
        # Let the custom exception handler deal with ClaimsAgentError
        raise
    except ValueError as e:
        # Handle invalid input values (e.g., agent response validation failures)
        logger.error(f"Value error during coverage determination v2: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid input: {str(e)}",
        ) from e
    except Exception as e:
        # Handle any other unexpected errors
        logger.exception(f"Unexpected error during coverage determination v2: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred during coverage determination: {str(e)}",
        ) from e


@router.get(
    "/v2/claims/{claim_id}/coverage/determination/cached",
    tags=["claims"],
    summary="Get Cached Coverage Determinations",
)
@trace_async_function(name="http.get_cached_coverage_determinations")
async def get_cached_coverage_determinations(
    claim_id: str,
    coverage_run_repository: CoverageRunRepositoryProtocol = Injected(
        CoverageRunRepositoryProtocol  # type: ignore [type-abstract]
    ),
    runs: int = 1,
) -> list[CoverageRunWithNotes]:
    """Get the latest successful coverage determination runs from cache.

    This endpoint returns previously computed coverage determinations
    stored in the database, without performing new analysis.

    Args:
        claim_id: The ID of the claim to get cached results for.
        coverage_run_repository: The coverage run repository (injected).
        runs: Number of runs to fetch (default: 1, minimum: 1, maximum: 10).

    Returns:
        A list of CoverageRunWithNotes objects with the cached results.
    """
    logger.info(
        f"Received cached coverage determination request for claim_id: {claim_id}, runs: {runs}"
    )

    # Set span attributes for tracing
    set_span_attributes(
        {
            "claim_id": claim_id,
            "runs_requested": runs,
        }
    )

    # Input validation
    if not claim_id or len(claim_id) < 3:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid claim ID format. Claim ID must be at least 3 characters long.",
        )

    if runs < 1:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Number of runs must be at least 1.",
        )

    if runs > 10:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Number of runs cannot exceed 10.",
        )

    try:
        cached_runs = (
            await coverage_run_repository.get_latest_successful_runs_for_claim(
                claim_id=claim_id,
                limit=runs,
            )
        )

        logger.info(f"Found {len(cached_runs)} cached runs for claim_id: {claim_id}")
        set_span_attributes({"cached_runs_found": len(cached_runs)})
        return cached_runs

    except Exception as e:
        logger.exception(
            f"Unexpected error retrieving cached coverage determinations: {str(e)}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred retrieving cached results: {str(e)}",
        ) from e


@router.post(
    "/v2/claims/coverage/feedback",
    tags=["claims"],
    summary="Submit Coverage Feedback",
)
async def upsert_coverage_feedback(
    feedback_request: UpsertFeedbackRequest,
    service: ClaimsServiceProtocol = Injected(ClaimsServiceProtocol),  # type: ignore [type-abstract]
) -> FeedbackResponse:
    """Submit feedback for coverage notes.

    This endpoint allows users to submit feedback for multiple coverage notes
    in a single request. The feedback is upserted (inserted or updated) for each note.

    Args:
        feedback_request: The feedback request containing feedback for notes.
        service: The claims service for feedback operations (injected).

    Returns:
        A FeedbackResponse with the updated note IDs.
    """
    logger.info(f"Received feedback request for {len(feedback_request.feedback)} notes")

    try:
        feedback_result = await service.submit_feedback(
            feedback_list=feedback_request.feedback,
            updated_by=get_user_email() or "system",
        )

        return feedback_result
    except ValueError as e:
        # Handle invalid input values (e.g., invalid feedback data, validation errors)
        logger.error(f"Invalid input during feedback submission: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid input: {str(e)}",
        ) from e
    except Exception as e:
        # Handle any other unexpected errors
        logger.exception(f"Unexpected error during feedback submission: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred during feedback submission: {str(e)}",
        ) from e
