import os

import requests
import streamlit as st

from boards import st_utils
from utils.constants import Constants

# Authentication constants
COOKIE_SESSION_TOKEN = "nirvana__auth_token"
COOKIE_CLERK_JWT = "nirvana__auth_jwt"
HEADER_JSESSION = "JSESSIONID"
HEADER_CLERK_AUTH = "CLERK-AUTHORIZATION"


def app():
    with st.form("login_form"):
        st.title("Login")
        email = st.text_input("Email", value="@nirvanatech.com")
        password = st.text_input("Password", type="password")
        bt = st.form_submit_button("Login")
        if bt:
            login(email, password)


def login(email, password):
    with st.spinner("Logging in..."):
        data = {"email": email, "password": password}
        r = requests.post(url=Constants.API_SERVER_ENDPOINT + "/auth/login", json=data)
    if r.status_code == 200:
        res = r.json()
        st.session_state.session_id = res["sessionId"]
        st.query_params.session_id = res["sessionId"]
        st.success("Login successful.")
        st.rerun()
    else:
        st.error("Login failed.")


def logout():
    query_params = st.query_params.to_dict()
    query_params.pop("session_id", None)
    st.session_state.session_id = None
    st.session_state.clerk_token = None
    st.session_state.headers = None
    st.query_params.from_dict(query_params)
    st.success("Logout successful.")


def _get_auth_credentials():
    """Extract authentication credentials from various sources.

    Returns:
        tuple: (session_id, clerk_token) where one will be None
    """
    if COOKIE_CLERK_JWT in st.context.cookies:
        return None, st.context.cookies[COOKIE_CLERK_JWT]

    if COOKIE_SESSION_TOKEN in st.context.cookies:
        return st.context.cookies[COOKIE_SESSION_TOKEN], None

    # Check query params
    if "session_id" in st.query_params:
        return st.query_params["session_id"], None

    # Check environment variable as fallback
    if "session_id" not in st.session_state:
        default_session = os.environ.get("DEFAULT_SESSION_ID")
        if default_session:
            return default_session, None

    return None, None


def _build_auth_headers(session_id=None, clerk_token=None):
    """Build authentication headers based on available credentials.

    Args:
        session_id: JSESSIONID value
        clerk_token: Clerk JWT token

    Returns:
        dict: Headers dictionary with appropriate auth header
    """
    if session_id:
        st.session_state.session_id = session_id
        return {HEADER_JSESSION: session_id}
    elif clerk_token:
        st.session_state.clerk_token = clerk_token
        return {HEADER_CLERK_AUTH: f"Bearer {clerk_token}"}
    return {}


def is_logged_in():
    """Check if user is authenticated via JSESSIONID or Clerk JWT.

    Returns:
        bool: True if authenticated, False otherwise
    """
    session_id, clerk_token = _get_auth_credentials()

    # No credentials found
    if not session_id and not clerk_token:
        return False

    # Build and store headers
    headers = _build_auth_headers(session_id, clerk_token)
    st.session_state.headers = headers

    # Verify authentication with backend
    try:
        response = requests.get(
            url=Constants.API_SERVER_ENDPOINT + "/me",
            headers=headers
        )
        return response.status_code == 200
    except requests.RequestException:
        return False
