import copy
import time
import warnings
import numpy as np
import pandas as pd
from typing import Any

import streamlit as st

from apps.uw_board import app_header, show_summary
from boards import st_utils

import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

from utils import api

import telemetry.api.logging as logging

from utils.constants import Constants
from utils.file_utils import filter_s3_files

from boards.basic_info import prepare_fleet_level_information_metaflow

from telematics_utils.config import FilesConfig
from telematics_utils.vin.read_data import read_vin_data

from telematics_utils.helpers.math import series_to_list

from boards.visualisation.fleet import recommend_vin_streamlit
from boards.visualisation.garage import update_vin_garage_table

from boards.pipeline import get_metaflow_run

from data_quality.fleet_data_quality import prepare_fleet_data_quality

log = logging.getLogger(__name__)

warnings.filterwarnings("ignore")


def app():
    """
    Streamlit tab for UW self serve
    """
    app_review = app_header("Data Science Self Service")
    st_utils.break_lines(1)

    connection_id = app_review.get("summary", dict({})).get("tspConnectionHandleId")
    if connection_id is None:
        st.error("Connection Handle Name not found")
        return

    pipeline = get_metaflow_run(connection_id=connection_id)
    if pipeline is None:
        st.error("{} | Did not find a valid metaflow run".format(connection_id))
        return

    tsp = pipeline.data.tsp
    if tsp is None:
        st.error("TSP Name not found")
        return

    show_summary(app_review, tsp)
    show_self_service_page(app_review, pipeline, tsp)


def show_self_service_page(app_review: dict, pipeline: Any, tsp: str, container: Any = st):
    """
    Telematics widgets

    Parameters
    ----------
    app_review  : dict
        Application information from application server
    pipeline : Any
        Metaflow pipeline run object
    tsp : str
        TSP name
    container : Any
        Streamlit object

    """

    log.info("Processing application", app_review_id=app_review["summary"]["appReviewID"])
    company_name = app_review["summary"]["companyName"]
    connection_id = app_review["summary"]["tspConnectionHandleId"]

    vin_list = pipeline.data.vins

    if vin_list.shape[0] == 0:
        st.error("No valid VINs in telematics data folder")
        return None

    with container.expander("Filter and Compare by VINs", expanded=True):
        st_utils.break_lines(1)
        with container.form("compare_by_vin_form"):
            selected_vin_list = st.multiselect("Select VIN Group [Default = All]", vin_list.tolist())
            st.form_submit_button("Submit")
    st_utils.break_lines(1)

    # Fall back to fleet level in case where no VINs are selected

    if len(selected_vin_list) == 0:
        selected_vin_list = vin_list.tolist()

    # Get fleet metrics

    start_time = time.time()
    fleet_metric = prepare_fleet_level_information_metaflow(
        pipeline=pipeline, vin_list=vin_list, vin_group_1=selected_vin_list
    )
    time_taken = np.round(time.time() - start_time, 3)
    log.info("Fetched fleet metrics", connection_id=connection_id, time_taken=time_taken)

    container.markdown("## Download Mileage")
    with container.expander("Download Mileage", expanded=True):
        st_utils.break_lines(1)

        mode_col, _, slider_col, _ = st.columns([1, 0.2, 6, 0.2])
        mode = mode_col.selectbox("Select Mileage Granularity", Constants.MILEAGE_MODE)

        if mode == "Week":
            mileage_view = copy.deepcopy(fleet_metric.get("week_mileage"))
        elif mode == "Day":
            mileage_view = copy.deepcopy(fleet_metric.get("day_mileage"))
        elif mode == "Month":
            mileage_view = copy.deepcopy(fleet_metric.get("month_mileage"))

        list_of_days = mileage_view[mode.lower()].unique().tolist()
        list_of_days.sort()
        start_date, end_date = slider_col.select_slider(
            "Select timeframe for the map", options=list_of_days, value=(list_of_days[0], list_of_days[-1])
        )

        condn = (
            (mileage_view[mode.lower()] >= start_date)
            & (mileage_view[mode.lower()] <= end_date)
            & (mileage_view["vin"].isin(selected_vin_list))
        )

        mileage_view = mileage_view[[mode.lower(), "vin", "distance"]]
        mileage_view = mileage_view[condn].to_csv(index=False)

        st.download_button(
            label="Download Mileage as CSV",
            data=mileage_view,
            file_name="{} {} Mileage.csv".format(company_name, mode),
            mime="text/csv",
        )
    st_utils.break_lines(1)

    container.markdown("## Visualise Raw Data")
    with container.expander("Visualise Raw Data", expanded=True):
        st_utils.break_lines(1)
        selected_vin, date_start, date_end, button = st.columns([1, 1, 1, 1])
        button = button.button(label="Show Raw data")
        start = date_start.date_input(label="Select Start Date")
        end = date_end.date_input(label="Select End Date")
        vin = selected_vin.selectbox(label="Select VIN", options=vin_list)

        # copy of selected_files

        s3_files = copy.deepcopy(pipeline.data.file_description)
        vin_column = 0
        idx = s3_files[:, vin_column] == vin
        s3_files = s3_files[idx, :]

        # Get week and year to filter

        start_week = start.isocalendar().week
        start_year = start.isocalendar().year

        end_year = end.isocalendar().year
        end_week = end.isocalendar().week

        filtered_files = filter_s3_files(s3_files, start_year, start_week, end_year, end_week)

        if button:
            if filtered_files.shape[0] == 0:
                st.write("Invalid Date Range, no files found")
            else:
                vin_data = read_vin_data(
                    vin=vin, telematics_files_arr=filtered_files, tsp=tsp, files_config=FilesConfig(),
                )

                plot_gps = copy.deepcopy(vin_data.vin_df)
                plot_condn = (plot_gps.date.dt.date >= start_date) & (plot_gps.date.dt.date <= end_date)

                if plot_gps is not None:
                    fig = make_subplots(specs=[[{"secondary_y": True}]])
                    distance_plot = go.Scatter(
                        x=plot_gps[plot_condn]["date"].values,
                        y=plot_gps[plot_condn]["distance"].values,
                        name="Final Distance",
                        marker={"color": "DarkGreen"},
                    )
                    gps_plot = go.Scatter(
                        x=plot_gps[plot_condn]["date"].values,
                        y=plot_gps[plot_condn]["distance_gps"].values,
                        name="GPS based Distance",
                        marker={"color": "Blue"},
                    )
                    fig.add_trace(distance_plot, secondary_y=False)
                    fig.add_trace(gps_plot, secondary_y=False)
                    st.plotly_chart(fig, use_container_width=True)

    st_utils.break_lines(1)

    container.markdown("## Data Quality")
    with container.expander("Visualise Data quality", expanded=True):
        st_utils.break_lines(1)
        with container.form("select_data_quality"):
            data_quality_vin = container.multiselect("Base VIN Group [Default = All]", vin_list.tolist())
            data_quality_list = container.multiselect(
                "Data Quality Metrics [Default = None]", Constants.LIST_DATA_QUALITY_TYPES
            )
            start_date, end_date = container.select_slider(
                "Select timeframe for the map", options=list_of_days, value=(list_of_days[0], list_of_days[-1])
            )
            container.form_submit_button("Submit")
        st_utils.break_lines(1)

        if len(data_quality_vin) == 0:
            data_quality_vin = copy.deepcopy(vin_list)

        fleet_data_quality = prepare_fleet_data_quality(pipeline=pipeline, vin_subset=data_quality_vin)

        if "No Odometer" in data_quality_list:
            container.markdown("### Missing Odometer")
            vin_count_without_odo = fleet_data_quality.get("vin_count_without_odo")
            vin_list_without_odo = fleet_data_quality.get("vin_list_without_odo")
            labels = ["No Odometer Data", "Odometer Data"]
            values = [vin_count_without_odo, (len(data_quality_vin) - vin_count_without_odo)]
            fig = go.Figure(data=[go.Pie(labels=labels, values=values)])
            st.plotly_chart(fig, use_container_width=True)
            st.write("List of VINs without Odometer Data: {}".format(vin_list_without_odo))
        if "Invalid Odometer" in data_quality_list:
            container.markdown("### Invalid Odometer")
            summary_data = fleet_data_quality.get("invalid_odo")
            if summary_data is not None:
                condn = (summary_data["only_date"] >= start_date) & (summary_data["only_date"] <= end_date)
                summary_data = summary_data[condn]
                fig = px.histogram(summary_data, x="dq", color="vin")
                fig.update_layout(
                    title="Histogram of all Invalid Odometer days",
                    xaxis_title="Percentage Invalid Odometer",
                    yaxis_title="Invalid Odometer Count",
                    legend_title="VIN-day Count",
                )
                st.plotly_chart(fig, use_container_width=True)
                main_data = fleet_data_quality.get("invalid_odo")
                download_data = main_data.to_csv(index=False)
                st.download_button(
                    label="Download Invalid Odometer as CSV",
                    data=download_data,
                    file_name="{} {} Invalid Odometer.csv".format(company_name, mode),
                    mime="text/csv",
                )
            else:
                st.write("Unable to get invalid odometer data")

        if "Jumping Odometer" in data_quality_list:
            container.markdown("### Jumping Odometer")
            summary_data = fleet_data_quality.get("abnormal_jump_odo")
            if summary_data is not None:
                condn = (summary_data["only_date"] >= start_date) & (summary_data["only_date"] <= end_date)
                summary_data = summary_data[condn]
                fig = px.histogram(summary_data, x="dq", color="vin")
                fig.update_layout(
                    title="Histogram of all Jumping Odometer days",
                    xaxis_title="Percentage Jumping Odometer",
                    yaxis_title="Jumping Odometer Count",
                    legend_title="VIN-day Count",
                )

                st.plotly_chart(fig, use_container_width=True)

                main_data = fleet_data_quality.get("abnormal_jump_odo")
                download_data = main_data.to_csv(index=False)
                st.download_button(
                    label="Download Jumping Odometer as CSV",
                    data=download_data,
                    file_name="{} {} Jumping Odometer.csv".format(company_name, mode),
                    mime="text/csv",
                )
            else:
                st.write("Unable to get invalid odometer data")

        if "Jumping GPS" in data_quality_list:
            container.markdown("### Jumping GPS")
            summary_data = fleet_data_quality.get("abnormal_jump_gps")
            if summary_data is not None:
                condn = (summary_data["only_date"] >= start_date) & (summary_data["only_date"] <= end_date)
                summary_data = summary_data[condn]
                fig = px.histogram(summary_data, x="dq", color="vin")
                fig.update_layout(
                    title="Histogram of all Jumping GPS days",
                    xaxis_title="Percentage Jumping GPS",
                    yaxis_title="Jumping GPS Count",
                    legend_title="VIN-day Count",
                )
                st.plotly_chart(fig, use_container_width=True)
                main_data = fleet_data_quality.get("abnormal_jump_gps")
                download_data = main_data.to_csv(index=False)
                st.download_button(
                    label="Download Jumping GPS as CSV",
                    data=download_data,
                    file_name="{} {} Jumping GPS.csv".format(company_name, mode),
                    mime="text/csv",
                )
            else:
                st.write("Unable to get invalid odometer data")

        if "Large Data Gaps" in data_quality_list:
            container.markdown("### Large Data Gaps")
            fleet_percentage_data = copy.deepcopy(fleet_data_quality.get("large_data_gaps_fleet"))
            if fleet_percentage_data is not None:
                condn = (fleet_percentage_data["Time"].dt.date >= start_date) & (
                    fleet_percentage_data["Time"].dt.date <= end_date
                )
                fleet_percentage_data = fleet_percentage_data[condn]
                fleet_percentage_data = fleet_percentage_data[fleet_percentage_data["vin"].isin(data_quality_vin)]
                fleet_gap_summary = fleet_percentage_data.pivot_table(index=["Time"], values="gap", aggfunc=np.nansum)
                fleet_gap_list = fleet_percentage_data.pivot_table(index=["Time"], values="vin", aggfunc=series_to_list)
                fleet_gap_list = fleet_gap_list.reset_index()
                fleet_gap_summary = fleet_gap_summary.reset_index()
                fleet_gap_summary = pd.merge(fleet_gap_summary, fleet_gap_list, on="Time", how="left")
                fig = make_subplots(specs=[[{"secondary_y": True}]])
                text = ["VIN list {}".format(vins) for vins in fleet_gap_summary["vin"].values]
                fleet_gap_trend = go.Scatter(
                    x=fleet_gap_summary["Time"].values,
                    y=fleet_gap_summary["gap"].clip(0, 100).values,
                    connectgaps=False,
                    opacity=1,
                    hovertemplate="<b>%{text}</b>",
                    text=text,
                    mode="lines+markers",
                )
                fig.add_trace(
                    fleet_gap_trend,
                    secondary_y=False,
                )
                st.plotly_chart(fig, use_container_width=True)
            else:
                st.write("Unable to get Large Data Gaps")

        if "Percentage Data" in data_quality_list:
            container.markdown("### Percentage Data")
            fleet_percentage_data = copy.deepcopy(fleet_data_quality.get("fleet_percentage_data"))
            fleet_percentage_data = fleet_percentage_data[fleet_percentage_data["vin"].isin(data_quality_vin)]
            condn = (fleet_percentage_data["only_date"] >= start_date) & (
                fleet_percentage_data["only_date"] <= end_date
            )
            fleet_percentage_data = fleet_percentage_data[condn]
            if len(data_quality_vin) > 1:
                fleet_percentage_data = fleet_percentage_data[fleet_percentage_data["vin"].isin(data_quality_vin)]
                fleet_percent_data_summary = fleet_percentage_data.pivot_table(
                    index="vin",
                    values=["stationary_duration", "moving_duration", "stationary_data", "moving_data"],
                    aggfunc=np.nansum,
                )
                fleet_percent_data_summary = fleet_percent_data_summary.reset_index()
                fleet_percent_data_summary["moving_percentage_data"] = (
                    fleet_percent_data_summary["moving_data"] * 100 / fleet_percent_data_summary["moving_duration"]
                )
                fleet_percent_data_summary["stationary_percentage_data"] = (
                    fleet_percent_data_summary["stationary_data"]
                    * 100
                    / fleet_percent_data_summary["stationary_duration"]
                )

                fig = make_subplots(specs=[[{"secondary_y": True}]])

                motion_trend = go.Bar(
                    x=fleet_percent_data_summary["vin"].values,
                    y=fleet_percent_data_summary["moving_percentage_data"].clip(0, 100).values,
                    name="Moving Percentage Data",
                    opacity=0.5,
                    marker={"color": "deepskyblue"},
                )

                stationary_trend = go.Bar(
                    x=fleet_percent_data_summary["vin"].values,
                    y=fleet_percent_data_summary["stationary_percentage_data"].clip(0, 100).values,
                    name="Stationary Percentage Data",
                    opacity=0.5,
                    marker={"color": "red"},
                )

                fig.add_trace(
                    stationary_trend,
                    secondary_y=False,
                )
                fig.add_trace(
                    motion_trend,
                    secondary_y=True,
                )
            else:
                fleet_percent_data_summary = copy.deepcopy(fleet_percentage_data)
                fig = make_subplots(specs=[[{"secondary_y": True}]])

                motion_trend = go.Bar(
                    x=fleet_percent_data_summary["only_date"].values,
                    y=fleet_percent_data_summary["moving_percentage_data"].clip(0, 100).values,
                    name="Moving Percentage Data",
                    opacity=0.5,
                    marker={"color": "deepskyblue"},
                )

                stationary_trend = go.Bar(
                    x=fleet_percent_data_summary["only_date"].values,
                    y=fleet_percent_data_summary["stationary_percentage_data"].clip(0, 100).values,
                    name="Stationary Percentage Data",
                    opacity=0.5,
                    marker={"color": "red"},
                )

                fig.add_trace(
                    stationary_trend,
                    secondary_y=False,
                )
                fig.add_trace(
                    motion_trend,
                    secondary_y=True,
                )

            st.plotly_chart(fig, use_container_width=True)

    with st.spinner("Preparing VIN recommendation"):
        vin_table = pipeline.data.vin_table
        garaging_table = pipeline.data.garage_table
        headers = st.session_state.get("headers", {})
        equipment_list, error_equipment = api.get_equipment_list(
            app_review["summary"]["appReviewID"], _headers=headers
        )

        # TODO: shouldn't be doing this complex operation to get the equipment and telematics VIN
        vin_table, garaging_table, vin_csv, garage_csv = update_vin_garage_table(
            equipment_list=equipment_list, vin_table=vin_table, garaging_table=garaging_table
        )
        recommendations = recommend_vin_streamlit(vin_table)

        if len(recommendations) > 0:
            st.markdown("### VIN Recommendation")
            st.dataframe(recommendations, height=400, width=1000)
