import datetime

import requests
import streamlit as st

import telemetry.api.logging as logging
from boards import st_utils
from telemetry.api import trace
from utils.constants import Constants

log = logging.getLogger(__name__)


def get_app_review(app_review_id, headers=None):
    if app_review_id is None or app_review_id == "":
        raise ValueError("Invalid Review Id: {}".format(app_review_id))

    r = make_get_request(
        f"{Constants.API_SERVER_ENDPOINT}/underwriting/application_reviews/{app_review_id}", headers=headers
    )
    return handle_json_response(r)


def get_app_review_non_fleet(app_review_id: str, headers=None):
    """
    This function calls app review summary API for non-fleet and transform to output similar to get_app_review API
    for consistent consumption.

    This is a temporary solution. In the future we need to have a common API for fleet and non-fleet
    """
    if app_review_id is None or app_review_id == "":
        raise ValueError("Invalid Review Id: {}".format(app_review_id))

    r = make_get_request(
        f"{Constants.API_SERVER_ENDPOINT}/nonfleet/underwriting/application_reviews/{app_review_id}/summary",
        headers=headers
    )

    response, error = handle_json_response(r)

    if response is not None:
        if response.get("appReviewID") is None:
            response["appReviewID"] = app_review_id

        response["nonfleet"] = True

        response = {"summary": response}

    return response, error


def make_get_request(url, headers=None) -> requests.Response:
    if headers is None:
        headers = {}
    return requests.get(url, headers=headers)


def make_simple_request(url):
    return requests.get(url)


def handle_json_response(r: requests.Response):
    if r.status_code == 200:
        return r.json(), None
    elif r.status_code == 401:
        return None, "Unauthorized. Please login and try again."
    else:
        return None, f"[{r.status_code}] Failed to fetch data. Error={r.text}"


def get_all_application_reviews(headers=None):
    r = make_get_request(f"{Constants.API_SERVER_ENDPOINT}/underwriting/application_reviews", headers=headers)
    return handle_json_response(r)


def get_app_review_summary(app_review_id, headers=None):
    r = make_get_request(
        f"{Constants.API_SERVER_ENDPOINT}/underwriting/application_reviews/{app_review_id}/summary",
        headers=headers
    )
    return handle_json_response(r)


@trace.trace_method
@st_utils.spinner('Fetching projected information')
def get_projected_information(app_review_id, headers=None):
    if app_review_id is None or app_review_id == "":
        return None, "Invalid App Review id"
    url = f"{Constants.API_SERVER_ENDPOINT}/underwriting/application_reviews/{app_review_id}/operations/projected_information"
    r = make_get_request(url=url, headers=headers)
    return handle_json_response(r)


@trace.trace_method
@st.cache_data(ttl=datetime.timedelta(minutes=30), show_spinner='Fetching equipment list...')
def get_equipment_list(app_review_id, _headers=None, non_fleet=False):
    if app_review_id is None or app_review_id == "":
        return None, "Invalid App Review id"

    if non_fleet:
        url = f"{Constants.API_SERVER_ENDPOINT}/nonfleet/underwriting/application_reviews/{app_review_id}/equipments"
    else:
        url = f"{Constants.API_SERVER_ENDPOINT}/underwriting/application_reviews/{app_review_id}/equipments/units"

    r = make_get_request(url=url, headers=_headers)
    return handle_json_response(r)


@trace.trace_method
@st_utils.spinner('Fetching radius of operation')
def get_radius_of_operations(id, headers=None):
    if id is None or id == "":
        return None, "Invalid App Review id"
    url = f"{Constants.API_SERVER_ENDPOINT}/underwriting/application_reviews/{id}/operations/radius_of_operation"
    r = make_get_request(url=url, headers=headers)
    return handle_json_response(r)
