# Config class for Knowledge graph entities

from telematics_utils.tsp.tsps import SupportedTSPs
class KwgUtil:
    """
    Configuration utility class for knowledge graph entities.

    This class focuses on the sample frequency for various telematics service providers (TSPs).

    Attributes
    ----------
    get_sample_frequency : dict
        A dictionary mapping TSP names to their corresponding sample frequencies in seconds. 
        The keys are the names of the telematics service providers, and the values are the 
        sample frequencies (in seconds) at which data is collected or processed.
    """

    # TSP sample frequency
    get_sample_frequency = {
        
        # Sampling frequency: 10 seconds
        SupportedTSPs.LIGHTANDTRAVELELD: 10,
        SupportedTSPs.SAMSARA: 10,
        SupportedTSPs.KEEPTRUCKIN: 10,
        SupportedTSPs.GEOTAB: 10,
        SupportedTSPs.ZONAR: 10,
        SupportedTSPs.MASTERELD: 10,
        SupportedTSPs.GPSTAB: 10,
        SupportedTSPs.KSKELD: 10,
        SupportedTSPs.MOUNTAINELD: 10,
        SupportedTSPs.RIGHTTRUCKINGELD: 10,
        SupportedTSPs.ROADSTARELD: 10,
        SupportedTSPs.TFMELD: 10,
        SupportedTSPs.FMELD: 10,
        SupportedTSPs.EROAD: 10,
        SupportedTSPs.REALELD: 10,
        SupportedTSPs.PRORIDEELD: 10,
        SupportedTSPs.IDELD: 10,
        SupportedTSPs.EAGLEWIRELESS: 10,
        SupportedTSPs.MAXELD: 10,

        # Sampling frequency: 30 seconds
        SupportedTSPs.VERIZONCONNECT: 30,
        SupportedTSPs.VERIZONCONNECTFLEET: 30,
        SupportedTSPs.VERIZONCONNECTREVEAL: 30,
        SupportedTSPs.ONESTEPGPS: 30,
        SupportedTSPs.SMARTELDS: 30,
        SupportedTSPs.APEXULTIMA: 30,
        SupportedTSPs.BLUEHORSEELD: 30,
        SupportedTSPs.ELDBOOKS: 30,

        # Sampling frequency: 60 seconds
        SupportedTSPs.BIGROAD: 60,
        SupportedTSPs.OMNITRACSXRS: 60,
        SupportedTSPs.TTELD: 60,
        SupportedTSPs.AGILISISLINXUP: 60,
        SupportedTSPs.TRANSFLO: 60,
        SupportedTSPs.GOFLEET: 60,
        SupportedTSPs.GOGPS: 60,
        SupportedTSPs.ATANDTFLEET: 60,
        SupportedTSPs.FLEETCOMPLETE: 60,
        SupportedTSPs.ATANDTFLEETCOMPLETE: 60,
        SupportedTSPs.EVOELD: 60,
        SupportedTSPs.ONTIMEELD: 60,
        SupportedTSPs.ZIPPYELD: 60,
        SupportedTSPs.ORIENTELD: 60,

        # Sampling frequency: 300 seconds
        SupportedTSPs.OMNITRACSES: 60,
    }
